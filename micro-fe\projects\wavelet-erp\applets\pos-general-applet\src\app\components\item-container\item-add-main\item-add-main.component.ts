import { Component, ChangeDetectionStrategy, ViewChild, Output, EventEmitter, AfterViewInit } from '@angular/core';
import { MatTabGroup } from '@angular/material/tabs';
import { ComponentStore } from '@ngrx/component-store';
import { Store } from '@ngrx/store';
import { UUID } from 'angular2-uuid';
import { bl_fi_generic_doc_line_RowClass, bl_fi_generic_doc_link_RowClass, SubQueryService } from 'blg-akaun-ts-lib';
import moment from 'moment';
import { ToastrService } from 'ngx-toastr';
import { ClientSidePermissionsSelectors } from 'projects/shared-utilities/modules/permission/client-side-permissions-controller/selectors';
import { ClientSidePermissionStates } from 'projects/shared-utilities/modules/permission/client-side-permissions-controller/states';
import { ClientSideViewModel } from 'projects/shared-utilities/modules/permission/client-side-permissions-controller/states/client-side-permission.states';
import { PermissionStates } from 'projects/shared-utilities/modules/permission/permission-controller';
import { SessionSelectors } from 'projects/shared-utilities/modules/session/session-controller/selectors';
import { SessionStates } from 'projects/shared-utilities/modules/session/session-controller/states';
import { ViewColumnComponent } from 'projects/shared-utilities/view-column.component';
import { AppConfig } from 'projects/shared-utilities/visa';
import { map } from 'rxjs/operators';
import { SubSink } from 'subsink2';
import { ViewColumnFacadeItem } from '../../../facades/view-column-item.facade';
import { SubItemType } from '../../../models/sub-item-type-constants';
import { MainSelectors, PNSSelectors } from '../../../state-controllers/draft-controller/store/selectors';
import { DraftStates } from '../../../state-controllers/draft-controller/store/states';
import { PosActions } from '../../../state-controllers/pos-controller/store/actions';
import { PosSelectors } from '../../../state-controllers/pos-controller/store/selectors';
import { PosStates } from '../../../state-controllers/pos-controller/store/states';
import { ItemDetailsComponent } from '../../utilities/item-details/item-details.component';
import { MembershipPointsComponent } from '../../utilities/membership-points/membership-points.component';
import { MultiLevelDiscountComponent } from 'projects/shared-utilities/utilities/multi-level-discount/multi-level-discount.component';
import { AddLineItemSerialNumberComponent } from '../add-line-item-serial-number/add-line-item-serial-number.component';
import { LineItemBatchNumberComponent } from '../batch-number/batch-number.component';
import { LineItemBinNumberComponent } from '../bin-number/bin-number.component';
import { MTOComponent } from '../mto/mto.component';
import { POSDexieService } from '../../../services/pos-dexie.service';
import { CouponSerialNumberListingComponent } from '../coupon-serial-number-listing/coupon-serial-number-listing.component';
import * as fromDatatype from "../../../models/datatype-library/index";
import { ChildItemComponent } from '../child-item/child-item.component';
import { ColumnViewModelStates } from '../../../state-controllers/view-model-controller/states';
import { Column4ViewSelectors } from '../../../state-controllers/view-model-controller/selectors';
import { Column4ViewModelActions } from '../../../state-controllers/view-model-controller/actions';
import { UtilItemSerialNumberComponent } from 'projects/shared-utilities/utilities/serial-number/serial-number.component';
import { UtilitiesModulePos } from '../../utilities/utilities.module';
import { ViewColumnFacade } from '../../../facades/view-column.facade';
import { ApiService } from '../../../services/api-service';
import { combineLatest } from 'rxjs';
import { PosEditStates } from '../../../state-controllers/pos-edit-controller/store/states';
import { PosEditSelectors } from '../../../state-controllers/pos-edit-controller/store/selectors';
import { DepartmentComponent } from "../department/department.component";

interface LocalState {
  deactivateAdd: boolean;
  deactivateReturn: boolean;
  selectedIndex: number;
  deleteConfirmation: boolean;
  serialNumberSelectedIndex: number;
}

@Component({
  selector: 'app-item-add-main',
  templateUrl: './item-add-main.component.html',
  styleUrls: ['./item-add-main.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [ComponentStore]
})

export class ItemAddMainComponent extends ViewColumnComponent {
  protected subs = new SubSink();
  @Output() stockDetailsEmitter = new EventEmitter<any>();
  protected compName = 'Item Add Main';
  protected index = 4;
  protected localState: LocalState;
  apiVisa = AppConfig.apiVisa;
  localState$;
  readonly deactivateAdd$ = this.componentStore.select(state => state.deactivateAdd);
  readonly deactivateReturn$ = this.componentStore.select(state => state.deactivateReturn);
  readonly deleteConfirmation$ = this.componentStore.select(state => state.deleteConfirmation);
  readonly selectedIndex$ = this.componentStore.select(state => state.selectedIndex);
  readonly serialNumberSelectedIndex$ = this.componentStore.select(state => state.serialNumberSelectedIndex);
  toggleColumn$;
  isEdit = false;
  prevIndex: number;
  protected prevLocalState: any;
  main$ = this.draftStore.select(MainSelectors.selectMain);
  line$ = this.store.select(PosSelectors.selectLineItem);
  item$ = this.store.select(PosSelectors.selectItem);
  cashbill$ = this.store.select(PosSelectors.selectEntity);
  pricing$ = this.store.select(PosSelectors.getPricingSchemeLinks);
  pns$ = this.draftStore.select(PNSSelectors.selectAll).pipe(
    map(a => a.filter(l => l.status !== 'DELETED'))
  );
  itemSold$ = this.store.select(PosSelectors.selectItemSold);
  lineItem: bl_fi_generic_doc_line_RowClass;
  subItemType;
  txnType;
  SUB_ITEM_TYPE = SubItemType;
  taxApplicable;
  hideUOM = true;
  uom;
  taxType;
  pricingHdrGuid;
  isReturn;
  isTradeIn = false;
  addText = "Add Item";
  roundingItemCode;
  eanCode;
  employeeDiscount;
  isMember = false;
  selectedPricingScheme;
  checkQuota = false;
  custGuid;
  spendingLimit;
  itemSold;
  canEditPricing = true;
  pns: bl_fi_generic_doc_line_RowClass[];
  @ViewChild(MatTabGroup) matTab: MatTabGroup;
  @ViewChild(ChildItemComponent) child: ChildItemComponent;
  @ViewChild(ItemDetailsComponent) itemDetails: ItemDetailsComponent;
  @ViewChild(UtilItemSerialNumberComponent) serial: UtilItemSerialNumberComponent;
  @ViewChild(LineItemBatchNumberComponent) batchNumber: LineItemBatchNumberComponent;
  @ViewChild(LineItemBinNumberComponent) binNumber: LineItemBinNumberComponent;
  @ViewChild(MultiLevelDiscountComponent) multiLevelDiscount: MultiLevelDiscountComponent;
  @ViewChild(MTOComponent) mto: MTOComponent;
  @ViewChild(CouponSerialNumberListingComponent, { static: false }) couponSerial: CouponSerialNumberListingComponent;
  @ViewChild(MembershipPointsComponent) membershipPoints: MembershipPointsComponent;
  @ViewChild(DepartmentComponent) dept: DepartmentComponent;
  private userPermissions: ClientSideViewModel[] = [];
  showSerialFromQueue: boolean = false;
  queueDocList = []
  POS_ITEM_DISCOUNT: boolean;
  clientSidePermissions$ = this.clientPermissionStore.select(
    ClientSidePermissionsSelectors.selectAll
  );
  clientSidePermissionSettings: any;
  masterSettings$ = this.sessionStore.select(SessionSelectors.selectMasterSettings);
  itemDetailsForm;
  couponHdrGuid;
  couponTxnType;
  coupon$ = this.store.select(PosSelectors.selectCoupon);
  allowAnonymous: any;
  qtyType: string;
  couponSerialLoaded: boolean;
  coupon;
  myImage;
  pricingLinks;
  mtoStatus;
  childItemList$ = this.store.select(PosSelectors.selectChildItemList);
  childItemList = [];
  mtoPricingLogic;

  inv_item_hdr_guid;
  postingStatus;
  ColumnViewSelectors = Column4ViewSelectors;
  ColumnViewActions = Column4ViewModelActions;
  serialNumberListing$ = this.viewModelStore.select(Column4ViewSelectors.selectSerialNumberTab_ScanTab_SerialNumbersListing);
  showStockDetails = false;
  showSalesHistory = false;
  serverDocType = "INTERNAL_SALES_CASHBILL";
  location;
  readonly invItem$ = this.store.select(PosSelectors.selectInvItem);
  serialNumberTabColor$=this.viewModelStore.select(Column4ViewSelectors.selectSerialNumberTab_Color);
  stockBalance;
  defaultPricingList = [];
  mode$ = this.store.select(PosSelectors.selectModeItem);
  mode;
  itemHdrGuid;
  button$ = this.store.select(PosSelectors.selectBackButton);
  serialTxnType = "SALES";
  appletSettings;
  tradeInBasicItem = false;
  posTheme ="LEFT_RIGHT";
  viewColFacade;
  menu;
  eInvoiceEnabled = false;
  einvoice_item_classification_desc;
  einvoice_item_classification_code;
  glcodeGuid;
  offlineMode = false;
  itemCount=0;
  departmentTabInvalid: boolean = false;
  category1_code;
  category2_code;
  category3_code;
  category4_code;
  category5_code;
  category6_code;
  category7_code;
  category8_code;
  category9_code;
  category10_code;
  constructor(
    protected readonly posEditStore: Store<PosEditStates>,
    private apiService: ApiService,
    private viewColFacadeMain: ViewColumnFacade,
    private readonly viewModelStore: Store<ColumnViewModelStates>,
    private toastr: ToastrService,
    protected posDexieService: POSDexieService,
    private readonly permissionStore: Store<PermissionStates>,
    private readonly draftStore: Store<DraftStates>,
    private readonly store: Store<PosStates>,
    protected viewColFacadeItem: ViewColumnFacadeItem,
    protected readonly componentStore: ComponentStore<LocalState>,
    private readonly sessionStore: Store<SessionStates>,
    protected readonly clientPermissionStore: Store<ClientSidePermissionStates>
  ) {
    super();
    this.viewColFacade = this.viewColFacadeItem;
    this.toggleColumn$ = this.viewColFacade.toggleColumn$;

    this.subs.sink = this.sessionStore.select(SessionSelectors.selectMasterSettings).subscribe({next: (resolve: any) => {
      this.posTheme = resolve?.POS_THEME?resolve.POS_THEME:"LEFT_RIGHT";
      if(this.posTheme==='TOP_BOTTOM'){
        this.viewColFacade = this.viewColFacadeMain;
        this.localState$ = this.viewColFacade.selectLocalState(31);
      }else{
        this.viewColFacade = this.viewColFacadeItem;
        this.localState$ = this.viewColFacade.selectLocalState(this.index);
      }
     }});
    this.viewColFacade.selectLocalState(this.index);
  }

  ngOnInit() {
    this.subs.sink = this.mode$.subscribe({ next: (resolve: string) => this.mode = resolve });
    this.subs.sink = this.button$.subscribe({ next: (resolve: string) => this.myImage = resolve });

    this.subs.sink = this.draftStore.select(PNSSelectors.selectItemCount).subscribe(c=>{
      this.itemCount = c;
    })
    this.store.dispatch(PosActions.resetLineItem());
    this.subs.sink = this.clientSidePermissions$.subscribe({
      next: (resolve) => {
        this.clientSidePermissionSettings = resolve;
        resolve.forEach(permission => {
          if (permission.perm_code === "POS_ITEM_DISCOUNT") {
            this.POS_ITEM_DISCOUNT = true;
          }
          if (permission.perm_code === "POS_SHOW_STOCK_DETAILS") {
            this.showStockDetails = true;
          }
          if (permission.perm_code === "POS_SHOW_SALES_HISTORY") {
            this.showSalesHistory = true;
          }
        })
      }
    });
    this.subs.sink = this.viewColFacade.prevIndex$.subscribe(resolve => this.prevIndex = resolve);
    this.subs.sink = this.viewColFacade.prevLocalState$().subscribe(resolve => this.prevLocalState = resolve);
    this.subs.sink = this.masterSettings$.subscribe(master => {
      this.showSerialFromQueue = master?.SERIAL_OPEN_QUEUE ? true : false;
      this.appletSettings = master;
      this.tradeInBasicItem =  master?.TRADE_IN_BASIC_ITEM;
      this.posTheme = master?.POS_THEME?master.POS_THEME:"LEFT_RIGHT";
    })
    this.subs.sink = this.localState$.subscribe(a => {
      this.localState = a;
      this.stockBalance = a.stockBalance;
      this.employeeDiscount = a.employeeDiscount;
      this.eanCode = a.eanCode;
      this.subItemType = a.subItemType;
      this.txnType = a.txnType;
      this.taxApplicable = a.taxApplicable;
      this.pricingHdrGuid = a.pricingHdrGuid;
      this.pricingLinks = a.pricingLinks;
      this.isReturn = a.isReturn;
      this.roundingItemCode = a.roundingItemCode;
      this.isMember = a.isMember;
      this.isTradeIn = a.isTradeIn;
      this.itemHdrGuid = a.itemHdrGuid;
      this.category1_code = a.category1_code;
      this.category2_code = a.category2_code;
      this.category3_code = a.category3_code;
      this.category4_code = a.category4_code;
      this.category5_code = a.category5_code;
      this.category6_code = a.category6_code;
      this.category7_code = a.category7_code;
      this.category8_code = a.category8_code;
      this.category9_code = a.category9_code;
      this.category10_code = a.category10_code;

      this.defaultPricingList= a.defaultPricingList?a.defaultPricingList:[];
      this.serialTxnType =  this.isReturn || this.isTradeIn?"PURCHASE":"SALES";
      if(this.defaultPricingList.length){
        const newList = this.defaultPricingList
        .filter(obj1 => this.pricingLinks
          .some(obj2 => obj1.bl_fi_mst_branch_pricing_scheme_hdr_link.guid_pricing_scheme_hdr.toString()
            === obj2.bl_fi_mst_pricing_scheme_link.guid_pricing_scheme_hdr.toString())
            );
        if(newList.length>0){
          const highestLevel = UtilitiesModulePos.findHighestLevel(newList);
          const pricing =  this.pricingLinks.find(item =>  item.bl_fi_mst_pricing_scheme_link.guid_pricing_scheme_hdr===highestLevel.bl_fi_mst_branch_pricing_scheme_hdr_link.guid_pricing_scheme_hdr && item.bl_fi_mst_pricing_scheme_link.sales_unit_price>0);
          if(pricing){
            this.pricingHdrGuid = pricing.bl_fi_mst_pricing_scheme_link.guid_pricing_scheme_hdr
          }
        }
      }

      if (this.isReturn) {
        this.addText = "Add Return Item";
      }
      if (this.isTradeIn && this.txnType==="NSTI" ||  (this.tradeInBasicItem && this.isTradeIn && this.txnType==="BASIC_ITEM") ) {
        this.addText = "Add Trade-In Item";
      }
      this.componentStore.setState(a);
    });
    let hasPermEdit = false;
    this.subs.sink = this.permissionStore.select(ClientSidePermissionsSelectors.selectAll).subscribe({
      next: resolve => {
        hasPermEdit = resolve.find(perm => perm.perm_code.toUpperCase() === 'POS_EDIT_PRICING_STAFF_PURCHASE') ? true : false;
      }
    })
    this.subs.sink = this.line$.subscribe({ next: resolve => this.lineItem = resolve });
    this.subs.sink = this.item$.subscribe({
      next: (item:any) => {
        if(item.bl_fi_mst_item_hdr.txn_type==='GL_CODE'){
          this.glcodeGuid = item.bl_fi_mst_item_hdr.glcode_guid?item.bl_fi_mst_item_hdr.glcode_guid: item.bl_fi_mst_item_hdr.guid_glcode_company_link;
        }

        this.einvoice_item_classification_code = item.bl_fi_mst_item_hdr.einvoice_item_classification_code;
        this.einvoice_item_classification_desc = item.bl_fi_mst_item_hdr.einvoice_item_classification_desc;
        this.inv_item_hdr_guid = item.bl_fi_mst_item_hdr.inv_item_hdr_guid;
        this.couponHdrGuid = item.bl_fi_mst_item_hdr.coupon_hdr_guid;
        this.taxType = item.bl_fi_mst_item_hdr.output_tax_type;
        let multiUOM: any = [];
        if (item.bl_fi_mst_item_lines?.length > 0) {
          multiUOM = item.bl_fi_mst_item_lines.filter(u => u.uom !== null);

        }
        if (!this.uom) {
          this.uom = item.bl_fi_mst_item_hdr.uom;
        }
        if (multiUOM.length == 0) {
          this.hideUOM = true;
        } else {
          this.hideUOM = false;
        }
        this.mtoPricingLogic = item.bl_fi_mst_item_hdr.pricing_logic;
      }
    });
    this.subs.sink = this.store.select(PosSelectors.selectSpendingLimit).subscribe(a => {
      //console.log('sp',a)
      this.spendingLimit = a;
      const is_enabled = a?.bl_fi_mst_entity_spending_limit_line?.is_enabled;
      const maxQtyPerItem = a?.bl_fi_mst_entity_spending_limit_line?.property_json?.maxQtyPerItem ?? 1;

      if (is_enabled && maxQtyPerItem <= 1) {
        this.checkQuota = true;
      } else {
        this.checkQuota = false;
      }
    });
    this.subs.sink = this.itemSold$.subscribe(i => {
      this.itemSold = i;
    });
    this.subs.sink = this.main$.subscribe(main => {
      this.custGuid = main.customer?.bl_fi_mst_entity_hdr?.guid;
      this.location = main.locationGuid;
    })
    this.subs.sink = this.pns$.subscribe(p => {
      this.pns = p;
    })
    this.selectedPricingScheme = this.pricingHdrGuid;
    //console.log('hasPermEdit',hasPermEdit)
    // console.log('checkQuota',this.checkQuota)
    if (this.checkQuota) {
      this.canEditPricing = hasPermEdit;
    }

    this.subs.sink = this.coupon$.subscribe((c: any) => {
      if (c) {
        this.allowAnonymous = c.bl_fi_mst_coupon_hdr.allow_anonymous;
        this.qtyType = c.bl_fi_mst_coupon_hdr.quantity_type;
        this.coupon = c;
        //console.log("this.coupon",this.coupon)
      }

    });
    this.subs.sink = this.store.select(PosSelectors.selectMTOStatus).subscribe(s => {
      this.mtoStatus = s;
    });

    this.subs.sink = combineLatest(
      [
        this.store.select(PosSelectors.selectMenu),
        this.store.select(PosSelectors.selectEInvoiceEnabled),
        this.posEditStore.select(PosEditSelectors.selectEInvoiceEnabled),
        this.sessionStore.select(SessionSelectors.selectPersonalSettings)
      ]).pipe(
      ).subscribe({next: ([
        menu,
        eInvoiceEnabledCreate,
        eInvoiceEnabledEdit,
        genDocCreate,
        genDocEdit,
        personal
      ]:any) => {
        this.menu = menu;
        if(this.menu ==="edit"){
          this.eInvoiceEnabled = eInvoiceEnabledEdit;
        }else{
          this.eInvoiceEnabled = eInvoiceEnabledCreate;
        }
        this.offlineMode = personal?.OFFLINE_MODE;
        if(!personal){
          this.offlineMode = true;
        }
      }})
  }

  canGiveItemDiscount(){
    let canItemDiscount = true;
    if(this.txnType !=='MADE_TO_ORDER' && this.txnType !== 'COUPON' && this.txnType !== 'NSTI'){
      if(this.appletSettings?.HIDE_DISCOUNT){
        canItemDiscount = false;
        if(this.POS_ITEM_DISCOUNT){
          canItemDiscount = true;
        }else{
          canItemDiscount = false;
        }
      }
    }else{
      canItemDiscount = false;
    }

    return canItemDiscount;
  }

  onAdd() {
    let canAdd = true;
    if (this.checkQuota) {
      const maxQty = this.spendingLimit.bl_fi_mst_entity_spending_limit_line.qty_quota;
      const limitQtyUsage = this.spendingLimit.bl_fi_mst_entity_spending_limit_line.qty_usage;
      let qtyUsage = 1 + Number(limitQtyUsage) + Number(this.pns.length);

      //console.log('itemguid',this.itemDetails?.form?.value.itemGuid)
      if (maxQty != 0 && qtyUsage > maxQty) {
        canAdd = false;
        this.toastr.error(
          'Customer account has reached the maximum quantity quota for this month',
          'Error',
          {
            tapToDismiss: true,
            progressBar: true,
            timeOut: 1300
          }
        );
      }
      else if (this.itemSold && this.itemSold.includes(this.itemDetails?.form?.value.itemGuid)) {
        canAdd = false;
        this.toastr.error(
          'Customer account has reached the maximum quantity quota for this item',
          'Error',
          {
            tapToDismiss: true,
            progressBar: true,
            timeOut: 1300
          }
        );
      }
      if (this.pns) {
        this.pns.forEach((l: any) => {
          let itemGuid = l.item_guid;

          if (itemGuid && itemGuid.includes(this.itemDetails?.form?.value.itemGuid)) {
            canAdd = false;
            this.toastr.error(
              'Customer account has reached the maximum quantity quota for this item',
              'Error',
              {
                tapToDismiss: true,
                progressBar: true,
                timeOut: 1300
              }
            );
          }
        })
      }
    }
    if (canAdd) {
      const line = new bl_fi_generic_doc_line_RowClass();
      line.position_id = this.generateZeroPaddedID(this.itemCount);
      line.guid = UUID.UUID().toLowerCase();
      if (this.serial?.serialNumbers.length > 0) {
        line.serial_no = this.subItemType === this.SUB_ITEM_TYPE.serialNumber ? <any>this.serial.serialNumbers  : null;
        line.quantity_base = this.serial.serialNumbers.length;
        this.itemDetails?.form?.patchValue({
          qty: this.serial.serialNumbers.length
        })
      }
      else if (this.couponSerial?.serialNumbers.length > 0) {
        line.serial_no = <any>  {
          serialNumbers: this.couponSerial.serialNumbers
        };
        line.quantity_base = this.couponSerial.serialNumbers.length;
      } else if (this.binNumber?.binNumbers.length > 0) {
        let total = this.binNumber?.binNumbers.map(bin => bin.qty).reduce((prev, next) => prev + next);
        line.quantity_base = total;
        this.itemDetails?.form?.patchValue({
          qty: total
        })
      } else {
        line.quantity_base = this.itemDetails?.form?.value.qty;
      }

      this.itemDetails.onCalculateFromUnitPriceStd();
      line.item_guid = this.itemDetails?.form?.value.itemGuid;

      line.item_code = this.itemDetails?.form?.value.itemCode;
      line.item_name = this.itemDetails?.form?.value.itemName;
      line.amount_std = this.itemDetails?.form?.value.stdAmt;
      line.tax_gst_code = this.itemDetails?.form?.value.taxCode;
      line.tax_gst_rate = this.itemDetails?.form?.value.taxPercent;
      line.tax_gst_type = this.itemDetails?.form?.value.gstTaxType;
      line.amount_txn = this.itemDetails?.form?.value.txnAmt;
      line.amount_net = this.itemDetails?.form?.value.netAmt1;
      line.amount_discount = this.itemDetails?.form?.value.discountAmt;
      line.amount_tax_gst = this.itemDetails?.form?.value.taxAmt;
      line.tax_wht_code = this.itemDetails?.form.value.whtCode;
      line.tax_wht_rate = this.itemDetails?.form.value.whtPercent;
      line.tax_wht_type = this.itemDetails?.form?.value.whtTaxType;
      line.amount_tax_wht = this.itemDetails?.form?.value.whtAmt;
      line.txn_type = 'PNS';
      line.status = 'ACTIVE';
      if (this.isReturn) {
        line.quantity_signum = 1;
        line.amount_signum = -1;
        line.server_doc_type = 'INTERNAL_SALES_RETURN';
        line.client_doc_type = 'INTERNAL_SALES_RETURN';
      } else {
        line.quantity_signum = -1;
        line.amount_signum = 1;
        line.server_doc_type = 'INTERNAL_SALES_CASHBILL';
        line.client_doc_type = 'INTERNAL_SALES_CASHBILL';
      }
      line.date_txn = new Date();
      line.item_remarks = this.itemDetails?.form?.value.remarks;
      line.item_property_json = <any>{
        hideUOM: this.hideUOM,
        sub_item_type: this.subItemType,
        taxApplicable: this.taxApplicable,
        taxType: this.taxType,
        //pricingScheme: this.itemDetails?.form.value.pricingScheme,
        unitDiscount: this.itemDetails?.form.value.unitDiscount,
        netAmt2: this.itemDetails?.form.value.netAmt2,
        uomGuid: this.itemDetails?.form.value.uomGuid,
        eanCode: this.eanCode,
        salesPrompt: this.itemDetails.salesPrompt,
        //inv_item_hdr_guid: this.itemDetails?.form.value.item_container?.bl_fi_mst_item_hdr?.inv_item_hdr_guid
      };
      line.discount_description = this.itemDetails.salesPrompt;
      console.log('this.itemDetails',this.itemDetails.form.value);
      if(this.itemDetails.form.value.pricingScheme?.price_amount_incl_tax===this.itemDetails.form.value.unitPriceStdWithTax){
        line.pricing_scheme_link_guid = this.itemDetails?.form.value.pricingScheme?.guid_pricing_scheme_link;
      }

      line.replacement_unit_price_excl_tax = this.itemDetails?.form.value.pricingScheme?.replacement_unit_price_excl_tax;
      line.rebate_price1_excl_tax = this.itemDetails?.form?.value.pricingScheme?.rebate_price1_excl_tax;
      line.rebate_price2_excl_tax = this.itemDetails?.form?.value.pricingScheme?.rebate_price2_excl_tax;
      line.rebate_price3_excl_tax = this.itemDetails?.form?.value.pricingScheme?.rebate_price3_excl_tax;
      line.ref_price1_excl_tax = this.itemDetails?.form?.value.pricingScheme?.ref_price1_excl_tax;
      line.ref_price2_excl_tax = this.itemDetails?.form?.value.pricingScheme?.ref_price2_excl_tax;
      line.ref_price3_excl_tax = this.itemDetails?.form?.value.pricingScheme?.ref_price3_excl_tax;
      line.delta_price1_excl_tax = this.itemDetails?.form?.value.pricingScheme?.delta_price1_excl_tax;
      line.delta_price2_excl_tax = this.itemDetails?.form?.value.pricingScheme?.delta_price2_excl_tax;
      line.delta_price3_excl_tax = this.itemDetails?.form?.value.pricingScheme?.delta_price3_excl_tax;
      line.cost_replacement_price_company = this.itemDetails?.form?.value.pricingScheme?.replacement_unit_price_excl_tax;
      line.cost_replacement_amount_company = this.itemDetails?.form?.value.pricingScheme?.replacement_unit_price_excl_tax
       ? Number(line.quantity_base) * Number(this.itemDetails?.form?.value.pricingScheme?.replacement_unit_price_excl_tax)
       : 0;


      line.unit_price_net = this.itemDetails?.form?.value.unitPriceNet;
      line.unit_price_std = this.itemDetails?.form?.value.unitPriceStd;
      line.unit_price_std_with_tax = this.itemDetails?.form?.value.unitPriceStdWithTax;
      line.unit_price_txn = this.itemDetails?.form?.value.unitPriceTxn;
      line.unit_price_std_by_uom = this.itemDetails?.form?.value.unitPriceStdUom;
      line.unit_price_txn_by_uom = this.itemDetails?.form?.value.unitPriceUom;
      line.unit_disc_by_uom = this.itemDetails?.form?.value.unitDiscountUom;
      line.unit_discount = this.itemDetails?.form?.value.unitDiscount;
      line.uom = this.itemDetails?.form?.value.uom;
      line.uom_to_base_ratio = this.itemDetails?.form?.value.uomBaseRatio;
      line.qty_by_uom = parseInt(this.itemDetails?.form?.value.qtyUom);
      line.batch_no = this.subItemType === this.SUB_ITEM_TYPE.batchNumber ? <any>{ batches: this.batchNumber.batchNumbers } : null;
      line.bin_no = this.subItemType === this.SUB_ITEM_TYPE.binNumber ? <any>{ bins: this.binNumber.binNumbers } : null;
      line.sales_entity_hdr_guid =  this.itemDetails?.form?.value.salesAgentGuid;
      line.sales_entity_hdr_name =  this.itemDetails?.form?.value.salesAgentName;
      line.sales_entity_hdr_code =  this.itemDetails?.form?.value.salesAgentCode;

      if (this.multiLevelDiscount) {
        line.multi_level_disc_json = <any>{
          discount: this.multiLevelDiscount.rowData
        };
      }


      line.item_txn_type = this.itemDetails?.form?.value.txnType;
      line.item_sub_type = this.subItemType;
      if (this.membershipPoints) {
        line.point_start_date = this.membershipPoints.form.value.valid_date_from ? this.membershipPoints.form.value.valid_date_from : null;
        line.point_end_date = this.membershipPoints.form.value.valid_date_to ? this.membershipPoints.form.value.valid_date_to : null;
        line.point_currency = this.membershipPoints.form.value.point_ccy ? this.membershipPoints.form.value.point_ccy : null;
        line.point_amount = this.membershipPoints.form.value.point_amt ? this.membershipPoints.form.value.point_amt : null;
        line.point_type = this.membershipPoints.form.value.point_amt ? this.membershipPoints.form.value.point_type : null;
        line.item_property_json = <any>{
         ...line.item_property_json,
         salesPrompt: this.membershipPoints.form.value.sales_prompt ? this.membershipPoints.form.value.sales_prompt : null
        };

      }


      if (this.showSerialFromQueue) {
        this.queueDocList.forEach(q => {
          const link = new bl_fi_generic_doc_link_RowClass();
          link.guid_doc_1_hdr = q.hdr;
          link.guid_doc_1_line = q.line;
          link.guid_doc_2_line = line.guid;
          link.server_doc_type_doc_1_hdr = 'INTERNAL_PURCHASE_GOODS_RECEIVED_NOTE';
          link.server_doc_type_doc_1_line = 'INTERNAL_PURCHASE_GOODS_RECEIVED_NOTE';
          link.server_doc_type_doc_2_hdr = 'INTERNAL_SALES_CASHBILL';
          link.server_doc_type_doc_2_line = 'INTERNAL_SALES_CASHBILL';
          link.txn_type = 'KO';
          link.quantity_signum = -1;
          link.quantity_contra = q.qty;
          link.date_txn = new Date();
          // console.log('q.serial',q.serial)
          let serialJson = {
            serialNumbers: q.serial
          }

          link.serial_no = <any>serialJson;
          //  console.log('link', link);
          this.viewColFacade.addLink(link);
        })

      }
      if (this.mto) {
        line.item_property_json = <any>{
          "mtoStatus": this.mtoStatus,
          "pricingLogic":this.mtoPricingLogic
        }

        this.store.select(PosSelectors.selectMTOmasterList).subscribe(mto => {
          line.sort_code = UUID.UUID().toLowerCase();
          mto.forEach((mtoItem) => {
            mtoItem.component.forEach((componentItem) => {
              componentItem.doclines.forEach((doclineItem) => {
                if (this.isReturn) {
                  doclineItem.quantity_signum = 1;
                  doclineItem.amount_signum = -1;
                  doclineItem.server_doc_type = 'INTERNAL_SALES_RETURN';
                  doclineItem.client_doc_type = 'INTERNAL_SALES_RETURN';
                }
              })
            })
          })

          const mtoJson = {
            "mto": mto
          };

          line.item_child_json = <any>mtoJson;
        })
      }
      line.tax_tariff_code = this.itemDetails?.form.value.tariffCode;
      if (line.item_txn_type === 'COUPON') {
        //console.log('this.couponSerial.couponLines', this.couponSerial.couponLines);
        //console.log('this.itemDetails?.form.value',this.itemDetails)
        line.point_type = this.itemDetails?.form.value.couponTxnType;
        line.coupon_hdr_guid = this.coupon?.bl_fi_mst_coupon_hdr.guid;
        console.log('this.couponSerial',this.couponSerial);
        if (this.couponSerial?.couponLines?.length === 1) {
          line.coupon_line_guid = <any>this.couponSerial.couponLines[0];
        } else if (this.couponSerial?.couponLines?.length > 1) {

          const jsonResult = { coupon_line_guids: this.couponSerial.couponLines };
          //const jsonString = JSON.stringify(jsonResult);
          line.coupon_line_guid_json = <any>jsonResult;
        }
      }
      if (line.item_txn_type === 'BUNDLE') {
        this.viewModelStore.select(Column4ViewSelectors.selectItemChildJson).subscribe(json=>{
          line.item_child_json = json;
        })
      }
      if (line.item_txn_type === 'NSTI' || (this.tradeInBasicItem  && line.item_txn_type==="BASIC_ITEM")) {
          if(this.isTradeIn){
            line.quantity_signum = 1;
            line.amount_signum = -1;
            line.server_doc_type = 'INTERNAL_PURCHASE_TRADE_IN';
            line.client_doc_type = 'INTERNAL_PURCHASE_TRADE_IN';
          }else{
            line.nsti_stock_hdr_guid = this.itemDetails.form.value.nsti;
          }
          if(line.item_txn_type === 'NSTI'){
            const arrSN: any[] = [];
            if (this.itemDetails.form.value.tradeInSerial) {
              arrSN.push(this.itemDetails.form.value.tradeInSerial)
            }
            line.serial_no = <any>{ serialNumbers: arrSN };
          }
      }

      if (line.item_txn_type === 'WARRANTY'){
        const arrSN: any[] = [];
        if (this.itemDetails.form.value.warrantySerial) {
          arrSN.push(this.itemDetails.form.value.warrantySerial)
        }
        line.serial_no = <any>{ serialNumbers: arrSN };
      }
      //console.log('line', line)
      line.einvoice_taxable_type_code = this.itemDetails.form.value?.einvoiceTaxTypeCode;
      line.einvoice_taxable_type_desc = this.itemDetails.form.value?.einvoiceTaxTypeDesc;
      line.einvoice_uom = this.itemDetails.form.value?.einvoice_uom;
      line.einvoice_uom_desc = this.itemDetails.form.value?.einvoice_uom_desc;
      line.einvoice_item_classification_code = this.einvoice_item_classification_code;
      line.einvoice_item_classification_desc = this.einvoice_item_classification_desc;
      line.status = 'ACTIVE';
      if (line.item_txn_type === 'GL_CODE'){
        line.guid_glcode = this.glcodeGuid;
      }
      //console.log('add line', line)
      if(this.dept){
        line.guid_dimension = this.dept.form.value.dimension?.guid;
        line.guid_profit_center = this.dept.form.value.profitCenter?.guid;
        line.guid_project = this.dept.form.value.project?.guid;
        line.guid_segment = this.dept.form.value.segment?.guid;
        line.segment_code = this.dept?.form.value.segment?.code;
        line.gl_dimension_code = this.dept?.form.value.dimension?.code;
        line.profit_center_code = this.dept?.form.value.profitCenter?.code;
        line.project_code = this.dept?.form.value.project?.code;
      }

      this.posDexieService.addRow(line);
      if (this.mtoPricingLogic === 'CHILD'){
        this.store.dispatch(PosActions.recalculateMTODiscount({line:line}));
      }
      line.item_property_json = <any> {
      ...line.item_property_json,
      category_1: this.category1_code,
      category_2: this.category2_code,
      category_3: this.category3_code,
      category_4: this.category4_code,
      category_5: this.category5_code,
      category_6: this.category6_code,
      category_7: this.category7_code,
      category_8: this.category8_code,
      category_9: this.category9_code,
      category_10: this.category10_code,
    }
      if(this.posTheme==='TOP_BOTTOM'){
        UtilitiesModulePos.addLineItem(
          line,
          false,
          this.store,
          this.draftStore,
          this.apiService,
          this.menu
        );
        this.viewColFacadeMain.resetIndex(29);
      }else{
        this.viewColFacade.addLineItem(line);
        this.viewColFacade.resetIndex(0);
      }

    }

  }

  generateZeroPaddedID(index, length = 4) {
    return String(index + 1).padStart(length, '0');
  }



  addLineItem(line: bl_fi_generic_doc_line_RowClass) {
    // console.log('line',line)
    this.viewColFacade.addLineItem(line);
    this.viewColFacade.resetIndex(0);
  }


  disableAdd() {
    let result = this.itemDetails?.form?.invalid;
    if (this.txnType === 'MADE_TO_ORDER') {
      if (this.mtoStatus === "FULFILLED") {
        result = false;
      } else {
        result = true;
      }
    }
    if (this.txnType === 'COUPON') {
      if (this.qtyType === 'FIXED' && !this.couponSerial?.serialNumbers?.length) {
        result = true;
      }
    }
    // console.log("disableAdd",result)
    return result;
  }

  onReturn() {
    if(this.posTheme==='TOP_BOTTOM'){
      this.viewColFacadeMain.onPrev(0);
    }else{
      this.viewColFacade.onPrev(0);
    }
  }

  goToBinListing() {
    this.viewColFacade.onNextAndReset(4, 6);
  }

  goToBatchListing() {
    this.viewColFacade.onNextAndReset(4, 5);
  }


  onDelete() {
    const line = { ...this.lineItem, status: 'DELETED' };
    //this.viewColFacade.editLineItem(line);
  }

  ngOnDestroy() {
    if (this.matTab) {
      this.viewColFacade.updateInstance(this.index, {
        ...this.localState,
        selectedIndex: this.matTab.selectedIndex,
      });
    }
    this.subs.unsubscribe();
  }

  addSerialNumberFromScan(e: any) {
    //console.log('this.queueDocList',this.queueDocList)
    //console.log('e',e)
    if (e && !this.serial.serialNumbers.includes(e.sn_id)) {
      this.serial.serialNumbers.push(e.sn_id);
      //this.serial.listing.createData();
      if (this.showSerialFromQueue && e.queue_guid_doc_1_hdr) {
        let existingQueueDoc = this.queueDocList.find(ele => ele.hdr === e.queue_guid_doc_1_hdr);
        //console.log('existingQueueDoc',existingQueueDoc)
        if (!existingQueueDoc) {
          let arr: any[] = []
          arr.push(e.sn_id)
          this.queueDocList.push({
            hdr: e.queue_guid_doc_1_hdr,
            line: e.queue_guid_doc_1_line,
            qty: 1,
            serial: arr
          })
        } else {
          //console.log('existingQueueDoc',existingQueueDoc)
          let newQty = existingQueueDoc.qty + 1;
          let arr: any[] = existingQueueDoc.serial;
          arr.push(e.sn_id)
          //console.log('arr2',arr)
          this.queueDocList.filter(x => x.hdr === e.queue_guid_doc_1_hdr).map(a => {
            a.qty = newQty;
            a.serial = arr;
          })

        }

      }


      if (this.serial.serialNumbers.length > 0) {
        /* this.itemDetails?.form?.patchValue ({
          quantity : this.serial.serialNumbers.length
        }) */
        this.itemDetails?.form.controls['qty'].setValue(this.serial.serialNumbers.length);
        this.itemDetails?.onCalculateFromQtyBase();
        this.itemDetails?.form.controls['qty'].markAsTouched();
        this.itemDetails?.form.controls['qty'].markAsDirty();
        this.itemDetails?.form.controls['qty'].updateValueAndValidity();
      }
    }
  }

  addSerialNumberFromList(e: any) {
    //console.log('this.queueDocList',this.queueDocList)
    //console.log('e',e)
    if (e && !this.serial.serialNumbers.includes(e.sn_id)) {
      this.serial.serialNumbers.push(e.sn_id);

      if (this.showSerialFromQueue && e.queue_guid_doc_1_hdr) {
        let existingQueueDoc = this.queueDocList.find(ele => ele.hdr === e.queue_guid_doc_1_hdr);
        //console.log('existingQueueDoc',existingQueueDoc)
        if (!existingQueueDoc) {
          let arr: any[] = []
          arr.push(e.sn_id)
          this.queueDocList.push({
            hdr: e.queue_guid_doc_1_hdr,
            line: e.queue_guid_doc_1_line,
            qty: 1,
            serial: arr
          })
        } else {
          //console.log('existingQueueDoc',existingQueueDoc)
          let newQty = existingQueueDoc.qty + 1;
          let arr: any[] = existingQueueDoc.serial;
          arr.push(e.sn_id)
          //console.log('arr2',arr)
          this.queueDocList.filter(x => x.hdr === e.queue_guid_doc_1_hdr).map(a => {
            a.qty = newQty;
            a.serial = arr;
          })

        }

      }


      if (this.serial.serialNumbers.length > 0) {
        /* this.itemDetails?.form?.patchValue ({
          quantity : this.serial.serialNumbers.length
        }) */
        this.itemDetails?.form.controls['qty'].setValue(this.serial.serialNumbers.length);
        this.itemDetails?.onCalculateFromQtyBase();
        this.itemDetails?.form.controls['qty'].markAsTouched();
        this.itemDetails?.form.controls['qty'].markAsDirty();
        this.itemDetails?.form.controls['qty'].updateValueAndValidity();
      }
    }
  }

  addCouponSerial(e: any) {
    //console.log('addCouponSerial',e)
    if (e && !this.couponSerial.serialNumbers.includes(e.serial_number)) {
      this.couponSerial.serialNumbers.push(e.serial_number);
      this.couponSerial.couponLines.push(e.guid);
      if (this.couponSerial.serialNumbers.length > 0) {
        this.itemDetails?.form.controls['qty'].setValue(this.couponSerial.serialNumbers.length);
        this.itemDetails?.onCalculateFromQtyBase();
        this.itemDetails?.form.controls['qty'].markAsTouched();
        this.itemDetails?.form.controls['qty'].markAsDirty();
        this.itemDetails?.form.controls['qty'].updateValueAndValidity();
      }
    }
  }

  deleteCouponSerial() {
    this.itemDetails?.form.controls['qty'].setValue(this.couponSerial.serialNumbers.length);
    this.itemDetails?.onCalculateFromQtyBase();
    this.itemDetails?.form.controls['qty'].markAsTouched();
    this.itemDetails?.form.controls['qty'].markAsDirty();
    this.itemDetails?.form.controls['qty'].updateValueAndValidity();
  }

  calculateBatchQty(e: any[]) {
    if (this.batchNumber.batchNumbers.length > 0) {
      let total = this.batchNumber?.batchNumbers.map(batch => batch.qty).reduce((prev, next) => prev + next);
      this.itemDetails?.form.controls['qty'].setValue(total);
      this.itemDetails?.onCalculateFromQtyBase();
      this.itemDetails?.form.controls['qty'].markAsTouched();
      this.itemDetails?.form.controls['qty'].markAsDirty();
      this.itemDetails?.form.controls['qty'].updateValueAndValidity();
    }
  }

  calculateBinQty(e: any[]) {
    if (this.binNumber.binNumbers.length > 0) {
      let total = this.binNumber?.binNumbers.map(bin => bin.qty).reduce((prev, next) => prev + next);
      this.itemDetails?.form.controls['qty'].setValue(total);
      this.itemDetails?.onCalculateFromQtyBase();
      this.itemDetails?.form.controls['qty'].markAsTouched();
      this.itemDetails?.form.controls['qty'].markAsDirty();
      this.itemDetails?.form.controls['qty'].updateValueAndValidity();
    }
  }

  updateMainPrice(data: number) {
    if (this.itemDetails) {
      this.itemDetails.form.patchValue({
        unitPriceTxn: Number(data.toFixed(2)),
      });
      this.itemDetails.onCalculateFromUnitPriceTxn();
    }
  }

  resetMainPrice() {
    if (this.itemDetails) {
      this.itemDetails.form.patchValue({
        discountAmt: 0,
      });
      this.itemDetails.onCalculateFromAmountDisc();
    }
  }

  onUpdateMainQty(newQty: number) {
    if (this.multiLevelDiscount) {
      this.multiLevelDiscount.qty = newQty;
    }

  }

  updateItemDetails(data: { priceSet: any, isReward: boolean }) {
    if (this.itemDetails) {
      //this.itemDetails.recalculatePriceset(data.priceSet, data.isReward);
    }
  }

  onUpdateMainUnitPrice(newPrice: number) {
    if (this.multiLevelDiscount) {
      this.multiLevelDiscount.unitPrice = newPrice;
      this.multiLevelDiscount.unitPriceAfterDisc = newPrice;
    }

  }

  selectPricingScheme(data) {
    //console.log("Pricing Scheme", data);
    this.selectedPricingScheme = data;
    //console.log("this.membershipPoints", this.membershipPoints);
    if (this.membershipPoints) {
      this.membershipPoints.pricingSchemeGuid = data;
      this.membershipPoints.points = []
      //this.membershipPoints.getPricingSchemeLink()
    }

  }


  onUpdateCouponType(e) {
    this.couponTxnType = e;
  }

  disableButton() {
    let result = this.itemDetails?.form?.invalid;


    return result;
  }

  updateStockDetails(data: any) {
    if(data){
      this.stockDetailsEmitter.emit(data);
    }
  }

  ngAfterViewInit() {
    if (this.dept?.form) {
      this.dept.form.statusChanges.subscribe((status) => {
        this.checkDepartmentTabValidity();
      });
      this.checkDepartmentTabValidity(); // Initial check
    }
  }

  checkDepartmentTabValidity() {
    if (this.showDepartmentTab() && this.dept?.form.invalid) {
      this.departmentTabInvalid = true;
    } else {
      this.departmentTabInvalid = false;
    }
  }

  showDepartmentTab(): boolean {
    const { HIDE_SEGMENT, HIDE_DIMENSION, HIDE_PROFIT_CENTER, HIDE_PROJECT, HIDE_DEPARTMENT } = this.appletSettings;

    // Return false (hide the tab) only if all fields are true
    return !HIDE_DEPARTMENT && !(HIDE_SEGMENT && HIDE_DIMENSION && HIDE_PROFIT_CENTER && HIDE_PROJECT);
  }

}
