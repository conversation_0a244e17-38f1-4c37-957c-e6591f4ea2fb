import { ChangeDetectionStrategy, Component, ViewChild, ChangeDetectorRef } from "@angular/core";
import { ComponentStore } from "@ngrx/component-store";
import { Store } from "@ngrx/store";
import { GridOptions } from "ag-grid-community";
import {
  GenericDocContainerModel,
  GenericDocSearchCriteriaDtoModel,
  InternalReceiptVoucherService,
  Pagination,
  DocumentShortCodesClass,
  GenericDocLockService,
  GenericDocEditingLockDto
} from "blg-akaun-ts-lib";
import * as moment from "moment";
import { ToastrService } from "ngx-toastr";
import { ClientSidePermissionsSelectors } from "projects/shared-utilities/modules/permission/client-side-permissions-controller/selectors";
import { PermissionStates } from "projects/shared-utilities/modules/permission/permission-controller";
import { UserPermInquirySelectors } from "projects/shared-utilities/modules/permission/user-permissions-inquiry-controller/selectors";
import { SessionSelectors } from "projects/shared-utilities/modules/session/session-controller/selectors";
import { SessionStates } from "projects/shared-utilities/modules/session/session-controller/states";
import { PaginationClientSideV3Component } from "projects/shared-utilities/utilities/pagination-client-side-v3/pagination-client-side-v3.component";
import { ViewColumnComponent } from "projects/shared-utilities/view-column.component";
import { from, Subject, Observable, forkJoin, of } from "rxjs";
import { debounceTime, map, mergeMap, toArray } from "rxjs/operators";
import { SubSink } from "subsink2";
import { ViewColumnFacade } from "../../../facades/view-column.facade";
import { InternalReceiptVoucherSearchModel } from "../../../models/advanced-search-models/internal-receipt-voucher.model";
import { AppletSettings } from "../../../models/applet-settings.model";
import { InternalReceiptVoucherActions } from "../../../state-controllers/internal-receipt-voucher-controller/store/actions";
import { InternalReceiptVoucherSelectors } from "../../../state-controllers/internal-receipt-voucher-controller/store/selectors";
import { InternalReceiptVoucherStates } from "../../../state-controllers/internal-receipt-voucher-controller/store/states";
import { Column1ViewModelActions } from "../../../state-controllers/receipt-voucher-view-model-controller/actions";
import { Column1ViewSelectors } from "../../../state-controllers/receipt-voucher-view-model-controller/selectors";
import { ColumnViewModelStates } from "../../../state-controllers/receipt-voucher-view-model-controller/states";
import { CustomFooterComponent } from "../../utilities/custom-footer/custom-footer.component";
import { AppConfig } from "projects/shared-utilities/visa";
import { UUID } from "angular2-uuid";
import { FormControl } from "@angular/forms";
import { ContraActions } from "../../../state-controllers/draft-controller/store/actions";
import { DraftStates } from "../../../state-controllers/draft-controller/store/states";
import { UtilitiesModule } from 'projects/shared-utilities/utilities/utilities.module';
import { ExportStatusBarComponent } from 'projects/shared-utilities/utilities/status-bar/export-status-bar.component';
import { SessionActions } from "projects/shared-utilities/modules/session/session-controller/actions";
import { MatDialog, MatDialogRef } from "@angular/material/dialog";
import { AkaunGenDocLockDialogComponent } from "projects/shared-utilities/dialogues/akaun-gen-doc-lock-dialog/akaun-gen-doc-lock-dialog.component";
import { ListingInputModel } from 'projects/shared-utilities/models/listing-input.model';
import { ListingService } from 'projects/shared-utilities/services/listing-service';
import { PaginationV2Component } from 'projects/shared-utilities/utilities/pagination-v2/pagination-v2.component';
import { SearchQueryModel } from 'projects/shared-utilities/models/query.model';
import { AdvancedSearchGeneralComponent } from 'projects/shared-utilities/utilities/advanced-search-general/advanced-search-general.component';
import { AkaunConfirmationDialogComponent } from "projects/shared-utilities/utilities/dialogues/akaun-confirmation-dialog/akaun-confirmation-dialog";
import {
  transformAllDateRangesToRelative,
  transformRelativeDatesToAbsolute
} from "../../../../../../../../shared-utilities/date-transform.util";

interface LocalState {
  deactivateAdd: boolean;
  deactivateList: boolean;
  selectedRow: any;
}
@Component({
  selector: "app-internal-receipt-voucher-listing",
  templateUrl: "./internal-receipt-voucher-listing.component.html",
  styleUrls: ["./internal-receipt-voucher-listing.component.scss"],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [ComponentStore],
})
export class InternalReceiptVoucherListingComponent extends ViewColumnComponent {
  @ViewChild(AdvancedSearchGeneralComponent) advancedSearch!: AdvancedSearchGeneralComponent;

  protected subs = new SubSink();
  compId = 'receipt-voucher'
  public compName = "Internal Receipt Voucher Listing";
  protected readonly index = 0;
  protected localState: LocalState;
  printableFormat = new FormControl();
  readonly localState$ = this.viewColFacade.selectLocalState(this.index);
  selectedBranches$ = this.viewModelStore.select(
    Column1ViewSelectors.selectAdvanceSearch_Branch_Field
  );
  readonly deactivateAdd$ = this.componentStore.select(
    (state) => state.deactivateAdd
  );
  toggleColumn$: Observable<boolean>;
  searchModel = InternalReceiptVoucherSearchModel;
  gridApi;
  gridColumnApi;
  appletCode = sessionStorage.getItem('appletCode');

  apiVisa = AppConfig.apiVisa;

  firstLoad$ = this.store.select(InternalReceiptVoucherSelectors.selectFirstLoadListing);
  totalRecords$ = this.store.select(InternalReceiptVoucherSelectors.selectTotalRecords);
  rowData$ = this.store.select(InternalReceiptVoucherSelectors.selectRowData);
  rowData = [];
  totalRecords = 0;
  limit = 25;
  searchQuery: SearchQueryModel;
  firstLoad;

  pagination = new Pagination();
  settings: any[] = [];
  appletSettings: any;
  permissions: any[] = [];

  ColumnViewSelectors = Column1ViewSelectors;
  ColumnViewActions = Column1ViewModelActions;

  private columnMoveSubject: Subject<void> = new Subject<void>();
  private debounceTimeMs = 500;

  readPermissionDefintion =
    {"branch": "TNT_API_DOC_INTERNAL_RECEIPT_VOUCHER_READ_TGT_GUID"};
  branchGuids: any[];
  akaunGenDocLockDialogComponent: MatDialogRef<AkaunGenDocLockDialogComponent>;

  receiptVoucher$: Observable<GenericDocContainerModel[]>;
  @ViewChild(PaginationClientSideV3Component)
  paginationComp: PaginationClientSideV3Component;

  showColumns = [
    { name: 'bl_fi_generic_doc_hdr.server_doc_1', setting: 'HIDE_SERVER_DOC_1', permission: 'SHOW_DOC_NO_TENANT' },
    { name: 'bl_fi_generic_doc_hdr.server_doc_2', setting: 'HIDE_SERVER_DOC_2', permission: 'SHOW_DOC_NO_COMPANY' },
    { name: 'bl_fi_generic_doc_hdr.server_doc_3', setting: 'HIDE_SERVER_DOC_3', permission: 'SHOW_DOC_NO_BRANCH' },
    { name: 'bl_fi_generic_doc_hdr.code_branch', setting: 'HIDE_LISTING_BRANCH', permission: 'SHOW_LISTING_BRANCH' },
    { name: 'bl_fi_generic_doc_hdr.date_txn', setting: 'HIDE_TRANSACTION_DATE', permission: 'SHOW_TRANSACTION_DATE' },
    { name: 'bl_fi_generic_doc_hdr.client_doc_type', setting: 'HIDE_CLIENT_DOC_TYPE', permission: 'SHOW_CLIENT_DOC_TYPE' },
    { name: 'bl_fi_generic_doc_hdr.client_doc_1', setting: 'HIDE_CLIENT_DOC_1', permission: 'SHOW_CLIENT_DOC_1' },
    { name: 'bl_fi_generic_doc_hdr.client_doc_2', setting: 'HIDE_CLIENT_DOC_2', permission: 'SHOW_CLIENT_DOC_2' },
    { name: 'bl_fi_generic_doc_hdr.client_doc_3', setting: 'HIDE_CLIENT_DOC_3', permission: 'SHOW_CLIENT_DOC_3' },
    { name: 'bl_fi_generic_doc_hdr.client_doc_4', setting: 'HIDE_CLIENT_DOC_4', permission: 'SHOW_CLIENT_DOC_4' },
    { name: 'bl_fi_generic_doc_hdr.client_doc_5', setting: 'HIDE_CLIENT_DOC_5', permission: 'SHOW_CLIENT_DOC_5' },
    { name: 'bl_fi_generic_doc_hdr.arap_pns_amount', setting: 'HIDE_ARAP_PNS', permission: 'SHOW_ARAP_PNS' },
    { name: 'bl_fi_generic_doc_hdr.arap_stlm_amount', setting: 'HIDE_ARAP_SETTLEMENT', permission: 'SHOW_ARAP_SETTLEMENT' },
    { name: 'bl_fi_generic_doc_hdr.arap_doc_open', setting: 'HIDE_ARAP_DOC_OPEN', permission: 'SHOW_ARAP_DOC_OPEN' },
    { name: 'bl_fi_generic_doc_hdr.arap_contra', setting: 'HIDE_ARAP_CONTRA', permission: 'SHOW_ARAP_CONTRA' },
    { name: 'bl_fi_generic_doc_hdr.arap_bal', setting: 'HIDE_ARAP_BAL', permission: 'SHOW_ARAP_BAL' },
    { name: 'bl_fi_generic_doc_hdr.arap_stlm_amount', setting: 'HIDE_ARAP_SETTLEMENT', permission: 'SHOW_ARAP_SETTLEMENT' },
    { name: 'bl_fi_generic_doc_hdr.bl_fi_mst_gl_dimension_code', setting: 'SHOW_GL_DIMENSION', permission: 'SHOW_GL_DIMENSION' },
    { name: 'bl_fi_generic_doc_hdr.bl_fi_mst_segment_code', setting: 'SHOW_SEGMENT', permission: 'SHOW_SEGMENT' },
    { name: 'bl_fi_generic_doc_hdr.bl_fi_mst_profit_center_code', setting: 'SHOW_PROFIT_CENTER', permission: 'SHOW_PROFIT_CENTER' },
    { name: 'bl_fi_generic_doc_hdr.bl_fi_mst_project_code', setting: 'SHOW_PROJECT', permission: 'SHOW_PROJECT' },
    { name: 'bl_fi_generic_doc_hdr.doc_entity_hdr_json.description', setting:'SHOW_DESCRIPTION', permission:'SHOW_DESCRIPTION'},
  ]
  dateMappings = [
    { display: 'CREATED DATE', key: 'created_date' },
    { display: 'UPDATED DATE', key: 'updated_date' },
    { display: 'TRANSACTION DATE', key: 'date_txn' }
  ];
  gridOptions: GridOptions = {
    rowSelection: 'multiple',
    paginationPageSize: this.limit,
    onRowClicked: (event) => this.onRowClicked(event.data),
    multiSortKey: 'ctrl',
    onSelectionChanged: (event) => this.onSelectionChanged(event),
  }

  hasCreatePermission: boolean;
  columnsDefs = [
    {
      floatingFilter: false,
      checkboxSelection: true,
      minWidth: 20,
      suppressColumnsToolPanel: false,
      sortable: false,
      resizable: true,
      suppressMenu: true,
      enableRowGroup: false,
      pdfExportOptions: { skipColumn: true }
    },
    {
      headerName: "Doc No (Tenant)",
      field: "bl_fi_generic_doc_hdr.server_doc_1",
      type: 'textColumn',
    },
    {
      headerName: "Doc No (Company)",
      field: "bl_fi_generic_doc_hdr.server_doc_2",
      type: 'textColumn',
    },
    {
      headerName: "Doc No (Branch)",
      field: "bl_fi_generic_doc_hdr.server_doc_3",
      type: 'textColumn',
    },
    {
      headerName: "Reference",
      field: "bl_fi_generic_doc_hdr.doc_reference",
      type: 'textColumn',
    },
    {
      headerName: "Posting Status",
      field: "bl_fi_generic_doc_hdr.posting_status",
      type: 'textColumn',
    },
    {
      headerName: "Status",
      field: "bl_fi_generic_doc_hdr.status",
      type: 'textColumn',
    },
    {
      headerName: "Branch Code",
      field: "bl_fi_generic_doc_hdr.code_branch",
      type: 'textColumn'
    },
    {
      headerName: "Currency",
      field: "bl_fi_generic_doc_hdr.doc_ccy",
      type: 'textColumn',
    },
    {
      headerName: "Customer Name",
      field: "bl_fi_generic_doc_hdr.doc_entity_hdr_json.entityName",
      type: 'textColumn',
    },
    {
      headerName: "Amount Txn",
      field: "bl_fi_generic_doc_hdr.amount_txn",
      type: "decimalColumn",
    },
    {
      headerName: "Settlement Amount",
      field: "bl_fi_generic_doc_hdr.amount_internal_settlement",
      type: "decimalColumn",
    },
    {
      headerName: "ARAP PNS",
      field: "bl_fi_generic_doc_hdr.arap_pns_amount",
      type: "decimalColumn",
    },
    {
      headerName: "ARAP Settlement",
      field: "bl_fi_generic_doc_hdr.arap_stlm_amount",
      type: "decimalColumn",
    },
    {
      headerName: "ARAP Doc Open",
      field: "bl_fi_generic_doc_hdr.arap_doc_open",
      type: "decimalColumn",
    },
    {
      headerName: "ARAP Contra",
      field: "bl_fi_generic_doc_hdr.arap_contra",
      type: "decimalColumn",
    },
    {
      headerName: "ARAP Bal",
      field: "bl_fi_generic_doc_hdr.arap_bal",
      type: "decimalColumn",
    },
    {
      headerName: "Updated Date",
      field: "bl_fi_generic_doc_hdr.updated_date",
      type: " dateTimeColumn",
    },
    {
      headerName: "Created Date",
      field: "bl_fi_generic_doc_hdr.created_date",
      type: " dateTimeColumn",
    },
    {
      headerName: "Transaction Date",
      field: "bl_fi_generic_doc_hdr.date_txn",
      type: " dateColumn",
    },
    {
      headerName: "Created by",
      field: "bl_fi_generic_doc_hdr.created_by_name",
      type: "textColumn",
    },
    {
      headerName: "Updated By",
      field: "bl_fi_generic_doc_hdr.updated_by_name",
      type: "textColumn"
    },
    {
      headerName: "Client Doc Type",
      field: "bl_fi_generic_doc_hdr.client_doc_type",
      type: "textColumn",
    },
    {
      headerName: "Client Doc 1",
      field: "bl_fi_generic_doc_hdr.client_doc_1",
      type: "textColumn",
    },
    {
      headerName: "Client Doc 2",
      field: "bl_fi_generic_doc_hdr.client_doc_2",
      type: "textColumn",
    },
    {
      headerName: "Client Doc 3",
      field: "bl_fi_generic_doc_hdr.client_doc_3",
      type: "textColumn",
    },
    {
      headerName: "Client Doc 4",
      field: "bl_fi_generic_doc_hdr.client_doc_4",
      type: "textColumn",
    },
    {
      headerName: "Client Doc 5",
      field: "bl_fi_generic_doc_hdr.client_doc_5",
      type: "textColumn",
    },
    {
      headerName: "GL Dimension",
      field: "bl_fi_generic_doc_hdr.bl_fi_mst_gl_dimension_code",
      type: "textColumn",
    },
    {
      headerName: "Segment",
      field: "bl_fi_generic_doc_hdr.bl_fi_mst_segment_code",
      type: "textColumn",
    },
    {
      headerName: "Profit Center",
      field: "bl_fi_generic_doc_hdr.bl_fi_mst_profit_center_code",
      type: "textColumn",
    },
    {
      headerName: "Project",
      field: "bl_fi_generic_doc_hdr.bl_fi_mst_project_code",
      type: "textColumn",
    },
    {
      headerName: "Remarks",
      field: "bl_fi_generic_doc_hdr.doc_remarks",
      type: "textColumn",
    }
  ];

  disableFinalButton: boolean = true;
  disableVoidButton: boolean = true;
  disableDiscardButton: boolean = true;
  akaunConfirmationDialogComponent: MatDialogRef<AkaunConfirmationDialogComponent>;

  personalData: any;

  constructor(
    private viewColFacade: ViewColumnFacade,
    public readonly viewModelStore: Store<ColumnViewModelStates>,
    private readonly store: Store<InternalReceiptVoucherStates>,
    private toastr: ToastrService,
    protected readonly sessionStore: Store<SessionStates>,
    protected readonly permissionStore: Store<PermissionStates>,
    private readonly componentStore: ComponentStore<LocalState>,
    private irvService: InternalReceiptVoucherService,
    private readonly draftStore: Store<DraftStates>,
    private genericDocLockService: GenericDocLockService,
    private dialogRef: MatDialog,
    private listingService: ListingService,
    private cdr: ChangeDetectorRef,
  ) {
    super();
  }
  userPermissionTarget$ = this.permissionStore.select(
    UserPermInquirySelectors.selectUserPermInquiry
  );
  clientSidePermissions$ = this.permissionStore.select(
    ClientSidePermissionsSelectors.selectAll
  );
  masterSettings$ = this.sessionStore.select(
    SessionSelectors.selectMasterSettings
  );
  ngOnInit() {
    this.subs.sink = this.firstLoad$.subscribe(firstLoad => this.firstLoad = firstLoad);
    this.subs.sink = this.rowData$.subscribe(rowData => this.rowData = rowData);
    this.subs.sink = this.totalRecords$.subscribe(totalRecords => this.totalRecords = totalRecords);

    this.toggleColumn$ = this.viewColFacade.toggleColumn$;
    this.subs.sink = this.localState$.subscribe((a) => {
      this.localState = a;
      this.componentStore.setState(a);
    });

    this.subs.sink = this.sessionStore.select(SessionSelectors.selectPersonalSettings)
      .subscribe((data) => {
        this.personalData = data;
        if(data?.DEFAULT_TOGGLE_COLUMN){
          if(data.DEFAULT_TOGGLE_COLUMN === "SINGLE"){
            this.onToggle(true);
          }else{
            this.onToggle(false);
          }
        }
      });

    this.subs.sink = this.userPermissionTarget$.subscribe((targets) => {
      console.log("targets", targets);
      let target = targets.filter(
        (target) =>
          target.permDfn ===
          "TNT_API_DOC_INTERNAL_RECEIPT_VOUCHER_READ_TGT_GUID"
      );
      let createPermissionTarget = targets.filter(
        (target) =>
          target.permDfn ===
          "TNT_API_DOC_INTERNAL_RECEIPT_VOUCHER_CREATE_TGT_GUID"
      );
      let adminCreatePermissionTarget = targets.filter(
        (target) => target.permDfn === "TNT_TENANT_ADMIN"
      );
      let ownerCreatePermissionTarget = targets.filter(
        (target) => target.permDfn === "TNT_TENANT_OWNER"
      );
      if (adminCreatePermissionTarget[0]?.hasPermission
        || ownerCreatePermissionTarget[0]?.hasPermission) {
        console.log("has Permission");
        this.branchGuids = [];
      } else {
        console.log("has targets");
        this.branchGuids = target[0]?.target !== null && Object.keys(target[0]?.target || {}).length !== 0
          ? target[0]?.target["bl_fi_mst_branch"]
          : [];
      }
      if (
        createPermissionTarget[0]?.hasPermission ||
        adminCreatePermissionTarget[0]?.hasPermission ||
        ownerCreatePermissionTarget[0]?.hasPermission
      ) {
        this.hasCreatePermission = true;
      } else {
        this.hasCreatePermission = false;
      }
    });
    this.getAppletSettings();
    this.getPermissions();
  }
  async getAppletSettings() {
    this.subs.sink = await this.masterSettings$.subscribe(
      resolve => {
        this.appletSettings = resolve;
        this.settings.push(resolve);
        this.printableFormat.setValue(resolve?.PRINTABLE);
      }
    );
  }
  async getPermissions() {
    this.subs.sink = await this.clientSidePermissions$.subscribe(
      resolve => {
        this.permissions = resolve;
      }
    );
  }
  isShowColumn(settingName, permissionName) {
    if (settingName.includes('SHOW')) {
      return this.settings.some(setting => setting[settingName]) || this.permissions.some(permission => permission.perm_code === permissionName);
    }
    return !this.settings.some(setting => setting[settingName]) || this.permissions.some(permission => permission.perm_code === permissionName);
  }

  // setColumnVisible(gridApi, gridColumnApi, column: any, visible: boolean) {
  //   var colDef = column.getColDef();
  //   colDef.suppressColumnsToolPanel = !visible;
  //   gridColumnApi.setColumnVisible(column, visible)
  //   gridApi.refreshToolPanel();
  // }
  // ngAfterViewChecked() {
  //     this.gridOptions.api.addEventListener("columnVisible", (event) => {
  //       this.columnMoveSubject.next();
  //     });
  //
  //     this.gridOptions.api.addEventListener("columnMoved", (event) => {
  //       this.columnMoveSubject.next();
  //     });
  // }

  onGridReady(params) {
    this.gridApi = params.api;
    this.gridApi.closeToolPanel();
    this.gridColumnApi = params.columnApi;

    const statusBarComponent = this.gridApi.getStatusPanel('statusBarExportKey');
    statusBarComponent.setTitle(this.compName);
    statusBarComponent.setFilename(this.compName);
    statusBarComponent.setPageOritentation('landscape');

    if (this.rowData.length > 0) {
      this.setDataRowCache();
    }
    else if (!this.appletSettings.DISABLE_GEN_DOC_LISTING) {
      this.clear();
      this.getTotalData();
      this.createData();
    }
    this.subs.sink = this.store.select(InternalReceiptVoucherSelectors.refreshGenDocListing).subscribe(resolved => {
      if (resolved) {
        this.firstLoad = true;
        this.clear();
        this.getTotalData();
        this.createData();
        this.store.dispatch(InternalReceiptVoucherActions.resetAgGrid());
      }
    });

    this.gridApi.sizeColumnsToFit();
    UtilitiesModule.autoSizeAllColumns(params);
  }

  onToggle(e: boolean) {
    this.viewColFacade.toggleColumn(e);
    this.personalData.DEFAULT_TOGGLE_COLUMN = e ? 'SINGLE' : 'DOUBLE';
    this.sessionStore.dispatch(SessionActions.saveTogglePersonalSettingsInit({ settings: this.personalData }));
  }

  isDisabled() {
    if (this.printableFormat.value === null) {
      return true;
    } else {
      return false;
    }
  }
  onPrintSelected() {
    let autoPreview: boolean = this.appletSettings.ENABLE_AUTO_POPUP ?? false;

    if (this.gridApi.getSelectedRows().length > 0) {
      let selectedRows;
      let itemGuid = [];
      let docNumbers = [];
      selectedRows = this.gridApi.getSelectedRows();
      selectedRows.map((row) => {
        itemGuid.push(row.bl_fi_generic_doc_hdr.guid);
        docNumbers.push(row.bl_fi_generic_doc_hdr?.server_doc_1);
      });
      if (itemGuid.length > 0) {
        this.store.dispatch(
          InternalReceiptVoucherActions.printMultipleJasperPdfInit({
            guids: itemGuid,
            printable: this.printableFormat.value.toString(),
            serverDocs: docNumbers,
            preview: autoPreview,
          })
        );
      }
    } else {
      this.viewColFacade.showFailedToast({
        message: "Please select one or more documents",
      });
    }
  }

  createData(showAll?) {
    console.log("on create data....");
    const formData = this.getInputModel();
    if (showAll) formData.limit = null;
    this.subs.sink = this.listingService.get("gen-doc/internal-receipt-vouchers/backoffice-ep", formData, this.apiVisa).pipe(
      mergeMap(a => from(a.data).pipe(
        map(b => {
          Object.assign(b,
            {
              bl_fi_generic_doc_hdr: b,
              "doc_type": b.server_doc_type ? DocumentShortCodesClass.serverDocTypeToShortCodeMapper(b.server_doc_type) : "",
              "posting_status": (b.posting_status ? b.posting_status : "DRAFT")
            }
          )
          return b;
        }),
        toArray(),
        map(c => {
          a.data = c;
          return a;
        })
      ))
    ).subscribe(resolved => {
      console.log(resolved);
      this.rowData = [...this.rowData, ...resolved.data];
      this.gridApi.setRowData(this.rowData);
      this.store.dispatch(InternalReceiptVoucherActions.selectRowData({ rowData: <any>this.rowData }));
      this.gridApi.paginationGoToLastPage();
    }, err => {
      console.error(err);
    });
  };

  getInputModel() {
    const inputModel = {} as ListingInputModel;
    inputModel.search = (this.searchQuery?.keyword);
    inputModel.searchColumns = this.getFuzzySearch();
    inputModel.status = ['ACTIVE'];
    inputModel.orderBy = this.appletSettings.SORT_ORDER ?? 'updated_date';
    inputModel.order = 'desc';
    inputModel.limit = this.limit;
    inputModel.offset = this.rowData.length;
    inputModel.calcTotalRecords = false;
    inputModel.showCreatedBy = false;
    inputModel.showUpdatedBy = false;
    inputModel.filterLogical = 'AND';
    inputModel.filterConditions = [];
    inputModel.childs = [];
    inputModel.joins = [
      // {
      //   "tableName": "bl_fi_mst_branch",
      //   "joinColumn": "guid_branch",
      //   "columns": ["code"],
      //   "joinType": "left join"
      // },
      {
        "tableName": "bl_fi_mst_gl_dimension",
        "joinColumn": "guid_dimension",
        "columns": ["code"],
        "joinType": "left join"
      },
      {
        "tableName": "bl_fi_mst_segment",
        "joinColumn": "guid_segment",
        "columns": ["code"],
        "joinType": "left join"
      },
      {
        "tableName": "bl_fi_mst_profit_center",
        "joinColumn": "guid_profit_center",
        "columns": ["code"],
        "joinType": "left join"
      },
      {
        "tableName": "bl_fi_mst_project",
        "joinColumn": "guid_project",
        "columns": ["code"],
        "joinType": "left join"
      },
    ]
    inputModel.childs = [
      {
        "tableName": "bl_fi_generic_doc_line",
        "joinColumn": "generic_doc_hdr_guid",
        "joinType": "left join",
        "filterLogical": "AND",
        "filterConditions": []
      },
    ];

    let filterBranch;
    if (this.branchGuids.length > 0) {
      filterBranch = {
        "filterColumn": "guid_branch",
        "filterValues": this.branchGuids,
        "filterOperator": "IN"
      };
    }

    let filterDate = {
      "dateFrom": UtilitiesModule.getDateNoTime(moment().subtract(1, 'month')),
      "dateTo": UtilitiesModule.getTodayNoTime(),
      "column": "date_txn"
    }

    if (this.appletSettings?.DEFAULT_TRANSACTION_DATE === "1_week") {
      filterDate = {
        "dateFrom": UtilitiesModule.getDateNoTime(moment().subtract(1, 'week')),
        "dateTo": UtilitiesModule.getTodayNoTime(),
        "column": "date_txn"
      }
    } else if (this.appletSettings?.DEFAULT_TRANSACTION_DATE === "1_day") {
      filterDate = {
        "dateFrom": UtilitiesModule.getTodayNoTime(),
        "dateTo": UtilitiesModule.getTodayNoTime(),
        "column": "date_txn"
      }
    }

    if (this.firstLoad) {
      inputModel.filterDate = filterDate;

      const status = this.getStatus();
      if (status) {
        inputModel.status = status;
      }

      const postingStatus = this.getPostingStatus();
      if (postingStatus && postingStatus.length > 0) {
        inputModel.filterConditions.push({
          "filterColumn": "posting_status",
          "filterValues": postingStatus,
          "filterOperator": "IN"
        })
      }
    }

    if (this.searchQuery?.queryString) {
      const entity = UtilitiesModule.checkNull(this.searchQuery.queryString['entity'], []);
      if (entity && entity.length > 0) {
        inputModel.filterConditions.push({
          "filterColumn": "doc_entity_hdr_guid",
          "filterValues": entity,
          "filterOperator": "IN"
        })
      }

      const companies = UtilitiesModule.checkNull(this.searchQuery.queryString['company'], []);
      if (companies && companies.length > 0) {
        inputModel.filterConditions.push({
          "filterColumn": "guid_comp",
          "filterValues": companies,
          "filterOperator": "IN"
        })
      }

      const branches = UtilitiesModule.checkNull(this.searchQuery.queryString['branch'], []);
      if (branches && branches.length > 0) {
        filterBranch = {
          "filterColumn": "guid_branch",
          "filterValues": branches,
          "filterOperator": "IN"
        }
      }
      const salesAgents = UtilitiesModule.checkNull(this.searchQuery.queryString['salesAgent'], []);
      if (salesAgents && salesAgents.length > 0) {
        inputModel.filterConditions.push({
          "filterColumn": "sales_entity_hdr_guid",
          "filterValues": salesAgents,
          "filterOperator": "IN"
        })
      }
      const postingStatus = UtilitiesModule.checkNull(this.searchQuery.queryString['postingStatus'], []);
      if (postingStatus && postingStatus.length > 0) {
        inputModel.filterConditions.push({
          "filterColumn": "posting_status",
          "filterValues": postingStatus,
          "filterOperator": "IN"
        })
      }

      const status = UtilitiesModule.checkNull(this.searchQuery.queryString['status'], []);
      if (status && status.length > 0) {
        inputModel.status = status;
      }

      const glDimensions = UtilitiesModule.checkNull(this.searchQuery.queryString['glDimension'], []);
      if (glDimensions && glDimensions.length > 0) {
        inputModel.filterConditions.push({
          "filterColumn": "guid_dimension",
          "filterValues": glDimensions,
          "filterOperator": "IN"
        })
      }
      const segments = UtilitiesModule.checkNull(this.searchQuery.queryString['segment'], []);
      if (segments && segments.length > 0) {
        inputModel.filterConditions.push({
          "filterColumn": "guid_segment",
          "filterValues": segments,
          "filterOperator": "IN"
        })
      }
      const profitCenters = UtilitiesModule.checkNull(this.searchQuery.queryString['profitCenter'], []);
      if (profitCenters && profitCenters.length > 0) {
        inputModel.filterConditions.push({
          "filterColumn": "guid_profit_center",
          "filterValues": profitCenters,
          "filterOperator": "IN"
        })
      }
      const projects = UtilitiesModule.checkNull(this.searchQuery.queryString['project'], []);
      if (projects && projects.length > 0) {
        inputModel.filterConditions.push({
          "filterColumn": "guid_project",
          "filterValues": projects,
          "filterOperator": "IN"
        })
      }
      if (this.searchQuery.queryString['createdDateCheckbox']) {
        const dateFrom = UtilitiesModule.checkNull(this.searchQuery.queryString['createdDate']['from'], '2022-01-01T00:00:00.000Z');
        const dateTo = UtilitiesModule.checkNull(this.searchQuery.queryString['createdDate']['to'], '2099-12-31T00:00:00.000Z');
        inputModel.filterDate = {
          "dateFrom": dateFrom,
          "dateTo": dateTo,
          "column": "created_date"
        }
      }
      else if (this.searchQuery.queryString['transactionDateCheckbox']) {
        const dateFrom = UtilitiesModule.checkNullDate(this.searchQuery.queryString['transactionDate']['from'], '2022-01-01T00:00:00.000Z');
        const dateTo = UtilitiesModule.checkNullDate(this.searchQuery.queryString['transactionDate']['to'], '2099-12-31T00:00:00.000Z');
        inputModel.filterDate = {
          "dateFrom": dateFrom,
          "dateTo": dateTo,
          "column": "date_txn"
        }
      }
      else if (this.searchQuery.queryString['updatedDateCheckbox']) {
        const dateFrom = UtilitiesModule.checkNull(this.searchQuery.queryString['updatedDate']['from'], '2022-01-01T00:00:00.000Z');
        const dateTo = UtilitiesModule.checkNull(this.searchQuery.queryString['updatedDate']['to'], '2099-12-31T00:00:00.000Z');
        inputModel.filterDate = {
          "dateFrom": dateFrom,
          "dateTo": dateTo,
          "column": "updated_date"
        }
      }

      const orderBy = UtilitiesModule.checkNull(this.searchQuery.queryString['orderBy'], null);
      if (orderBy) {
        inputModel.orderBy = this.getOrderBy(orderBy);
      }

    }

    if (filterBranch) {
      inputModel.filterConditions.push(filterBranch);
    }
    return inputModel;
  }

  getTotalData() {
    const formData = this.getInputModel();
    formData.childs = [];
    formData.joins = [];

    formData.calcTotalRecords = true;
    formData.calcTotalRecordsOnly = true;
    this.subs.sink = this.listingService.get("gen-doc/internal-receipt-vouchers/backoffice-ep", formData, this.apiVisa).pipe(
    ).subscribe(resolved => {
      this.totalRecords = resolved.totalRecords;
      this.store.dispatch(InternalReceiptVoucherActions.selectTotalRecords({ totalRecords: this.totalRecords }));
      this.setPaginationTotalRecords(this.totalRecords);
    }, err => {
      console.error(err);
    });
  };

  setDataRowCache() {
    this.gridApi.setRowData(this.rowData);
    this.setPaginationTotalRecords(this.totalRecords);

    this.gridApi.forEachNode(node => {
      if (node.data.guid === this.localState.selectedRow) {
        node.setSelected(true);
        const pageToNavigate = JSON.parse(localStorage.getItem(this.compId+'ListingPage'));
        this.gridApi.paginationGoToPage(pageToNavigate);
      }
    });
  }

  setPaginationTotalRecords(totalRecords) {
    const statusBarPagination = this.gridApi.getStatusPanel('statusBarPagination');
    statusBarPagination.setTotalRecords(totalRecords);
  }

  onSearch(e: SearchQueryModel) {
    console.log("search", e);
    if (!e.isEmpty) {
      if (e.keyword && e.keyword.length > 0 && e.keyword.length < 3) {
        this.toastr.error(
          'Search keyword must more than 2 characters.',
          'Keyword',
          {
            tapToDismiss: true,
            progressBar: true,
            timeOut: 1300
          }
        );
        return;
      }

      this.store.dispatch(InternalReceiptVoucherActions.selectFirstLoadListing({ firstLoadListing: false }));
      this.firstLoad = false;

      this.searchQuery = e;
    } else {
      this.store.dispatch(InternalReceiptVoucherActions.selectFirstLoadListing({ firstLoadListing: true }));
      this.firstLoad = true;

      this.searchQuery = null;
    }
    this.clear();
    this.getTotalData();
    this.createData();
  }

  clear() {
    this.gridApi.setRowData(null);
    this.totalRecords = 0;
    this.rowData = [];
    this.setPaginationTotalRecords(this.totalRecords);
  }

  onAdd() {
    this.viewColFacade.resetDepartment();
    this.draftStore.dispatch(ContraActions.resetContra());
    this.store.dispatch(InternalReceiptVoucherActions.resetDraft());
    this.store.dispatch(InternalReceiptVoucherActions.resetExpansionPanel(
      { resetIndex: true }
    ));
    this.store.dispatch(InternalReceiptVoucherActions.recalculateDocBalance());
    let genDoc = new GenericDocContainerModel();
    genDoc.bl_fi_generic_doc_hdr.guid = UUID.UUID().toLowerCase();
    this.subs.sink = this.irvService
      .createTemp(genDoc, AppConfig.apiVisa)
      .subscribe((response) => {
        let entity = new GenericDocContainerModel();
        entity = response.data;
        this.store.dispatch(
          InternalReceiptVoucherActions.createTempReceiptVoucherSuccess({
            response: entity,
          })
        );
        const genDoc = { ...response.data };
        this.store.dispatch(
          InternalReceiptVoucherActions.selectGUID({
            guid: genDoc.bl_fi_generic_doc_hdr.guid.toString(),
          })
        );
        this.store.dispatch(
          InternalReceiptVoucherActions.selectIRVForEdit({ genDoc })
        );
        this.store.dispatch(
          InternalReceiptVoucherActions.refreshArapListing({
            refreshArapListing: true,
          })
        );
        this.store.dispatch(
          InternalReceiptVoucherActions.setEditMode({ editMode: true })
        );

        let genDocLockDto = new GenericDocEditingLockDto();

        genDocLockDto.generic_doc_hdr_guid = genDoc.bl_fi_generic_doc_hdr.guid.toString();
        this.subs.sink = this.genericDocLockService.checkDocument(genDocLockDto, AppConfig.apiVisa).subscribe();

        this.viewColFacade.updateInstance(this.index, {
          ...this.localState,
          deactivateAdd: true,
          deactivateList: false,
          selectedRow: response.data.bl_fi_generic_doc_hdr.guid,
        });
        this.viewColFacade.onNextAndReset(this.index, 2);
      });
  }
  onRowClicked(entity: GenericDocContainerModel) {
    if (entity && !this.localState.deactivateList) {
      this.viewColFacade.resetDepartment();
      this.subs.sink = this.irvService
        .getByGuid(
          entity.bl_fi_generic_doc_hdr.guid.toString(),
          AppConfig.apiVisa
        )
        .subscribe((response) => {
          const genDoc = { ...response.data };
          this.store.dispatch(
            InternalReceiptVoucherActions.selectGUID({
              guid: response.data.bl_fi_generic_doc_hdr.guid.toString(),
            })
          );
          // this one action points to many reducer functions instead of firing many actions
          this.store.dispatch(
            InternalReceiptVoucherActions.selectIRVForEdit({ genDoc })
          );
          this.store.dispatch(InternalReceiptVoucherActions.resetExpansionPanel(
            { resetIndex: false }
          ));
          this.store.dispatch(
            InternalReceiptVoucherActions.refreshArapListing({
              refreshArapListing: true,
            })
          );
          this.store.dispatch(
            InternalReceiptVoucherActions.setEditMode({ editMode: true })
          );
          this.draftStore.dispatch(
            ContraActions.loadContraInit({ guid_doc_1_hdr: entity.bl_fi_generic_doc_hdr.guid.toString() })
          );
          this.store.dispatch(InternalReceiptVoucherActions.editedGenDoc({edited: false}));
          this.store.dispatch(InternalReceiptVoucherActions.recalculateDocBalance());
          if(genDoc.bl_fi_generic_doc_hdr.guid_segment){
            this.store.dispatch(
              InternalReceiptVoucherActions.loadSegment({ guid: genDoc.bl_fi_generic_doc_hdr.guid_segment })
            );
          }
          if(genDoc.bl_fi_generic_doc_hdr.guid_project){
            this.store.dispatch(
              InternalReceiptVoucherActions.loadProject({ guid: genDoc.bl_fi_generic_doc_hdr.guid_project })
            );
          }
          if(genDoc.bl_fi_generic_doc_hdr.guid_profit_center){
            this.store.dispatch(
              InternalReceiptVoucherActions.loadProfitCenter({ guid: genDoc.bl_fi_generic_doc_hdr.guid_profit_center })
            );
          }
          if(genDoc.bl_fi_generic_doc_hdr.guid_dimension){
            this.store.dispatch(
              InternalReceiptVoucherActions.loadDimension({ guid: genDoc.bl_fi_generic_doc_hdr.guid_dimension })
            );
          }
          this.store.dispatch(
            InternalReceiptVoucherActions.selectAdjustSettlement({ guid: genDoc.bl_fi_generic_doc_hdr.guid })
          );
          this.store.dispatch(InternalReceiptVoucherActions.selectEditAdjustment({value: false}));
          let genDocLockDto = new GenericDocEditingLockDto();
          let created_by = localStorage.getItem('guid');

          genDocLockDto.generic_doc_hdr_guid = genDoc.bl_fi_generic_doc_hdr.guid.toString();

          this.subs.sink = this.genericDocLockService.checkDocument(genDocLockDto, AppConfig.apiVisa)
          .subscribe((genDocLockData: any) => {
            if (genDocLockData.code === "THIS_DOCUMENT_IS_LOCKED" && genDocLockData.data.bl_fi_generic_doc_editing_lock.created_by_subject_guid !== created_by && (genDoc.bl_fi_generic_doc_hdr.posting_status !== 'FINAL' || genDoc.bl_fi_generic_doc_hdr.posting_status !== 'VOID' || genDoc.bl_fi_generic_doc_hdr.posting_status !== 'DISCARDED')) {
              this.akaunGenDocLockDialogComponent = this.dialogRef.open(AkaunGenDocLockDialogComponent, { width: '400px' });
              this.akaunGenDocLockDialogComponent.componentInstance.warningMessage =
                'This document is currently being edited by <b>' +
                genDocLockData.data.bl_fi_generic_doc_editing_lock.created_by_name +
                '</b>. You can view it in read-only mode, but changes cannot be made until <b>' +
                genDocLockData.data.bl_fi_generic_doc_editing_lock.created_by_name +
                '</b> finishes editing and the document is unlocked.';
              this.akaunGenDocLockDialogComponent.afterClosed().subscribe((result) => {
                if (result === true) {
                  this.store.dispatch(InternalReceiptVoucherActions.lockDocument());
                  this.viewColFacade.updateInstance(this.index, {
                    ...this.localState,
                    deactivateAdd: true,
                    deactivateList: false,
                    selectedRow: response.data.bl_fi_generic_doc_hdr.guid,
                  });
                  this.viewColFacade.onNextAndReset(this.index, 2);
                }
              });
            } else {
              this.viewColFacade.updateInstance(this.index, {
                ...this.localState,
                deactivateAdd: true,
                deactivateList: false,
                selectedRow: response.data.bl_fi_generic_doc_hdr.guid,
              });
              this.viewColFacade.onNextAndReset(this.index, 2);
            }
          })
        });
    }
  }

  disableDiscardButtonStatus(selectedRows) {
    if (!selectedRows || selectedRows.length === 0) {
      this.disableDiscardButton = true;
    } else {
      this.disableDiscardButton = !selectedRows.every(row => row.bl_fi_generic_doc_hdr.posting_status === "DRAFT");
    }
    this.cdr.detectChanges();
  }

  onDiscard() {
    const selectedRows: any[] = this.gridApi.getSelectedRows();
    const discardPileGuids = selectedRows
      .filter(
        (row) =>
          (!row.bl_fi_generic_doc_hdr.posting_status ||
            row.bl_fi_generic_doc_hdr.posting_status === "DRAFT") &&
          row.bl_fi_generic_doc_hdr.status == "ACTIVE"
      )
      .map((row) => row.bl_fi_generic_doc_hdr?.guid.toString());
    console.log("discardPileGuids", discardPileGuids);
    if (discardPileGuids.length > 0) {
      this.akaunConfirmationDialogComponent = this.dialogRef.open(AkaunConfirmationDialogComponent, { width: '400px' });
      this.akaunConfirmationDialogComponent.componentInstance.confirmMessage = 'Are you sure you want to DISCARD selected documents?';
      this.akaunConfirmationDialogComponent.afterClosed().subscribe((result) => {
      if (result === true) {
       this.store.dispatch(
        InternalReceiptVoucherActions.discardInit({ guids: discardPileGuids })
      );
    }
  })
    }
  }

  onSelectionChanged(event) {
    this.disableVoidButtonStatus(event.api.getSelectedRows())
    this.disableFinalButtonStatus(event.api.getSelectedRows())
    this.disableDiscardButtonStatus(event.api.getSelectedRows())
  }

  disableFinalButtonStatus(selectedRows) {
    if (!selectedRows || selectedRows.length === 0) {
      this.disableFinalButton = true;
    } else {
      this.disableFinalButton = !selectedRows.every(row => row.bl_fi_generic_doc_hdr.posting_status === "DRAFT");
    }
    this.cdr.detectChanges();
  }

  onFinal() {
    let selectedRows;
    let arr = [];
    const json = {
      posting_status: "FINAL",
    };
    selectedRows = this.gridApi.getSelectedRows();
    const observables = selectedRows.map((row) =>
      this.checkPaymentError(row).pipe(
        map((hasInvalidPayment) => {
          if (hasInvalidPayment) {
            return (
              row.bl_fi_generic_doc_hdr.server_doc_1 +
              " Invalid payment."
            );
          } else {
            arr.push(row);
            return null;
          }
        })
      )
    );
    forkJoin(observables).subscribe((errorMessages) => {
      const filteredErrorMessages = errorMessages.filter(
        (message) => message !== null
      );
      if (filteredErrorMessages.length) {
        const docList = filteredErrorMessages.join("<br/> ");
        this.toastr.error(
          "Unable to post the document to FINAL: <br/>" + docList,
          "Error",
          {
            tapToDismiss: true,
            progressBar: true,
            timeOut: 1300,
            enableHtml: true,
            closeButton: false,
          }
        );
      } else {
        arr = arr.filter(
          (x) =>
            x.bl_fi_generic_doc_hdr.posting_status !== "FINAL" &&
            x.bl_fi_generic_doc_hdr.status == "ACTIVE"
        );
        arr.forEach((e) => {
          console.log("e: ", e);
          let temp: GenericDocContainerModel = {
            bl_fi_generic_doc_hdr: e.bl_fi_generic_doc_hdr,
            bl_fi_generic_doc_event: e.bl_fi_generic_doc_event,
            bl_fi_generic_doc_ext: e.bl_fi_generic_doc_ext,
            bl_fi_generic_doc_link: e.bl_fi_generic_doc_link,
            bl_fi_generic_doc_line: e.bl_fi_generic_doc_line,
          };
          console.log("Gen doc container: ", temp);
          this.store.dispatch(
            InternalReceiptVoucherActions.updatePostingStatus({
              status: json,
              doc: temp,
            })
          );
        });
        this.gridApi.refreshServerSideStore();
      }
    });
    if(this.appletSettings.ENABLE_AUTO_PRINT) {
      this.onPrintSelected();
    }
  }
  checkPaymentError(genDoc: any) {
    let hasInvalidPayment = false;
    if(!genDoc.bl_fi_generic_doc_line.filter(line=>line.status==='ACTIVE' && line.txn_type==='STL_MTHD').length){
      hasInvalidPayment = true;
    }
    return of(hasInvalidPayment);
  }
  disableVoidButtonStatus(selectedRows) {
    if (!selectedRows || selectedRows.length === 0) {
      this.disableVoidButton = true;
    } else {
      this.disableVoidButton = !selectedRows.every(row => row.bl_fi_generic_doc_hdr.posting_status === "FINAL");
    }
    this.cdr.detectChanges();
  }

  onVoid() {
    let selectedRows;
    let arr = [];
    const json = {
      posting_status: "FINAL",
    };
    selectedRows = this.gridApi.getSelectedRows();
    selectedRows.map((row) => {
      arr.push(row);
    });
    arr = arr.filter(
      (x) =>
        x.bl_fi_generic_doc_hdr.posting_status === "FINAL" &&
        x.bl_fi_generic_doc_hdr.status === "ACTIVE"
    );
    this.akaunConfirmationDialogComponent = this.dialogRef.open(AkaunConfirmationDialogComponent, { width: '400px' });
    this.akaunConfirmationDialogComponent.componentInstance.confirmMessage = 'Are you sure you want to VOID selected documents?';
    this.akaunConfirmationDialogComponent.afterClosed().subscribe((result) => {
      if (result === true) {
    arr.forEach((e) => {
      let temp: GenericDocContainerModel = {
        bl_fi_generic_doc_hdr: e.bl_fi_generic_doc_hdr,
        bl_fi_generic_doc_event: e.bl_fi_generic_doc_event,
        bl_fi_generic_doc_ext: e.bl_fi_generic_doc_ext,
        bl_fi_generic_doc_link: e.bl_fi_generic_doc_link,
        bl_fi_generic_doc_line: e.bl_fi_generic_doc_line,
      };
      this.store.dispatch(
        InternalReceiptVoucherActions.voidReceiptVoucherInit({
          status: json,
          doc: temp,
        })
      );
    });
  }
})
    this.gridApi.refreshServerSideStore();
  }

  getRowStyle = params => {
    if (params.node.footer) {
      return { fontWeight: 'bold', background: '#e6f7ff' };
    }
    if (params.node.group) {
      return { fontWeight: 'bold' };
    }
    if (params.data.bl_fi_generic_doc_hdr?.forex_doc_hdr_guid) {
      return { background: '#ff4500' };
    }
  }

  onMorePage() {
    console.log('on more page click');
    if (this.rowData.length < this.totalRecords) {
      this.createData();
    }
  }

  onAllPage() {
    this.createData(true);
  }

  // setColumnVisibilityFromSettings() {
  //   const columnApi = this.gridOptions.columnApi;
  //   const api = this.gridOptions.api;
  //   this.showColumns.forEach(showColumn => {
  //     const column = columnApi.getColumn(showColumn.name);
  //     if (column) {
  //       const isShowColumn = this.isShowColumn(showColumn.setting, showColumn.permission);
  //       this.setColumnVisible(api, columnApi, column, isShowColumn);
  //     }
  //   });
  // }
  ngOnDestroy() {
    this.subs.unsubscribe();
  }

  ngAfterViewInit() {

    this.subs.sink = this.sessionStore.select(SessionSelectors.selectPersonalSettings).subscribe(data=>{
      this.viewModelStore.dispatch(Column1ViewModelActions.setReceiptVoucherListing_State({receiptVoucherListingState:data.receiptVoucherListingState}));
      if (!this.appletSettings?.['DISABLE_DEFAULT_SETTING_ADVANCE_SEARCH']) {
        const key = 'receipt-voucher-listingAdvancedSearch';
        const relativeData = transformAllDateRangesToRelative(data[key]);
        const absoluteData = transformRelativeDatesToAbsolute(relativeData);
        this.setAdvFormValue(absoluteData);
      }
    });
    // Check if there's a saved column state in MVVM
    let serializedColumnState;
    this.subs.sink = this.viewModelStore.select(Column1ViewSelectors.selectReceiptVoucherListing_State).subscribe(data=>{
      if(data){
        serializedColumnState=data;
      }
    })
    if (serializedColumnState) {
      // this.setColumnVisibilityFromSettings();
      const newColumnState = JSON.parse(serializedColumnState);

      const currentColumnState = this.gridOptions.columnApi.getColumnState();
      const currentColumnIds = new Set(currentColumnState.map(column => column.colId));
      const hiddenColumns = {};
      currentColumnState.forEach(column => {
        if (column.hide) {
          hiddenColumns[column.colId] = true;
        }
      });
      const filteredNewColumnState = newColumnState.filter(column => currentColumnIds.has(column.colId));
      const combinedColumnState = filteredNewColumnState.map(column => {
        if (hiddenColumns[column.colId] !== undefined) {
          column.hide = hiddenColumns[column.colId];
        }
        return column;
      });

      // Apply the combined column state forcefully after a delay
      setTimeout(() => {
        this.gridOptions.columnApi.applyColumnState({
          state: combinedColumnState,
          applyOrder: true, // Set this to true to forcefully apply the state
        });
        UtilitiesModule.autoSizeAllColumns(this.gridOptions);
      }, 2000);
    }
    else {
      UtilitiesModule.autoSizeAllColumns(this.gridOptions);
    }
  }
  saveColumnStateToBackend(columnApi) {
    const columnState = columnApi.getColumnState();
    const serializedColumnState = JSON.stringify(columnState);
    this.sessionStore.dispatch(SessionActions.saveColumnStateInit({ settings: {receiptVoucherListingState:serializedColumnState} }));
  }

  getOrderBy(value) {
    const match = this.dateMappings.find(mapping => mapping.display === value);
    return match ? match.key : 'date_txn';
  }

  getOrderBy2(value) {
    const match = this.dateMappings.find(mapping => mapping.key === value);
    return match ? match.display : 'TRANSACTION DATE';
  }

  getDefaultDate() {
    let defaultDate = UtilitiesModule.getDateNoTime(moment().subtract(1, 'month'));
    if (this.appletSettings?.DEFAULT_TRANSACTION_DATE === "1_week") {
      defaultDate = UtilitiesModule.getDateNoTime(moment().subtract(1, 'week'));
    } else if (this.appletSettings?.DEFAULT_TRANSACTION_DATE === "1_day") {
      defaultDate = UtilitiesModule.getTodayNoTime();
    }
    return defaultDate;
  }

  getPostingStatus() {
    return this.appletSettings?.DEFAULT_POSTING_STATUS === 'ALL'
      ? []
      : (this.appletSettings?.DEFAULT_POSTING_STATUS
        ? [this.appletSettings?.DEFAULT_POSTING_STATUS] : []);
  }

  getStatus() {
    return (this.appletSettings?.DEFAULT_STATUS === 'ALL' ||
      this.appletSettings?.DEFAULT_STATUS === true)
      ? []
      : [(this.appletSettings?.DEFAULT_STATUS ?? 'ACTIVE')];
  }

  getFuzzySearch() {
    let columns = 'server_doc_1,client_doc_1,client_doc_2,client_doc_3,client_doc_4,client_doc_5,doc_reference';
    if (this.appletSettings?.FUZZY_SEARCH_COLUMNS) {
      columns = this.appletSettings?.FUZZY_SEARCH_COLUMNS;
    }
    return columns.split(',');
  }

  setAdvFormValue(e?) {
    // Make sure the child component is initialized before setting values
    if (this.advancedSearch) {
      this.searchQuery = {
        isEmpty: true,
        queryString: '',
      };

      const newFormValue = {
        postingStatus: this.advancedSearch.advForm.get('postingStatus')?.value ? this.advancedSearch.advForm.get('postingStatus')?.value : this.getPostingStatus(),
        status: this.advancedSearch.advForm.get('status')?.value ? this.advancedSearch.advForm.get('status')?.value : this.getStatus(),
        orderBy: this.getOrderBy2(this.appletSettings.SORT_ORDER)
      };
      this.advancedSearch.advForm.patchValue(newFormValue);

      const sortOrder = this.appletSettings.SORT_ORDER;
      if (sortOrder === 'created_date') {
        this.advancedSearch.advForm.get('createdDateCheckbox')?.patchValue(true);
      }
      else if (sortOrder === 'updated_date') {
        this.advancedSearch.advForm.get('updatedDateCheckbox')?.patchValue(true);
      }
      else if (sortOrder === 'date_txn') {
        this.advancedSearch.advForm.get('transactionDateCheckbox')?.patchValue(true);
      }

      if (this.advancedSearch.advForm.get('transactionDateCheckbox')?.value) {
        const transactionDateFrom = this.advancedSearch.advForm.get('transactionDate.from');
        if (!transactionDateFrom?.value) {
          transactionDateFrom?.patchValue(this.getDefaultDate());
        }
      }

      if (this.advancedSearch.advForm.get('createdDateCheckbox')?.value) {
        const createdDateFrom = this.advancedSearch.advForm.get('createdDate.from');
        if (!createdDateFrom?.value) {
          createdDateFrom?.patchValue(this.getDefaultDate());
        }
      }

      if (this.advancedSearch.advForm.get('updatedDateCheckbox')?.value) {
        const updatedDateFrom = this.advancedSearch.advForm.get('updatedDate.from');
        if (!updatedDateFrom?.value) {
          updatedDateFrom?.patchValue(this.getDefaultDate());
        }
      }
      this.searchQuery.queryString = e;
    }
  }
}
