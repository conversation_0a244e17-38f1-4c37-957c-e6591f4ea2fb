import {
  AppletSettingFilterItemCategoryLinkContainerModel,
  bl_fi_generic_doc_line_RowClass,
  EntityContainerModel,
  GenericDocContainerModel,
  GenericDocARAPContainerModel,
  PricingSchemeLinkContainerModel,
  bl_fi_generic_doc_hdr_RowClass,
  LabelListContainerModel,
  FinancialItemContainerModel
} from 'blg-akaun-ts-lib';

export interface PurchaseInvoiceState {
  totalRecords: number;
  selectedInvoice: GenericDocContainerModel;
  selectedEntity: EntityContainerModel;
  selectedEntityBranch: null,
  selectedLineItem: any;
  selectedPricingScheme: any;
  selectedPayment: bl_fi_generic_doc_line_RowClass;
  selectedContraDoc: GenericDocContainerModel;
  selectedContraLink: GenericDocARAPContainerModel;
  selectedAttachmentGuid: string;
  selectedPrintableFormatGuid: string;
  selectedPrintableFormatGuids: string[];
  selectedMode: string;
  pricingSchemeLink: PricingSchemeLinkContainerModel[];
  updateAgGrid: boolean;
  knockoffListingConfig: any;
  selectedCompGuid:string;
  loadedGenDocs: GenericDocContainerModel[];
  refreshGenDocListing: boolean;
  editMode: boolean;
  selectedGuid: string;
  intercompanySalesInvoices: any[];
  selectedInvItem: any;
  loadedArap : any;
  refreshArapListing : boolean;
  updateContraAgGrid: boolean;
  errorLog: {timeStamp: Date, log: string}[];
  totalContraRecords: number;
  totalRevenue : any;
  totalExpense : any;
  totalSettlement : any;
  totalContra: any;
  docOpenAmount : any;
  docArapBalance : any;
  disableCreate: boolean;
  editedInvoice: boolean;
  koAttachments: any;
  delimeter: string;
  createdTempDoc: GenericDocContainerModel;
  convertActionDispatched: boolean;
  addedContraDoc: GenericDocARAPContainerModel;
  cloneGenericDocHdrGuids: string[];
  disableCloneBtn: boolean;
  cloneSourceGenDocHdr: bl_fi_generic_doc_hdr_RowClass;
  eInvoiceEnabled: boolean;
  docLink: any;
  pnsLevel2: any[];
  isEinvoiceSubmissionAnotherSupplier: boolean;
  selectedSegment: any;
  selectedProject: any;
  selectedProfitCenter: any;
  selectedDimension: any;
  selectedCOA: any;
  departmentMode: any;
  selectedSegmentLine: any;
  selectedProjectLine: any;
  selectedProfitCenterLine: any;
  selectedDimensionLine: any;
  copyDepartmentFromHdr:boolean;
  resetExpansionPanel: boolean;
  delivery_date_expected: any;
  childItems: any[];
  selectedPricingSchemeHdr: string;
  intercompanyBranches: any[];
  validIntercompanyBranch: any;
  selectedIntercompanyBranch: any;
  selectedItemCategoryFilter: AppletSettingFilterItemCategoryLinkContainerModel[];
  categoryGroupList: LabelListContainerModel[];
  selectedCompany: any;
  selectedBranch: any;
  docLock:boolean;
  rowData: any[];
  searchItemRowData: any[];
  searchItemTotalRecords: number;
  firstLoadListing: boolean;
  resetKOVertical: boolean;
  updateMatchedHistoryAgGrid: boolean;
  totalRecordsMatchedHistory: number;
  selectedDocForKO: string;
  searchChildItemRowData: any[];
  searchChildItemTotalRecords: number;
  isTradeIn: boolean;
  pricingSchemeGuid : any;
  roundingItem: FinancialItemContainerModel;
  roundingFiveCent: boolean;
  groupDiscountItem: FinancialItemContainerModel;
  groupDiscountPercentage: number;
}

export const initState: PurchaseInvoiceState = {
  totalRecords: 0,
  selectedInvoice: null,
  selectedEntity: null,
  selectedEntityBranch: null,
  selectedLineItem: null,
  selectedPricingScheme: null,
  selectedPayment: null,
  selectedContraDoc: null,
  selectedContraLink: null,
  selectedAttachmentGuid: null,
  selectedPrintableFormatGuid: null,
  selectedPrintableFormatGuids: null,
  selectedMode: null,
  pricingSchemeLink: null,
  updateAgGrid: false,
  knockoffListingConfig: null,
  selectedCompGuid:null,
  loadedGenDocs: null,
  refreshGenDocListing: false,
  editMode: false,
  selectedGuid: null,
  intercompanySalesInvoices:null,
  selectedInvItem: null,
  loadedArap: null,
  refreshArapListing : true,
  updateContraAgGrid: false,
  errorLog: [],
  totalContraRecords: 0,
  totalRevenue: 0,
  totalExpense: 0,
  totalSettlement: 0,
  totalContra : 0,
  docOpenAmount : 0,
  docArapBalance : 0,
  disableCreate: true,
  editedInvoice: false,
  koAttachments: [],
  delimeter: null,
  createdTempDoc: null,
  convertActionDispatched: false,
  addedContraDoc: null,
  cloneGenericDocHdrGuids: [],
  disableCloneBtn: false,
  cloneSourceGenDocHdr: null,
  eInvoiceEnabled: false,
  docLink: null,
  pnsLevel2: null,
  isEinvoiceSubmissionAnotherSupplier: false,
  selectedSegment: null,
  selectedProject: null,
  selectedProfitCenter: null,
  selectedDimension: null,
  selectedCOA: null,
  departmentMode: null,
  selectedSegmentLine: null,
  selectedProjectLine: null,
  selectedProfitCenterLine: null,
  selectedDimensionLine: null,
  copyDepartmentFromHdr: true,
  resetExpansionPanel: null,
  delivery_date_expected: null,
  childItems: [],
  selectedPricingSchemeHdr: null,
  intercompanyBranches: [],
  validIntercompanyBranch: null,
  selectedIntercompanyBranch: null,
  selectedItemCategoryFilter: [],
  categoryGroupList: [],
  selectedCompany: null,
  selectedBranch: null,
  docLock: false,
  rowData: [],
  searchItemRowData: [],
  searchItemTotalRecords: 0,
  firstLoadListing: true,
  resetKOVertical: false,
  updateMatchedHistoryAgGrid: false,
  totalRecordsMatchedHistory: 0,
  selectedDocForKO: null,
  searchChildItemRowData: [],
  searchChildItemTotalRecords: 0,
  isTradeIn: false,
  pricingSchemeGuid : null,
  roundingItem: null,
  roundingFiveCent: null,
  groupDiscountItem: null,
  groupDiscountPercentage: null,
};
