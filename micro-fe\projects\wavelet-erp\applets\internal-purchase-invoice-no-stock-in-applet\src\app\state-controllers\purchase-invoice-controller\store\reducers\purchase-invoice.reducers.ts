import { Action, createReducer, on } from '@ngrx/store';
import { PurchaseInvoiceActions } from '../actions';
import { initState, PurchaseInvoiceState } from '../states/purchase-invoice.states';

export const PurchaseInvoiceFeatureKey = 'purchaseInvoice';

export const PurchaseInvoiceReducer = createReducer(
  initState,
  on(PurchaseInvoiceActions.loadBranchCompanySuccess, (state, action) => ({
    ...state,
    selectedBranch: action.branch,
    selectedCompany: action.company,
    pricingSchemeHdr: action.branch?.bl_fi_mst_branch?.default_pricing_scheme,
    selectedCOA: action.company?.bl_fi_mst_comp?.chart_of_acc_guid
                   ? action.company?.bl_fi_mst_comp?.chart_of_acc_guid
                   : null,
    eInvoiceEnabled: action.company?.bl_fi_mst_comp?.einvoice_status === 'ENABLED' ? true : false,
  })),
  on(PurchaseInvoiceActions.selectPricingSchemeHdr, (state, action) => ({
    ...state, selectedPricingSchemeHdr: action.pricingSchemeHdr
  })),
  on(PurchaseInvoiceActions.updateChildItem, (state, action) => ({
    ...state,
    childItems: state.childItems.map(obj =>
      obj.guid === action.child.guid ? { ...obj, ...action.child } : obj
    )
  })),
  on(PurchaseInvoiceActions.selectChildItem, (state, action) => ({
    ...state, childItems: action.child
  })),
  on(PurchaseInvoiceActions.selectCopyDepartmentFromHdr, (state, action) => ({
    ...state, copyDepartmentFromHdr:  action.value
  })),
  on(PurchaseInvoiceActions.selectSegmentLine, PurchaseInvoiceActions.loadSegmentLineSuccess, (state, action) => ({
    ...state, selectedSegmentLine:  action.segment
  })),
  on(PurchaseInvoiceActions.selectProjectLine, PurchaseInvoiceActions.loadProjectLineSuccess, (state, action) => ({
    ...state, selectedProjectLine:  action.project
  })),
  on(PurchaseInvoiceActions.selectProfitCenterLine, PurchaseInvoiceActions.loadProfitCenterLineSuccess, (state, action) => ({
    ...state, selectedProfitCenterLine:  action.profitCenter
  })),
  on(PurchaseInvoiceActions.selectDimensionLine, PurchaseInvoiceActions.loadDimensionLineSuccess, (state, action) => ({
    ...state, selectedDimensionLine:  action.dimension
  })),
  on(PurchaseInvoiceActions.selectDepartmentMode, (state, action) => ({
    ...state, departmentMode:  action.mode
  })),
  on(PurchaseInvoiceActions.selectCOA, (state, action) => ({
    ...state, selectedCOA:  action.coa
  })),
  on(PurchaseInvoiceActions.selectSegment, PurchaseInvoiceActions.loadSegmentSuccess, (state, action) => ({
    ...state, selectedSegment:  action.segment
  })),
  on(PurchaseInvoiceActions.selectProject, PurchaseInvoiceActions.loadProjectSuccess, (state, action) => ({
    ...state, selectedProject:  action.project
  })),
  on(PurchaseInvoiceActions.selectProfitCenter, PurchaseInvoiceActions.loadProfitCenterSuccess, (state, action) => ({
    ...state, selectedProfitCenter:  action.profitCenter
  })),
  on(PurchaseInvoiceActions.selectDimension, PurchaseInvoiceActions.loadDimensionSuccess, (state, action) => ({
    ...state, selectedDimension:  action.dimension
  })),
  on(PurchaseInvoiceActions.selectInvItem, (state, action) => ({
    ...state,
    selectedInvItem: action.invItem
  })),
  on(PurchaseInvoiceActions.loadPurchaseInvoiceSuccess, (state, action) => ({
    ...state,
    loadedGenDocs: action.purchaseInvoice
  })),
  on(PurchaseInvoiceActions.editMode, (state, action) => ({
    ...state,
    editMode: true
  })),
  on(PurchaseInvoiceActions.createPurchaseInvoiceSuccess, (state, action) => ({
    ...state,
    refreshGenDocListing: true,  koAttachments:[]
  })),
  on(PurchaseInvoiceActions.editPurchaseInvoiceSuccess, (state, action) => ({
    ...state,
    refreshGenDocListing: true,
    editMode: false,
    convertActionDispatched: false
  })),
  on(PurchaseInvoiceActions.deletePurchaseInvoiceSuccess, (state, action) => ({
    ...state,
    refreshGenDocListing: true,
    editMode: false
  })),
  on(PurchaseInvoiceActions.selectPurchaseInvoice, (state, action) => ({
    ...state,
    selectedInvoice: action.genDoc,
    cloneGenericDocHdrGuids: [],
    cloneSourceGenDocHdr: null,
  })),
  on(PurchaseInvoiceActions.selectEntity, (state, action) => ({
    ...state,
    selectedEntity: action.entity.entity
  })),
  on(PurchaseInvoiceActions.selectEntityBranch, (state, action) => ({
    ...state,
    selectedEntityBranch: action.entityBranch
  })),
  on(PurchaseInvoiceActions.selectEntityOnEdit, (state, action) =>
    ({ ...state, selectedEntity: action.entity.entity })),
  on(PurchaseInvoiceActions.selectLineItem, (state, action) => ({
    ...state,
    selectedLineItem: action.lineItem
  })),
  on(PurchaseInvoiceActions.selectPricingScheme, (state, action) => ({
    ...state,
    selectedPricingScheme: action.pricingScheme
  })),
  on(PurchaseInvoiceActions.selectPricingSchemeLinkSuccess, (state, action) => ({
    ...state,
    pricingSchemeLink: action.pricing
  })),
  on(PurchaseInvoiceActions.selectPayment, (state, action) => ({
    ...state,
    selectedPayment: action.payment
  })),
  on(PurchaseInvoiceActions.selectContraDoc, (state, action) => ({
    ...state,
    selectedContraDoc: action.entity
  })),
  on(PurchaseInvoiceActions.selectContraLink, (state, action) => ({
    ...state,
    selectedContraLink: action.link
  })),
  on(PurchaseInvoiceActions.selectAttachmentGuid, (state, action) => ({
    ...state,
    selectedAttachmentGuid: action.guid
  })),
  on(PurchaseInvoiceActions.selectMode, (state, action) => ({
    ...state,
    selectedMode: action.mode
  })),
  on(PurchaseInvoiceActions.printJasperPdfInit, (state, action) => ({
    ...state,
    selectedPrintableFormatGuid: action.guid
  })),
  on(PurchaseInvoiceActions.printMultipleJasperPdfInit, (state, action) => ({
    ...state,
    selectedPrintableFormatGuids: action.guids
  })),
  on(PurchaseInvoiceActions.resetAgGrid, (state, action) => ({
    ...state,
    refreshGenDocListing: false
  })),
  on(PurchaseInvoiceActions.updatePostingStatusSuccess, (state, action) => ({
    ...state, refreshGenDocListing: true, convertActionDispatched: false
  })),
  on(PurchaseInvoiceActions.updateKnockoffListingConfig, (state, action) => ({
    ...state,
    knockoffListingConfig: action.settings
  })),
  on(PurchaseInvoiceActions.selectCompanyGuid, (state, action) => ({
    ...state,
    selectedCompGuid: action.compGuid
  })),
  on(PurchaseInvoiceActions.setEditMode, (state, action) => ({
        ...state, editMode: action.editMode
      })),
  on(PurchaseInvoiceActions.loadIntercompanySalesInvoiceSuccess, (state, action) => ({
    ...state, intercompanySalesInvoices: action.salesInvoices
  })),
  on(PurchaseInvoiceActions.createIntercompanyTransactionsSuccess, (state, action) => ({
    ...state, refreshGenDocListing: true
  })),
  on(PurchaseInvoiceActions.refreshArapListing, (state, action) => ({...state, refreshArapListing: action.refreshArapListing})),
  on(
    PurchaseInvoiceActions.loadArapListingSuccess,
    (state, action) => ({ ...state, loadedArap: action.arapListing })
  ),
  on(PurchaseInvoiceActions.updateAgGridDone, (state, action) => ({...state, updateContraAgGrid: action.done})),
  on(PurchaseInvoiceActions.selectTotalRevenue, (state, action) => ({...state, totalRevenue: action.totalRevenue})),
  on(PurchaseInvoiceActions.selectTotalExpense, (state, action) => ({...state, totalExpense: action.totalExpense})),
  on(PurchaseInvoiceActions.selectTotalSettlement, (state, action) => ({...state, totalSettlement: action.totalSettlement})),

  on(PurchaseInvoiceActions.selectTotalContra, (state, action) => ({...state, totalContra: action.totalContra})),
  on(PurchaseInvoiceActions.selectDocOpenAmount, (state, action) => ({...state, docOpenAmount: action.docOpenAmount})),
  on(PurchaseInvoiceActions.selectDocArapBalance, (state, action) => ({...state, docArapBalance: action.docArapBalance})),
  on(PurchaseInvoiceActions.discardComplete, (state, action) => ({ ...state, refreshGenDocListing: action.successCount > 0 ? true : false })),
  on(PurchaseInvoiceActions.voidPurchaseInvoiceSuccess, (state, action) => ({
    ...state, refreshGenDocListing: true
  })),
  on(PurchaseInvoiceActions.resetContra, (state, action) => ({
    ...state,
    totalContraRecords: 0,
    totalContra : 0,
    docOpenAmount : 0,
    docArapBalance : 0,
    loadedArap : null
  })),
  on(PurchaseInvoiceActions.disableCreate, (state, action) => ({
    ...state, disableCreate: action.disable
  })),
  on(PurchaseInvoiceActions.editedInvoice, (state, action) => ({
    ...state, editedInvoice: action.edited
  })),
  on(PurchaseInvoiceActions.setKOAttachments, (state, action) => ({
    ...state, koAttachments: [...state.koAttachments, action.attachments],
  })),
  on(PurchaseInvoiceActions.setDelimeter, (state, action) => ({
    ...state, delimeter: action.delimeter,
  })),
  on(PurchaseInvoiceActions.getTotalRecordsSuccess, (state, action) => ({ ...state, totalRecords: action.totalRecords })),
  on(PurchaseInvoiceActions.createTempPurchaseInvoiceSuccess, (state, action) =>
  ({ ...state, createdTempDoc: action.response, koAttachments:[] })),
  on(PurchaseInvoiceActions.convertToActiveSuccess, (state, action) => ({
    ...state, refreshGenDocListing: true, koAttachments: [], convertActionDispatched: true
  })),
  on(PurchaseInvoiceActions.resetconvertActionDispatchedState, (state, action) => ({
    ...state, convertActionDispatched: false
  })),
  on(PurchaseInvoiceActions.resetDraft, (state) => ({ ...state, convertActionDispatched:false, docLock: false, groupDiscountPercentage: null  })),
  on(PurchaseInvoiceActions.addContra, (state, action) => ({
    ...state, addedContraDoc: action.contraDoc
  })),
  on(PurchaseInvoiceActions.discardComplete, (state, action) => ({ ...state, refreshGenDocListing: action.successCount > 0 ? true : false })),
  on(PurchaseInvoiceActions.updateToSelfBilledInvoiceSuccess, (state, action) => ({
    ...state, refreshGenDocListing: true
  })),
  on(PurchaseInvoiceActions.cloneDocumentInit, (state, action) => ({ ...state, disableCloneBtn: true })),
  on(PurchaseInvoiceActions.cloneDocumentSuccess, (state, action) => ({ ...state, cloneGenericDocHdrGuids: [...state.cloneGenericDocHdrGuids, action.dto?.cloneGenericDocHdrGuid?.toString() ]})),
  on(PurchaseInvoiceActions.pollClonedDocumentSuccess, (state, action) => ({ ...state, refreshGenDocListing: true, disableCloneBtn: false })),
  on(PurchaseInvoiceActions.pollClonedDocumentFail, (state, action) => ({ ...state, disableCloneBtn: false })),
  on(PurchaseInvoiceActions.loadCloneSourceGenDocHdrSuccess, (state, action) => ({ ...state, cloneSourceGenDocHdr: action.hdr })),
  on(PurchaseInvoiceActions.selectEInvoiceEnabled, (state, action) => ({
    ...state, eInvoiceEnabled:  action.val
  })),

  on(PurchaseInvoiceActions.selectDocLink, (state, action) => ({
    ...state, docLink: action.docLink
  })),

  on(PurchaseInvoiceActions.selectPnsLevel2, (state, action) => ({
    ...state, pnsLevel2: action.pnsLevel2
  })),
  on(PurchaseInvoiceActions.setIsEinvoiceSubmissionAnotherSupplier, (state, action) => ({
    ...state, isEinvoiceSubmissionAnotherSupplier: action.isEinvoiceSubmissionAnotherSupplier
  })),
  on(PurchaseInvoiceActions.resetExpansionPanel, (state, action) => ({
    ...state, resetExpansionPanel: action.resetIndex
  })),
  on(PurchaseInvoiceActions.selectSettingItemFilterSuccess, (state, action) =>
    ({...state, selectedItemCategoryFilter:action.setting})),
  on(PurchaseInvoiceActions.resetSettingItemFilter, (state, action) =>
    ({...state, selectedItemCategoryFilter:[]})),
  on(PurchaseInvoiceActions.lockDocument, (state, action) => ({
    ...state, docLock: true
  })),

  on(PurchaseInvoiceActions.selectRowData, (state, action) =>
    ({...state, rowData: action.rowData})),
  on(PurchaseInvoiceActions.selectTotalRecords, (state, action) =>
    ({...state, totalRecords: action.totalRecords})),
  on(PurchaseInvoiceActions.selectSearchItemRowData, (state, action) =>
    ({...state, searchItemRowData: action.rowData})),
  on(PurchaseInvoiceActions.selectSearchItemTotalRecords, (state, action) =>
    ({...state, searchItemTotalRecords: action.totalRecords})),
  on(PurchaseInvoiceActions.selectFirstLoadListing, (state, action) =>
    ({...state, firstLoadListing: action.firstLoadListing})),
  on(PurchaseInvoiceActions.resetKOForVertical, (state, action) =>
    ({...state, resetKOVertical: action.reset})),
  on(PurchaseInvoiceActions.updateMatchedHistoryAgGridDone, (state, action) => ({
    ...state, updateMatchedHistoryAgGrid: action.done
  })),
  on(PurchaseInvoiceActions.loadMatchedHistorySuccess, (state, action) => ({ ...state, totalRecords: action.totalRecords })),
  on(PurchaseInvoiceActions.selectDocumentForKO, (state, action) => ({
    ...state, selectedDocForKO: action.docGuid
  })),
  on(PurchaseInvoiceActions.updateContraSuccess, (state, action) => ({
    ...state,
    refreshArapListing: true
  })),
  on(PurchaseInvoiceActions.selectSearchChildItemRowData, (state, action) => {
    return {
      ...state,
      searchChildItemRowData: action.rowData
    };
  }),
  on(PurchaseInvoiceActions.selectSearchChildItemTotalRecords, (state, action) =>
    ({...state, searchChildItemTotalRecords: action.totalRecords})),

  on(PurchaseInvoiceActions.selectTradeInEnabled, (state, action) =>
    ({...state, isTradeIn: action.isTradeIn})),

  on(PurchaseInvoiceActions.selectItemType, (state, action) =>
    ({...state, itemTxnType: action.itemTxnType})),

  on(PurchaseInvoiceActions.selectPricingSchemeGuid, (state, action) =>
    ({...state, pricingSchemeGuid: action.guid})),

  on(PurchaseInvoiceActions.addGroupDiscountSuccess, (state, action) =>
  ({ ...state, groupDiscountPercentage: action.discPercentage })),
  on(PurchaseInvoiceActions.resetRoundingGroupDiscountItem, (state, action) =>
  ({ ...state, groupDiscountItem: null,roundingItem: null,roundingFiveCent: null })),
  on(PurchaseInvoiceActions.loadGroupDiscountItemSuccess, (state, action) =>
  ({ ...state, groupDiscountItem: action.item})),
  on(PurchaseInvoiceActions.loadRoundingItemSuccess, (state, action) =>
  ({ ...state, roundingItem: action.item})),
  on(PurchaseInvoiceActions.selectRoundingItem, (state, action) =>
  ({ ...state, roundingItem: action.item})),
  );

export function reducer(state: PurchaseInvoiceState | undefined, action: Action) {
  return PurchaseInvoiceReducer(state, action);
}
