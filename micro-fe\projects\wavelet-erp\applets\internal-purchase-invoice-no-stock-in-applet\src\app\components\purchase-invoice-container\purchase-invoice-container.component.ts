import {
  Component, ComponentFactoryResolver, OnDestroy, OnInit, ViewChild,
} from '@angular/core';
import { Store } from '@ngrx/store';
import { containerAnim } from 'projects/shared-utilities/modules/layout/animations/animations';
import { containerTemplate } from 'projects/shared-utilities/modules/layout/container';
import { PermissionStates } from "projects/shared-utilities/modules/permission/permission-controller";
import { UserPermInquirySelectors } from "projects/shared-utilities/modules/permission/user-permissions-inquiry-controller/selectors";
import { SessionActions } from 'projects/shared-utilities/modules/session/session-controller/actions';
import { SessionSelectors } from 'projects/shared-utilities/modules/session/session-controller/selectors';
import { SessionStates } from 'projects/shared-utilities/modules/session/session-controller/states';
import { FirstColumnDirective } from 'projects/shared-utilities/utilities/first-column.directive';
import { SecondColumnDirective } from 'projects/shared-utilities/utilities/second-column.directive';
import { ViewColumn } from 'projects/shared-utilities/view-column';
import { ViewColumnComponent } from 'projects/shared-utilities/view-column.component';
import { AppConfig } from "projects/shared-utilities/visa";
import { map } from "rxjs/operators";
import { SubSink } from 'subsink2';
import { ViewColumnFacade } from '../../facades/view-column.facade';
import { AppletSettings } from '../../models/applet-settings.model';
import { PurchaseInvoicePagesService } from '../../services/purchase-invoice-pages.service';
import { Column1ViewModelActions } from "../../state-controllers/generic-doc-view-model-controller/actions";
import { ColumnViewModelStates } from "../../state-controllers/generic-doc-view-model-controller/states";
import { combineLatest } from 'rxjs';
import { PurchaseInvoiceActions } from '../../state-controllers/purchase-invoice-controller/store/actions';


@Component({
  template: containerTemplate,
  animations: containerAnim,
  providers: [PurchaseInvoicePagesService],
})
export class PurchaseInvoiceContainerComponent implements OnInit, OnDestroy {
  userPermissionTarget$ = this.permissionStore.select(
    UserPermInquirySelectors.selectUserPermInquiry
    );
  appletSettings$ = combineLatest([
    this.sessionStore.select(SessionSelectors.selectMasterSettings),
    this.sessionStore.select(SessionSelectors.selectPersonalSettings)
  ]).pipe(map(([a, b]) => ({ ...a, ...b })));  
  @ViewChild(FirstColumnDirective, { static: true }) firstColumnHost: FirstColumnDirective;
  @ViewChild(SecondColumnDirective, { static: true }) secondColumnHost: SecondColumnDirective;

  private subs = new SubSink();

  leftDrawer: ViewColumn[];
  rightDrawer: ViewColumn[];
  breadCrumbs: ViewColumn[];
  firstCol: ViewColumn;
  secondCol: ViewColumn;

  mobileView = window.matchMedia("(max-width: 768px)");
  appletGuid;
  tenantGuid;
  rank;
  appletSettings: AppletSettings;

  constructor(
    private viewColFacade: ViewColumnFacade,
    private componentFactoryResolver: ComponentFactoryResolver,
    private readonly sessionStore: Store<SessionStates>,
    private pagesService: PurchaseInvoicePagesService,
    private readonly permissionStore: Store<PermissionStates>,
    public readonly viewModelStore: Store<ColumnViewModelStates>
  ) {}

  ngOnInit() {
    this.appletGuid = sessionStorage.getItem("appletGuid");
    this.sessionStore.select(SessionSelectors.selectAppletLoginSubjectLink).subscribe(data => {
      if(data){
        this.tenantGuid = data.tenant_guid.toString();
      }
    })
    this.sessionStore.select(SessionSelectors.selectUserRank).subscribe(data => {
      this.rank = data;
    })
    this.subs.sink = this.viewColFacade.purchaseInvoiceCache$.subscribe(a =>
      a ? this.viewColFacade.setViewColState(a) : this.viewColFacade.setViewColState(this.pagesService.pages)
    );
    this.mobileView.matches ? this.viewColFacade.toggleColumn(true) : this.viewColFacade.toggleColumn(false);
    this.mobileView.addEventListener('change', (e) => {
      this.mobileView.matches ? this.viewColFacade.toggleColumn(true) : this.viewColFacade.toggleColumn(false);
    });

    this.subs.sink = this.viewColFacade.firstCol$.subscribe( resolve => {
      this.firstCol = resolve;
      this.loadComponent(resolve, this.firstColumnHost);
    });
    this.subs.sink = this.viewColFacade.secondCol$.subscribe( resolve => {
      this.secondCol = resolve;
      this.loadComponent(resolve, this.secondColumnHost);
    });
    this.subs.sink = this.viewColFacade.leftDrawer$.subscribe( resolve => this.leftDrawer = resolve );
    this.subs.sink = this.viewColFacade.rightDrawer$.subscribe( resolve => this.rightDrawer = resolve );

    this.subs.sink = this.viewColFacade.breadCrumbs$.subscribe( resolve => this.breadCrumbs = resolve );
    this.subs.sink = this.appletSettings$.subscribe({
      next: (resolve: AppletSettings) => {
        this.appletSettings = resolve;
        if(!this.mobileView.matches){
          if (resolve?.VERTICAL_ORIENTATION) {
            if (resolve.DEFAULT_TOGGLE_COLUMN === "DOUBLE" || resolve.DEFAULT_ORIENTATION === "HORIZONTAL") {
              this.viewColFacade.toggleColumn(false);
            } else {
              this.viewColFacade.toggleColumn(true);
            }
          } else {
            if (resolve.DEFAULT_TOGGLE_COLUMN === "SINGLE" || resolve.DEFAULT_ORIENTATION === "VERTICAL") {
              this.viewColFacade.toggleColumn(true);
            } else {
              this.viewColFacade.toggleColumn(false);
            }
          }
        }
      }
    });
    this.sessionStore.dispatch(PurchaseInvoiceActions.loadBranchCompany({ compGuid: null, branchGuid: null }));
    this.sessionStore.dispatch(SessionActions.getRolePricingSchemeLinkInit({ appletGuid: this.appletGuid, tenantGuid: this.tenantGuid?.toString(), rank: this.rank }))
  }

  loadComponent(
    comp: ViewColumn,
    host: FirstColumnDirective | SecondColumnDirective
    ) {
    const viewContainerRef = host.viewContainerRef;
    viewContainerRef.clear();
    if (!comp) {
      return;
    }
    const compFactory = this.componentFactoryResolver.resolveComponentFactory(
      comp.component
      );
    viewContainerRef.createComponent<ViewColumnComponent>(compFactory);
  }

  goBackIndex(i: number) {
    this.viewColFacade.goBackIndex(i);
  }

  goForwardIndex(i: number) {
    this.viewColFacade.goForwardIndex(i);
  }

  goToIndex(i: number) {
    this.viewColFacade.goToIndex(i);
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
    this.viewColFacade.savePurchaseInvoiceState();
  }

}
