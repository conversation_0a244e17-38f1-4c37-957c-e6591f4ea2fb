import { ChangeDetectionStrategy, Component, ViewChild } from '@angular/core';
import { MatTabGroup } from '@angular/material/tabs';
import { ComponentStore } from '@ngrx/component-store';
import { Store } from '@ngrx/store';
import { bl_fi_generic_doc_line_RowClass, GenericDocARAPContainerModel } from 'blg-akaun-ts-lib';
import { SessionSelectors } from 'projects/shared-utilities/modules/session/session-controller/selectors';
import { SessionStates } from 'projects/shared-utilities/modules/session/session-controller/states';
import { ViewColumnComponent } from 'projects/shared-utilities/view-column.component';
import { AppConfig } from 'projects/shared-utilities/visa';
import { combineLatest } from 'rxjs';
import { map, take } from 'rxjs/operators';
import { SubSink } from 'subsink2';
import { ViewColumnFacade } from '../../../facades/view-column.facade';
import { BillingAddress, BillingInfo, Department, MainDetails, ShippingAddress, ShippingInfo } from '../../../models/purchase-invoice.model';
import { HDRActions } from '../../../state-controllers/draft-controller/store/actions';
import { HDRSelectors, PNSSelectors } from '../../../state-controllers/draft-controller/store/selectors';
import { DraftStates } from '../../../state-controllers/draft-controller/store/states';
import { PurchaseInvoiceActions } from '../../../state-controllers/purchase-invoice-controller/store/actions';
import { PurchaseInvoiceStates } from '../../../state-controllers/purchase-invoice-controller/store/states';
import { PurchaseInvoiceSelectors } from '../../../state-controllers/purchase-invoice-controller/store/selectors';
import { PurchaseInvoiceAccountComponent } from './account/account.component';
import { PurchaseInvoiceLineItemsListingComponent } from './line-items/line-items-listing.component';
import { PurchaseInvoiceMainDetailsComponent } from './main-details/main-details.component';
import { ClientSidePermissionStates } from 'projects/shared-utilities/modules/permission/client-side-permissions-controller/states';
import { ClientSidePermissionsSelectors } from 'projects/shared-utilities/modules/permission/client-side-permissions-controller/selectors';
import { ColumnViewModelStates } from '../../../state-controllers/generic-doc-view-model-controller/states';
import { Column4ViewModelActions } from '../../../state-controllers/generic-doc-view-model-controller/actions';
import { UtilitiesModule } from 'projects/shared-utilities/utilities/utilities.module';
import { MatDialog } from "@angular/material/dialog";
import { PurchaseInvoiceReturnPopUpComponent } from '../purchase-invoice-create/return-popup/popup.component';

interface LocalState {
  deactivateReturn: boolean;
  deactivateAdd: boolean;
  deactivateList: boolean;
  selectedIndex: number;
  childSelectedIndex: number;
  selectedLine: any;
}

@Component({
  selector: 'app-purchase-invoice-create',
  templateUrl: './purchase-invoice-create.component.html',
  styleUrls: ['./purchase-invoice-create.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [ComponentStore]
})
export class PurchaseInvoiceCreateComponent extends ViewColumnComponent {

  protected subs = new SubSink();

  protected compName = 'Purchase Invoice Create';
  protected readonly index = 1;
  protected localState: LocalState;
  protected prevLocalState: any;
  appletSettinsgMain;

  readonly localState$ = this.viewColFacade.selectLocalState(this.index);
  readonly deactivateReturn$ = this.componentStore.select(state => state.deactivateReturn);
  readonly deactivateAdd$ = this.componentStore.select(state => state.deactivateAdd);
  readonly selectedIndex$ = this.componentStore.select(state => state.selectedIndex);
  readonly childSelectedIndex$ = this.componentStore.select(state => state.childSelectedIndex);
  clientSidePermissions$ = this.permissionStore.select(ClientSidePermissionsSelectors.selectAll)
  clientSidePermissionSettings: any;
  SHOW_INTERCOMPANY_TAB: boolean;

  draft$ = this.draftStore.select(HDRSelectors.selectHdr);
  pns$ = this.draftStore.select(PNSSelectors.selectAll);
  // payment$ = this.draftStore.select(PaymentSelectors.selectAll);

  prevIndex: number;
  apiVisa = AppConfig.apiVisa;
  editedInvoice: boolean;

  appletSettings$ = combineLatest([
    this.sessionStore.select(SessionSelectors.selectMasterSettings),
    this.sessionStore.select(SessionSelectors.selectPersonalSettings)
  ]).pipe(map(([a, b]) => ({ ...a, ...b, DEFAULT_BRANCH: b.DEFAULT_BRANCH ? b.DEFAULT_BRANCH: a.DEFAULT_BRANCH, DEFAULT_LOCATION: b.DEFAULT_LOCATION ? b.DEFAULT_LOCATION : a.DEFAULT_LOCATION})));

  @ViewChild(MatTabGroup) matTab: MatTabGroup;
  @ViewChild(PurchaseInvoiceMainDetailsComponent) main: PurchaseInvoiceMainDetailsComponent;
  @ViewChild(PurchaseInvoiceAccountComponent) account: PurchaseInvoiceAccountComponent;
  @ViewChild(PurchaseInvoiceLineItemsListingComponent) lines: PurchaseInvoiceLineItemsListingComponent;
  selectedIndex: number;
  
  constructor(
    private viewModelStore: Store<ColumnViewModelStates>,
    protected viewColFacade: ViewColumnFacade,
    protected readonly store: Store<PurchaseInvoiceStates>,
    protected readonly draftStore: Store<DraftStates>,
    protected readonly sessionStore: Store<SessionStates>,
    protected readonly componentStore: ComponentStore<LocalState>,
    protected readonly permissionStore: Store<ClientSidePermissionStates>,
    private dialogRef: MatDialog,) {

    super();
  }

  ngOnInit() {
    this.subs.sink = this.localState$.subscribe(a => {
      this.localState = a;
      this.componentStore.setState(a);
    });
    this.subs.sink = this.viewColFacade.prevIndex$.subscribe(resolve => this.prevIndex = resolve);
    this.subs.sink = this.viewColFacade.prevLocalState$().subscribe(resolve => this.prevLocalState = resolve);

    this.subs.sink = this.selectedIndex$.pipe(
      take(1)
    ).subscribe({next: resolve => this.selectedIndex = resolve});
  
    this.subs.sink = this.clientSidePermissions$.subscribe({
      next: (resolve) => {
        console.log("clientside perms -->", resolve)
        resolve.forEach(permission => {
          if(permission.perm_code === "SHOW_INTERCOMPANY_PI_SCREEN"){
            this.SHOW_INTERCOMPANY_TAB = true
          }
        })
      }
    });

    this.store.select(PurchaseInvoiceSelectors.selectEditedInvoice).subscribe(resp => {
      this.editedInvoice = resp;
    });

    this.sessionStore.select(SessionSelectors.selectMasterSettings).subscribe(data => {
      // console.log("session data loaded: ",data )
      this.appletSettinsgMain = data;
    })

  }

  onCreate() {
    this.store.dispatch(PurchaseInvoiceActions.editedInvoice({edited: false}));
    this.store.dispatch(PurchaseInvoiceActions.createPurchaseInvoiceInit());
  }

  disableCreate() {
    return this.main?.form.invalid || !this.lines?.rowData.length || this.account?.entity?.form.invalid;
  }

  onReset() {
    this.lines?.form.reset();
    this.viewColFacade.resetDraft();
  }

  showGroupDiscount(){
    this.lines?.checkGroupDiscount();
  }

  onUpdateMainDetails(form: MainDetails) {
    this.draftStore.dispatch(HDRActions.updateMainDetails({ form }));
  }

  onUpdateBillingInfo(form: BillingInfo) {
    this.draftStore.dispatch(HDRActions.updateBillingInfo({ form }));
  }

  onUpdateBillingAddress(form: BillingAddress) {
    this.draftStore.dispatch(HDRActions.updateBillingAddress({ form }));
  }

  onUpdateShippingInfo(form: ShippingInfo) {
    this.draftStore.dispatch(HDRActions.updateShippingInfo({ form }));
  }

  onUpdateShippingAddress(form: ShippingAddress) {
    this.draftStore.dispatch(HDRActions.updateShippingAddress({ form }));
  }

  onUpdateDepartmentInfo(form: Department) {
    this.draftStore.dispatch(HDRActions.updateDepartment({ form }));
  }

  onSelectPurchaser() {
    this.viewColFacade.updateInstance(this.index, {
      ...this.localState,
      deactivateReturn: true,
      deactivateAdd: true,
      deactivateList: true
    });
    this.viewColFacade.onNextAndReset(this.index, 17);
  }

  onSelectEntity() {
    this.viewColFacade.updateInstance(this.index, {
      ...this.localState,
      deactivateReturn: true,
      deactivateAdd: true,
      deactivateList: true
    });
    this.viewColFacade.onNextAndReset(this.index, 3);
  }

  onSelectBilling() {
    this.viewColFacade.updateInstance(this.index, {
      ...this.localState,
      deactivateReturn: true,
      deactivateAdd: true,
      deactivateList: true
    });
    this.viewColFacade.onNextAndReset(this.index, 4);
  }

  onSelectShipping() {
    this.viewColFacade.updateInstance(this.index, {
      ...this.localState,
      deactivateReturn: true,
      deactivateAdd: true,
      deactivateList: true
    });
    this.viewColFacade.onNextAndReset(this.index, 5);
  }

  onLineItemCreate() {
    this.store.dispatch(PurchaseInvoiceActions.selectMode({ mode: 'create' }))
    this.viewColFacade.updateInstance(this.index, {
      ...this.localState,
      deactivateReturn: true,
      deactivateAdd: true,
      deactivateList: true
    });
    this.viewColFacade.onNextAndReset(this.index, 6);
  }

  onLineItemEdit(item: bl_fi_generic_doc_line_RowClass) {
    if (item && !this.localState.deactivateList) {
      const lineItem = { ...item };
      this.store.dispatch(PurchaseInvoiceActions.selectPricingSchemeLink({ item }))
      this.store.dispatch(PurchaseInvoiceActions.selectLineItem({ lineItem }));
      if(item.item_sub_type==='SERIAL_NUMBER'){
        this.viewModelStore.dispatch(Column4ViewModelActions.processSerialNumberListing_Reset());
        const hasInvalidSerial = UtilitiesModule.checkSerialValid(<any> item.serial_no);
        console.log('onLineItemEdit',hasInvalidSerial)
        if(hasInvalidSerial){
          this.viewModelStore.dispatch(Column4ViewModelActions.setSerialNumberTabFieldColor({color: "warn"}));
        }
        this.viewModelStore.dispatch(Column4ViewModelActions.setSerialNumberTab_ScanTab_SerialNumbersListing({serialNumberListing: <any> item.serial_no}));
      }
      this.viewColFacade.updateInstance(this.index, {
        ...this.localState,
        deactivateReturn: true,
        deactivateAdd: true,
        deactivateList: false,
        selectedLine: item.guid
      });
      this.viewColFacade.onNextAndReset(this.index, 8);
    }
  }

  onPaymentCreate() {
    this.viewColFacade.updateInstance(this.index, {
      ...this.localState,
      deactivateReturn: true,
      deactivateAdd: true,
      deactivateList: true
    });
    this.viewColFacade.onNextAndReset(this.index, 11);
  }

  onPaymentEdit(payment: bl_fi_generic_doc_line_RowClass) {
    if (payment && !this.localState.deactivateList) {
      this.store.dispatch(PurchaseInvoiceActions.selectPayment({ payment }))
      this.viewColFacade.updateInstance(this.index, {
        ...this.localState,
        deactivateReturn: true,
        deactivateAdd: true,
        deactivateList: false,
        selectedLine: payment.guid
      });
      this.viewColFacade.onNextAndReset(this.index, 12);
    }
  }

  onSelectDocument() {
    this.viewColFacade.updateInstance(this.index, {
      ...this.localState,
      deactivateReturn: true,
      deactivateAdd: true
    });
    this.viewColFacade.onNextAndReset(this.index, 13);
  }

  onContraEdit(contra: GenericDocARAPContainerModel) {
    if (contra && !this.localState.deactivateList) {
      this.store.dispatch(PurchaseInvoiceActions.selectContraLink({ link: contra }));
      this.viewColFacade.updateInstance<LocalState>(this.index, {
        ...this.localState,
        deactivateReturn: true,
        deactivateAdd: false,
        deactivateList: false,
      });
      this.viewColFacade.onNextAndReset(this.index, 15);
    }
  }

  onReturn() {
    if(!this.disableCreate()){
      this.store.dispatch(PurchaseInvoiceActions.disableCreate({ disable: false }));
    }else{
      this.store.dispatch(PurchaseInvoiceActions.disableCreate({ disable: true }));
    }

    if(this.editedInvoice){
      this.dialogRef.open(PurchaseInvoiceReturnPopUpComponent);
    }else{
      this.viewColFacade.updateInstance<LocalState>(this.prevIndex, {
        ...this.prevLocalState,
        deactivateAdd: false,
        deactivateList: false
      });
      this.viewColFacade.onPrev(this.prevIndex);
    }
    // this.viewColFacade.updateInstance<LocalState>(this.prevIndex, {
    //   ...this.prevLocalState,
    //   deactivateAdd: false,
    //   deactivateList: false
    // });
    // this.viewColFacade.onPrev(this.prevIndex);
  }

  ngOnDestroy() {
    if (this.matTab) {
      this.viewColFacade.updateInstance<LocalState>(this.index, {
        ...this.localState,
        selectedIndex: this.matTab?.selectedIndex,
        childSelectedIndex: this.account.matTab?.selectedIndex,
      });
    }
    this.subs.unsubscribe();
  }

}
