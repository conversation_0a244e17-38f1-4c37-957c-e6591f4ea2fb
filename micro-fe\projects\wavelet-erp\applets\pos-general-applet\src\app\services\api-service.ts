import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import {
  ApiResponseModel,
  ApiVisa, Core2Config, CouponContainerModel, GenericDocContainerModel, Pagination, PosTerminalDeviceContainerModel, PosTerminalService, GenericDocARAPContainerModel } from 'blg-akaun-ts-lib';
import { Observable, of, combineLatest } from 'rxjs';
import { UtilitiesModulePos } from '../components/utilities/utilities.module';
import { ViewColumnFacade } from '../facades/view-column.facade';
import { MainSelectors } from '../state-controllers/draft-controller/store/selectors';
import { DraftStates } from '../state-controllers/draft-controller/store/states';
import { PNSSelectors } from '../state-controllers/draft-controller/store/selectors';
import { SettlementSelectors } from '../state-controllers/draft-controller/store/selectors';
import { Store } from '@ngrx/store';
import { ZReportInputModel } from '../models/z-report-model';
import { PosStates } from '../state-controllers/pos-controller/store/states';
import { MainActions } from '../state-controllers/draft-controller/store/actions';
import { EmailNotificationPdfModel } from '../models/pos.model';
import { POSDexieService } from './pos-dexie.service';
import { PosPagesService } from './pos-pages.service';
import { WebSocketService } from './websocket-service';
import { Subscription } from 'rxjs';
import { PosActions } from '../state-controllers/pos-controller/store/actions'
import { CashierCollectionReportInputDto } from '../models/pos.model';
import { UtilitiesModule } from 'projects/shared-utilities/utilities/utilities.module';

@Injectable({providedIn: 'root'})
export class ApiService {
  readonly url = Core2Config.DOMAIN_URL + Core2Config.TENANT_DOMAIN_URL_PREFIX + Core2Config.ERP_PREFIX + Core2Config.REPORTS_PREFIX + 'sales';
  readonly url2 = Core2Config.DOMAIN_URL + Core2Config.TENANT_DOMAIN_URL_PREFIX + Core2Config.ERP_PREFIX +  'pos/mto-items';
  readonly url3 = Core2Config.DOMAIN_URL + Core2Config.TENANT_URL_PREFIX + Core2Config.DOMAIN_PREFIX + Core2Config.ERP_PREFIX + Core2Config.GENERIC_PREFIX + 'internal-sales-cashbills';

  protected apiUrl: string;
  protected apiUrl2: string;
  protected apiUrl3: string;
  protected api_domain_url: string;
  protected endpoint_path: string;
  protected endpoint_path2: string;
  protected endpoint_path3: string;
  protected paying;
  protected total;
  protected httpClient: HttpClient;
  private socketSubscription: Subscription;
  constructor(http: HttpClient,
      protected readonly store: Store<PosStates>,
      private terminalDeviceService: PosTerminalService,
      protected viewColFacade: ViewColumnFacade,
      protected readonly draftStore: Store<DraftStates>,
      private readonly posDexieService: POSDexieService,
      private webSocketService: WebSocketService
  ) {
    this.apiUrl = this.url;
    this.apiUrl2 = this.url2;
    this.apiUrl3 = this.url3;
    this.endpoint_path = Core2Config.TENANT_DOMAIN_URL_PREFIX + Core2Config.ERP_PREFIX + Core2Config.REPORTS_PREFIX + 'sales';
    this.endpoint_path2 = Core2Config.TENANT_DOMAIN_URL_PREFIX + 'pos/mto-items';
    this.endpoint_path3 = Core2Config.TENANT_DOMAIN_URL_PREFIX +  Core2Config.ERP_PREFIX + Core2Config.GENERIC_PREFIX + 'internal-sales-cashbills';
    this.httpClient = http;
    combineLatest([
      this.draftStore.select(PNSSelectors.selectTotalAmount),
      this.draftStore.select(SettlementSelectors.selectTotalPaying)
    ]).pipe(
    ).subscribe({next:  (
      [
        total, paying
      ]:any) => {
        this.total = total;
        this.paying = paying;
    }});

  }

  public getApiUrl(apiVisa: ApiVisa) {
    //console.log('getApiUrl apiVisa',apiVisa)
    let url = this.apiUrl;
    //console.log('getApiUrl',url)
    if (this.endpoint_path && apiVisa.api_domain_url) {
      //console.log('getApiUrl',this.endpoint_path)
      url = apiVisa.api_domain_url + this.endpoint_path;
    }
    //console.log('getApiUrl end',url)
    return url;
  }

  public getApiUrlPosMTO(apiVisa: ApiVisa) {
    //console.log('getApiUrlPosMTO apiVisa',apiVisa)
    let url = this.apiUrl2;
    //console.log('getApiUrlPosMTO',url)
    if (this.endpoint_path2 && apiVisa.api_domain_url) {
     // console.log('getApiUrlPosMTO',this.endpoint_path2)
      url = apiVisa.api_domain_url + this.endpoint_path2;
    }
    //console.log('getApiUrlPosMTO end',url)
    return url;
  }

  public getApiUrlVoid(apiVisa: ApiVisa) {
    let url = this.apiUrl3;
    if (this.endpoint_path3 && apiVisa.api_domain_url) {
      url = apiVisa.api_domain_url + this.endpoint_path3;
    }
    return url;
  }

  public getHttpHeader(apiVisa: ApiVisa) {
    apiVisa.applet_code = apiVisa.applet_code ? apiVisa.applet_code : 'none';
    apiVisa.tenantCode = apiVisa.tenantCode ? apiVisa.tenantCode : '';

    const httpOptions = {
      headers: new HttpHeaders({
        authorization: apiVisa.jwt_secret,
        tenantCode: apiVisa.tenantCode, /// this will be removed in the future
        appId: apiVisa.applet_code, /// this will be removed in the future
      })
    };

    return httpOptions;
  }

  public showDisplayPoleTotal(): Observable<any> {
    let url = 'http://localhost:8085/getDisplayPole';


    let message1 = 'Total: MYR' + (this.total?UtilitiesModulePos.decimalFormatter(this.total, 2): '0.00');
    let changeAmt = 0.00;
    changeAmt = +(Number(this.paying) - Number(this.total)).toFixed(2);
    //console.log("changeAmt:",changeAmt)
    const message2 = 'Change: MYR' + changeAmt.toFixed(2);
    while (message1.length < 20) {
      message1 += " ";
    }

    // Truncate message1 if it's longer than 20 characters
    if (message1.length > 20) {
        message1 = message1.substring(0, 20);
    }
    url += '/' + message1 + '/' + message2;
    //console.log("display pole:",url)
    const headers = new HttpHeaders();

    return this.httpClient.get(url, { headers, responseType: 'text' });
}

  public getDisplayPole(itemCode: any, price: any): Observable<any> {
    let url = 'http://localhost:8085/getDisplayPole';

    const strItemCode = itemCode.substring(0, 9);
    const finalItemCode = this.pad2(strItemCode, " ", 13);

    const strPrice = UtilitiesModulePos.decimalFormatter(price, 2);
    const finalPrice = this.pad2(strPrice, " ", 8);

    const message1 = finalItemCode + " " + finalPrice;
    const message2 = 'Total: MYR' + (this.total?UtilitiesModulePos.decimalFormatter(this.total, 2): '0.00');
    url += '/' + message1 + '/' + message2;
    //console.log("display pole:",url)
    const headers = new HttpHeaders();

    return this.httpClient.get(url, { headers, responseType: 'text' });
  }

  public clearDisplayPole(): Observable<any>  {
    let url = 'http://localhost:8085/getDisplayPole';
    let message1 = "THANK YOU";
    while (message1.length < 20) {
      message1 += " ";
    }

    // Truncate message1 if it's longer than 20 characters
    if (message1.length > 20) {
        message1 = message1.substring(0, 20);
    }
    const message2 = "HAVE A NICE DAY";
    url += '/' + message1 + '/' + message2;
    const headers = new HttpHeaders();

    return this.httpClient.get(url, { headers, responseType: 'text' });
  }

  public pad2(input, pad, length)
	{
		while (input.length < length)
		{
			input = input + pad;
		}
		return input.substring(0, length - 1);
	}

  public getZReport(inputModel: ZReportInputModel, apiVisa: ApiVisa): Observable<ApiResponseModel<any>> {
    let url = this.getApiUrl(apiVisa);
    let formData = {
    };

    if (inputModel) formData = inputModel;
    url += "/z-report";
    //console.log('getZReport',url)
    return this.httpClient.post<ApiResponseModel<any>>(
      url,
      formData,
      this.getHttpHeader(apiVisa)
    );
  }

  public getMTOByCat(code: string, apiVisa: ApiVisa): Observable<ApiResponseModel<any>> {
    let url = this.getApiUrlPosMTO(apiVisa);


    url += "/category/backoffice-ep/"+code;
    //console.log('getMTOByCat',url)
    return this.httpClient.get<ApiResponseModel<any>>(
      url,
      this.getHttpHeader(apiVisa)
    );
  }

  public getMTOByOptionLine(code: string, apiVisa: ApiVisa): Observable<ApiResponseModel<any>> {
    let url = this.getApiUrlPosMTO(apiVisa);


    url += "/option-line/backoffice-ep/"+code;
    //console.log('getMTOByOptionLine',url)
    return this.httpClient.get<ApiResponseModel<any>>(
      url,
      this.getHttpHeader(apiVisa)
    );
  }

  void(body: any, apiVisa: ApiVisa, guid: string) {
    let url = this.getApiUrlVoid(apiVisa);

    return this.httpClient.put<ApiResponseModel<any>>(
      url + '/backoffice-ep/void/' + guid,
      body,
      this.getHttpHeader(apiVisa)
    );
  }



  getDevice(apiVisa: ApiVisa) {
    let deviceGuid = localStorage.getItem('device_guid');
    if(!deviceGuid){
      let urlMac = "http://localhost:8085//getMacAddress";
      this.httpClient.get(urlMac,{ responseType: 'text' }).subscribe((res:any)  => {
        //console.log("getMacAddress success",res)
        let obj = JSON.parse(res);
        let mac_add = obj.mac_address;
        //console.log("mac address:",mac_add);
        if(mac_add){
          const paging = new Pagination();
          paging.conditionalCriteria.push({ columnName: 'mac_address', operator: '=', value: mac_add });
          this.terminalDeviceService.getByCriteria(paging, apiVisa).subscribe((resp) => {
            let deviceContainer = new PosTerminalDeviceContainerModel();
            if(resp.data.length>0){
              deviceContainer = resp.data[0];
              deviceGuid = deviceContainer.bl_pos_terminal_device.guid.toString();
              localStorage.setItem('device_guid',deviceContainer.bl_pos_terminal_device.guid.toString());
              localStorage.setItem('device_code',deviceContainer.bl_pos_terminal_device.code.toString());
              this.store.dispatch(MainActions.getDeviceInit({guid: deviceGuid}));
            }
          })

        }

      },error => {
        console.log('getMacAddress failed', error);
      })

    }
    return deviceGuid;

  }

  public emailPDF(inputModel: EmailNotificationPdfModel, apiVisa: ApiVisa, guid: any): Observable<ApiResponseModel<any>> {

    let url = this.getApiUrlVoid(apiVisa);
    let formData = {
    };

    if (inputModel) formData = inputModel;
    url += "/send-email-pdf/backoffice-ep/"+guid;
    console.log('emailPDF',url)
    return this.httpClient.post<ApiResponseModel<any>>(
      url,
      formData,
      this.getHttpHeader(apiVisa)
    );
  }

  public getCouponBySerial(sn: string, apiVisa: ApiVisa): Observable<ApiResponseModel<CouponContainerModel>> {

    let url = Core2Config.DOMAIN_URL + Core2Config.TENANT_DOMAIN_URL_PREFIX + Core2Config.ERP_PREFIX +  'coupon/lines';
    let eppath = Core2Config.TENANT_DOMAIN_URL_PREFIX + Core2Config.ERP_PREFIX + 'coupon/lines';
    if (eppath&& apiVisa.api_domain_url) {
      url = apiVisa.api_domain_url + eppath;
    }

    url += "/serial-numbers/inquiry/"+sn+"/backoffice-ep";

    return this.httpClient.get<ApiResponseModel<any>>(
      url,
      this.getHttpHeader(apiVisa)
    );
  }

  public getItemPricingListingByCriteriaSnapshot(paging:Pagination,apiVisa: ApiVisa): Observable<ApiResponseModel<any>> {

    let url = Core2Config.DOMAIN_URL + Core2Config.TENANT_DOMAIN_URL_PREFIX + Core2Config.ERP_PREFIX +  'fi/fi-items-pricing-stock-balance/backoffice-ep';
    let eppath = Core2Config.TENANT_DOMAIN_URL_PREFIX + Core2Config.ERP_PREFIX + 'fi/fi-items-pricing-stock-balance/backoffice-ep';
    if (eppath&& apiVisa.api_domain_url) {
      url = apiVisa.api_domain_url + eppath;
    }

    const UrlByCriteria =
    url + '/query/snapshot?' + paging.getQueryKeyValues();

    return this.httpClient.get<ApiResponseModel<any>>(
      UrlByCriteria,
      this.getHttpHeader(apiVisa)
    );
  }

  public getItemPricingListingByCriteria(paging:Pagination,apiVisa: ApiVisa): Observable<ApiResponseModel<any>> {

    let url = Core2Config.DOMAIN_URL + Core2Config.TENANT_DOMAIN_URL_PREFIX + Core2Config.ERP_PREFIX +  'fi/fi-items-pricing-stock-balance/backoffice-ep';
    let eppath = Core2Config.TENANT_DOMAIN_URL_PREFIX + Core2Config.ERP_PREFIX + 'fi/fi-items-pricing-stock-balance/backoffice-ep';
    if (eppath&& apiVisa.api_domain_url) {
      url = apiVisa.api_domain_url + eppath;
    }

    const UrlByCriteria =
    url + '/query?' + paging.getQueryKeyValues();
    return this.httpClient.get<ApiResponseModel<any>>(
      UrlByCriteria,
      this.getHttpHeader(apiVisa)
    );
  }

  public scanItems(item_code: string, location_guid:any,pricing_hdr_guid:any, itemCategoryFilterList: any, branchGuid: any, apiVisa: ApiVisa): Observable<ApiResponseModel<any>> {
    const levelsToProcess = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
    //console.log('itemCategoryFilterList',itemCategoryFilterList)
    for (const level of levelsToProcess) {
      const matchingSetting = itemCategoryFilterList.find(s => {
        const settingLevelValue = (s.bl_applet_setting_filter_fi_item_category_link as any)?.level_value;
        const settingGuidBranch = (s.bl_applet_setting_filter_fi_item_category_link as any)?.guid_branch;
        return settingLevelValue == level && settingGuidBranch == branchGuid;
      });
        //console.log('matchingSetting',matchingSetting)
        if (matchingSetting) {
            const categoryGuids = (matchingSetting as any).bl_applet_setting_filter_fi_item_category_link.category_json?.guids;

            if(categoryGuids){
              this[`filterCategory${level}`] = categoryGuids.join(",");
            }

            const excludeCategoryGuids = (matchingSetting as any).bl_applet_setting_filter_fi_item_category_link.exclude_category_json?.guids;
            //console.log('excludeCategoryGuids',excludeCategoryGuids);
            if(excludeCategoryGuids){
              this[`excludeFilterCategory${level}`] = excludeCategoryGuids.join(",");
            }
        } else {
            this[`filterCategory${level}`] = "";
        }
    }
    let url = Core2Config.DOMAIN_URL + Core2Config.TENANT_DOMAIN_URL_PREFIX + Core2Config.ERP_PREFIX +  'fi/fi-items-pricing-stock-balance/backoffice-ep/scan-items';
    let eppath = Core2Config.TENANT_DOMAIN_URL_PREFIX + Core2Config.ERP_PREFIX + 'fi/fi-items-pricing-stock-balance/backoffice-ep/scan-items';
    if (eppath&& apiVisa.api_domain_url) {
      url = apiVisa.api_domain_url + eppath;
    }
    let urlWithFilters = url + `?code=${item_code}&stock_location_guid=${location_guid}&guid_pricing_scheme_hdr=${pricing_hdr_guid}`;

    for (let level = 0; level <= 10; level++) {
        const filterCategory = this[`filterCategory${level}`];

        if (filterCategory) {
            //urlWithFilters += `&category_guids_${level}=${encodeURIComponent(filterCategory)}`;
            const encodedFilterCategory = filterCategory
            .split(',')
            .map(encodeURIComponent)
            .join(',');
            urlWithFilters += `&category_guids_${level}=${encodedFilterCategory}`;
        }
    }
    for (let level = 0; level <= 10; level++) {
        const excludeFilterCategory = this[`excludeFilterCategory${level}`];

        if (excludeFilterCategory) {
            const encodedFilterCategory = excludeFilterCategory
            .split(',')
            .map(encodeURIComponent)
            .join(',');
            urlWithFilters += `&exclude_category_guids_${level}=${encodedFilterCategory}`;
        }
    }

    const finalUrl = urlWithFilters;

    return this.httpClient.get<ApiResponseModel<any>>(
      finalUrl,
      this.getHttpHeader(apiVisa)
    );
  }

  public getItemListingByLabel(paging:Pagination,apiVisa: ApiVisa): Observable<ApiResponseModel<any>> {

    let url = Core2Config.DOMAIN_URL + Core2Config.TENANT_DOMAIN_URL_PREFIX + Core2Config.ERP_PREFIX +  'fi/fi-items/backoffice-ep/ecom/product-list';
    let eppath = Core2Config.TENANT_DOMAIN_URL_PREFIX + Core2Config.ERP_PREFIX + 'fi/fi-items/backoffice-ep/ecom/product-list';
    if (eppath&& apiVisa.api_domain_url) {
      url = apiVisa.api_domain_url + eppath;
    }

    const UrlByCriteria =
    url + '?' + paging.getQueryKeyValues();
    return this.httpClient.get<ApiResponseModel<any>>(
      UrlByCriteria,
      this.getHttpHeader(apiVisa)
    );
  }

  public getItemListingStockByLabel(paging:Pagination,apiVisa: ApiVisa): Observable<ApiResponseModel<any>> {

    let url = Core2Config.DOMAIN_URL + Core2Config.TENANT_DOMAIN_URL_PREFIX + Core2Config.ERP_PREFIX +  'fi/fi-items-pricing-stock-balance/backoffice-ep/label';
    let eppath = Core2Config.TENANT_DOMAIN_URL_PREFIX + Core2Config.ERP_PREFIX + 'fi/fi-items-pricing-stock-balance/backoffice-ep/label';
    if (eppath&& apiVisa.api_domain_url) {
      url = apiVisa.api_domain_url + eppath;
    }

    const UrlByCriteria =
    url + '/query?' + paging.getQueryKeyValues();
    return this.httpClient.get<ApiResponseModel<any>>(
      UrlByCriteria,
      this.getHttpHeader(apiVisa)
    );
  }

  async fetchJrxmlFromDexieAndPrint(isPopup:boolean,genericDocumentContainer : GenericDocContainerModel,branch: any,company:any,
    profileName:string,salesAgentName: string,customerName: string,customerPhone:string,deviceCode:string) {
    try {
      console.log('branch.bl_fi_mst_branch?.code',branch.bl_fi_mst_branch);

      const jrxmlRecordDefault = await this.posDexieService.db.printable.get(1);
      const jrxmlRecord = await this.posDexieService.db.printable
      .where('branch_guid')
      .equals(branch?.bl_fi_mst_branch?.guid)
      .first();
      //console.log('jrxmlRecordDefault',jrxmlRecordDefault);
      //console.log('jrxmlRecord',jrxmlRecord)
      let finalJrxmlRecord;
      if(jrxmlRecord){
        finalJrxmlRecord = jrxmlRecord;
      }
      else if(!jrxmlRecord && jrxmlRecordDefault){
        finalJrxmlRecord = jrxmlRecordDefault;
      }
      //console.log('finalJrxmlRecord',finalJrxmlRecord);
      const images = await this.posDexieService.db.images.toArray();

      //console.log("images",images)
      if (finalJrxmlRecord) {
        let genDoc = this.constructPrintDto(
          genericDocumentContainer,
          branch,
          company,
          profileName,
          salesAgentName,
          customerName,
          customerPhone,
          deviceCode,
          finalJrxmlRecord.main_report,
          finalJrxmlRecord.sub_report,
          finalJrxmlRecord.company_logo,
          images.length > 0 ? images : []
        );

        let url = "http://localhost:8085/printing/pdf/dexie";
        this.httpClient.post(url,genDoc,{ responseType:'blob' as 'json'}).subscribe((b:any)  => {
          if(isPopup){
            const fileURL = window.URL.createObjectURL(b);
            window.open(fileURL, '_blank');
          }
        })
      } else {
        console.error('JRXML data not found in Dexie.js');
      }
    } catch (error) {
      console.error('Error:', error);
      return of(null);
    }
  }

  constructPrintDto(genericDocumentContainer : GenericDocContainerModel,branch: any,company:any,
    profileName:string,salesAgentName: string,customerName: string,customerPhone:string,deviceCode:string,jrxml_main:string,jrxml_sub:string, companyLogo: string,
    images?: any[]){
    genericDocumentContainer = Object.assign({
      images: images,
      companyLogo: companyLogo,
      hideRounding:branch.bl_fi_mst_branch?.code?.includes("LUNA")?true:false,
      jrxml_main:jrxml_main,
      jrxml_sub:jrxml_sub,
      branchName: branch?.bl_fi_mst_branch?.name ?? "",
      branchCode : branch.bl_fi_mst_branch.code?? "",
      branchRegNo: branch.bl_fi_mst_branch_ext.find(x=>x.param_code==='BUSINESS_REGISTRATION_NUMBER')?.value_string?? "",
      compName: company.bl_fi_mst_comp.name?? "",
      compRegNo: company.bl_fi_mst_comp.comp_registration_num?? "",
      branchAdd1:branch.bl_fi_mst_branch_ext.find(x=>x.param_code==='ADDRESS')?.value_json?.address1?? "",
      branchAdd2:branch.bl_fi_mst_branch_ext.find(x=>x.param_code==='ADDRESS')?.value_json?.address2?? "",
      branchAdd3:branch.bl_fi_mst_branch_ext.find(x=>x.param_code==='ADDRESS')?.value_json?.address3?? "",
      branchAdd4:branch.bl_fi_mst_branch_ext.find(x=>x.param_code==='ADDRESS')?.value_json?.address4?? "",
      branchAdd5:branch.bl_fi_mst_branch_ext.find(x=>x.param_code==='ADDRESS')?.value_json?.address5?? "",
      branchPhone:branch.bl_fi_mst_branch_ext.find(x=>x.param_code==='CONTACT_INFO')?.value_json?.branchPhoneNumber?? "",
      branchMobile:branch.bl_fi_mst_branch_ext.find(x=>x.param_code==='CONTACT_INFO')?.value_json?.branchMobileNumber?? "",
      companyWebsite:company.bl_fi_mst_comp_ext.find(x=>x.param_code==='CONTACT_INFO')?.value_json?.website?? "",
      companySSTId:company.bl_fi_mst_comp.sst_registration_id?? "",
      userCreate: profileName,
      salesAgent: salesAgentName,
      customerName : customerName,
      customerPhone : customerPhone,
      deviceCode : deviceCode
    },genericDocumentContainer)
    return genericDocumentContainer;
  }


  print(dtoObject: any, isPopup: boolean){
    let url = "http://localhost:8085/printing/pdf/blg";

    this.httpClient.post(url,dtoObject,{ responseType:'blob' as 'json'}).subscribe((b:any)  => {
      if(isPopup){
        const fileURL = window.URL.createObjectURL(b);
        window.open(fileURL, '_blank');
      }
    })
  }

  public updateAutoFinal(genericDocumentContainer: any,validateStockBalance: boolean, apiVisa: ApiVisa,genericDocumentArapContraContainerList?:any[]): Observable<ApiResponseModel<any>> {
    let url = this.apiUrl3;
    if (this.endpoint_path3 && apiVisa.api_domain_url) {
      url = apiVisa.api_domain_url + this.endpoint_path3;
    }
    const formData: { [key: string]: any } = {
      validateStockBalance: validateStockBalance,
      genericDocumentContainer: genericDocumentContainer
    };
    let newContra : any []= genericDocumentArapContraContainerList.filter(c=>!c.status);
    newContra = newContra.map(c => {
      const { bl_fi_generic_doc_hdr_server_doc_1, ...rest } = c;
      return { ...rest, status: 'ACTIVE' };
    });
    let arapContainerList = newContra.map(c => {
        let arapContainer = new GenericDocARAPContainerModel();
        arapContainer.bl_fi_generic_doc_arap_contra = c;
        return arapContainer;
      });

    if (genericDocumentArapContraContainerList) {
      formData.genericDocumentArapContraContainerList = arapContainerList;
    }

    url += "/update-auto-final/backoffice-ep";
    return this.httpClient.post<ApiResponseModel<any>>(
      url,
      formData,
      this.getHttpHeader(apiVisa)
    );
  }

  public createNewPosSession(container: any, apiVisa: ApiVisa): Observable<ApiResponseModel<any>> {
    let url = Core2Config.DOMAIN_URL + Core2Config.TENANT_URL_PREFIX + Core2Config.DOMAIN_PREFIX + Core2Config.ERP_PREFIX + 'pos-terminal-session-hdrs/drawer-link/backoffice-ep';
    let ep = Core2Config.TENANT_DOMAIN_URL_PREFIX +  Core2Config.ERP_PREFIX + 'pos-terminal-session-hdrs/drawer-link/backoffice-ep';
    if (ep && apiVisa.api_domain_url) {
      url = apiVisa.api_domain_url + ep;
    }

    return this.httpClient.post<ApiResponseModel<any>>(
      url,
      container,
      this.getHttpHeader(apiVisa)
    );
  }

  public grossProfit(container: any, apiVisa: ApiVisa): Observable<ApiResponseModel<any>> {
    let url = Core2Config.DOMAIN_URL + Core2Config.TENANT_URL_PREFIX + Core2Config.DOMAIN_PREFIX + Core2Config.ERP_PREFIX + 'gen-doc/reports/gross-profits/backoffice-ep';
    let ep = Core2Config.TENANT_DOMAIN_URL_PREFIX +  Core2Config.ERP_PREFIX + 'gen-doc/reports/gross-profits/backoffice-ep';
    if (ep && apiVisa.api_domain_url) {
      url = apiVisa.api_domain_url + ep;
    }

    return this.httpClient.post<ApiResponseModel<any>>(
      url,
      container,
      this.getHttpHeader(apiVisa)
    );
  }

  updateSettlementMethod(body: any, apiVisa: ApiVisa) {
    let url = Core2Config.DOMAIN_URL + Core2Config.TENANT_DOMAIN_URL_PREFIX + Core2Config.ERP_PREFIX + Core2Config.GENERIC_PREFIX + 'internal-sales-cashbills/update-settlement-methods/backoffice-ep';
    let eppath = Core2Config.TENANT_DOMAIN_URL_PREFIX + Core2Config.ERP_PREFIX + Core2Config.GENERIC_PREFIX + 'internal-sales-cashbills/update-settlement-methods/backoffice-ep';
    if (eppath&& apiVisa.api_domain_url) {
      url = apiVisa.api_domain_url + eppath;
    }

    return this.httpClient.put<ApiResponseModel<any>>(
      url,
      body,
      this.getHttpHeader(apiVisa)
    );
  }

  public getPurchaseHistory(guid:any,apiVisa: ApiVisa): Observable<ApiResponseModel<any>> {

    let url = Core2Config.DOMAIN_URL + Core2Config.TENANT_DOMAIN_URL_PREFIX +  'pos/validate-staff-purchase-history';
    let eppath = Core2Config.TENANT_DOMAIN_URL_PREFIX + 'pos/validate-staff-purchase-history';
    if (eppath&& apiVisa.api_domain_url) {
      url = apiVisa.api_domain_url + eppath;
    }

    const UrlByCriteria =
    url + '/backoffice-ep/' + guid;
    return this.httpClient.get<ApiResponseModel<any>>(
      UrlByCriteria,
      this.getHttpHeader(apiVisa)
    );
  }

  public contraMulti(container: any, apiVisa: ApiVisa): Observable<ApiResponseModel<any>> {
    let url = Core2Config.DOMAIN_URL + Core2Config.TENANT_URL_PREFIX + Core2Config.DOMAIN_PREFIX + Core2Config.ERP_PREFIX + 'gen-doc/arap-contras/multi/backoffice-ep';
    let ep = Core2Config.TENANT_DOMAIN_URL_PREFIX +  Core2Config.ERP_PREFIX + 'gen-doc/arap-contras/multi/backoffice-ep';
    if (ep && apiVisa.api_domain_url) {
      url = apiVisa.api_domain_url + ep;
    }

    return this.httpClient.post<ApiResponseModel<any>>(
      url,
      container,
      this.getHttpHeader(apiVisa)
    );
  }

  ping(ip_address: string, code: string) {
    const headers = new HttpHeaders();
    const url = `http://localhost:8085/revenue-terminals/ping?terminalIp=${ip_address}&terminalPort=8080&terminalId=${code}`;
    this.httpClient.get(url, { headers, responseType: 'text' }).subscribe((response: any) => {
      this.getWebsocketMessage(code);
    });
  }


  payByCard(ip_address: string, code: string, amount: any) {
    const headers = new HttpHeaders();
    const formattedAmount = Number(amount).toFixed(2);
    const url = `http://localhost:8085/revenue-terminals/card-sale?terminalIp=${ip_address}&terminalPort=8080&payAmount=${formattedAmount}&terminalId=${code}`;

    this.httpClient.post(url, { headers }).subscribe({
      next: (res: any) => {
        console.log('payByCard res', res);
      },
      error: (err: any) => {
        console.error('payByCard error', err);
      }
    });
    this.getWebsocketMessage(code);
  }


  resetWebsocketMessage() {
    this.webSocketService.resetMessage()
  }

  getWebsocketMessage(code) {
    this.socketSubscription = this.webSocketService.connect(code)
      .subscribe((message) => {
       console.log('Received message from WebSocket:', message);
       //console.log('message.pay_resp_error_desc:', message.pay_resp_error_desc);
       if(message.pay_resp_error_desc==='Transaction Cancelled'){

        const status = {
          status: "Payment Failed: Transaction Cancelled",
          type: "FAILED"
        };
        this.store.dispatch(PosActions.selectTerminalStatus({ status }));
      }
      else if(message.pay_resp_cvm_desc==='SIGNATURE IS REQUIRED'){
        const status = {
          status: "Payment Failed: SIGNATURE IS REQUIRED",
          type: "FAILED"
        };
        this.store.dispatch(PosActions.selectTerminalStatus({ status }));
      }
      else if(message.pay_resp_error_desc==='Timeout'){
        const status = {
          status: "Payment Failed: Timeout",
          type: "FAILED"
        };
        this.store.dispatch(PosActions.selectTerminalStatus({ status }));
      }
      else if(message.pay_resp_error_desc==='APPROVED'){
       // console.log('message',message);
        const cardNumber = message.pay_resp_card_no;
        const match = cardNumber.match(/\d{4}$/);
        //console.log('match',match);
        if (match) {
          console.log(match[0]);
        } else {
          console.log("No card number found.");
        }
        const status = {
          status: "Payment Success",
          type: "SUCCESS",
          cardNo: match ? match[0] : null
        };
        this.store.dispatch(PosActions.selectTerminalStatus({ status }));
      }
      });
  }

  public getCashierCollectionReport(inputModel: CashierCollectionReportInputDto, apiVisa: ApiVisa): Observable<ApiResponseModel<any>> {
    let url = this.getApiUrl(apiVisa);
    let formData = {

    };

    if (inputModel) formData = inputModel;
    console.log('formData',formData)
    url += "/cashier-collections/backoffice-ep";
    return this.httpClient.post<ApiResponseModel<any>>(
      url,
      formData,
      this.getHttpHeader(apiVisa)
    );
  }

  public getAllMTO(apiVisa: ApiVisa, pricingHdrGuid: any): Observable<ApiResponseModel<any>> {
    let url = this.apiUrl2;
    let formData = {pricingHdrGuid: pricingHdrGuid}
    if (this.endpoint_path2 && apiVisa.api_domain_url) {
      url = apiVisa.api_domain_url + this.endpoint_path2;
    }

    url += "/all/backoffice-ep";
    return this.httpClient.post<ApiResponseModel<any>>(
      url,
      formData,
      this.getHttpHeader(apiVisa)
    );
  }

  opencashDrawer(){
    let url = "http://localhost:8085/openCashDrawer";

    this.httpClient.post(url,{ responseType: 'text' }).subscribe((b:any)  => {

    })
  }
}

