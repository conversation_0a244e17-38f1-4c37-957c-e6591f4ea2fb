import { Injectable } from "@angular/core";
import { Actions, createEffect, ofType } from "@ngrx/effects";
import { Store } from "@ngrx/store";
import { UUID } from "angular2-uuid";
import { MatSnackBar } from '@angular/material/snack-bar';
import {
  ARAPService,
  bl_fi_generic_doc_ext_RowClass,
  bl_fi_generic_doc_line_RowClass,
  BranchService,
  CompanyService,
  LocationService,
  EntityService,
  GenericDocContainerModel,
  GenericDocLinkBackofficeEPService,
  SalesInvoiceService,
  CustomerService,
  IntercompanyService,
  GenericDocLinkContainerModel,
  GenDocLinkContainerModel,
  InternalPurchaseInvoiceNoStockInService,
  Pagination,
  PricingSchemeLinkService,
  PricingSchemeService,
  SupplierService,
  EmployeeService,
  GenericDocLineService,
  TenantUserProfileService,
  SubQueryService,
  GenericDocAttachmentService,
  DimensionService,
  SegmentCoaService,
  ProfitCenterService,
  ProjectCoaService,
  EInvoiceSelfBilledService,
  GenericDocumentCloneDTO,
  MyEInvoiceToIRBHdrLinesService,
  AppletSettingFilterItemCategoryLinkService,
  AppletSettingFilterItemCategoryLinkContainerModel,
  GenericDocLockService,
  GenericDocEditingLockDto,
  EinvoiceNotificationQueueService,
  CompanyEInvoicePrintableFormatHdrService,
  IncomingEInvoiceMatchedHistoryService,
  FinancialItemService,
} from "blg-akaun-ts-lib";
import { ToastrService } from "ngx-toastr";
import { AppConfig } from "projects/shared-utilities/visa";
import {EMPTY, forkJoin, of, iif, Observable, zip, timer, from} from 'rxjs';
import {
  catchError,
  exhaustMap,
  map,
  mergeMap,
  withLatestFrom,
  switchMap,
  tap,
  take,
  filter,
  delay,
} from "rxjs/operators";
import { ViewColumnFacade } from "../../../../facades/view-column.facade";
import { ToastConstants } from "../../../../models/constants/toast.constants";
import { HDRActions, PNSActions } from "../../../draft-controller/store/actions";
import {
  HDRSelectors,
  LinkSelectors,
  PaymentSelectors,
  PNSSelectors,
} from "../../../draft-controller/store/selectors";
import { DraftStates } from "../../../draft-controller/store/states";
import { PurchaseInvoiceActions } from "../actions";
import { PurchaseInvoiceSelectors } from "../selectors";
import { PurchaseInvoiceStates } from "../states";
import { AppletConstants } from "../../../../models/constants/applet-constants";
import moment from "moment";
import { ListingService } from "projects/shared-utilities/services/listing-service";
import { ListingInputModel } from "projects/shared-utilities/models/listing-input.model";
import { SessionSelectors } from 'projects/shared-utilities/modules/session/session-controller/selectors';
import { SessionStates } from 'projects/shared-utilities/modules/session/session-controller/states';
import { PDFDocument } from 'pdf-lib';
import { ApiService } from "../../../../services/api-service";
import { AppletSettings } from '../../../../models/applet-settings.model';

@Injectable()
export class PurchaseInvoiceEffects {
  apiVisa = AppConfig.apiVisa;

  loadBranchCompany$ = createEffect(() =>
    this.actions$.pipe(
      ofType(PurchaseInvoiceActions.loadBranchCompany),
      withLatestFrom(
        this.sessionStore.select(SessionSelectors.selectPersonalSettings),
        this.sessionStore.select(SessionSelectors.selectMasterSettings)
      ),
      exhaustMap(([action, personal, master]) => {
        this.store.dispatch(PurchaseInvoiceActions.loadBranchCompanySuccess({
          branch: null,
          company: null
        }));
        let DEFAULT_BRANCH = action.branchGuid ?? personal?.DEFAULT_BRANCH ?? master?.DEFAULT_BRANCH;
        let DEFAULT_COMPANY = action.compGuid ?? personal?.DEFAULT_COMPANY ?? master?.DEFAULT_COMPANY;


        if (DEFAULT_BRANCH) {
          return this.branchService.getByGuid(DEFAULT_BRANCH, this.apiVisa).pipe(
            mergeMap((branchRes) => {
              const branchAction = PurchaseInvoiceActions.loadBranchCompanySuccess({
                branch: branchRes.data,
                company: null,
              });
              if(!DEFAULT_COMPANY){
                DEFAULT_COMPANY = branchRes.data.bl_fi_mst_branch.comp_guid;
              }

              if (DEFAULT_COMPANY) {
                return this.companyService.getByGuid(DEFAULT_COMPANY, this.apiVisa).pipe(
                  map((compRes) => {
                    return PurchaseInvoiceActions.loadBranchCompanySuccess({
                      branch: branchRes.data,
                      company: compRes.data
                    });
                  }),
                  catchError((compErr) => {
                    console.error('Error in companyService:', compErr);
                    return of(
                      PurchaseInvoiceActions.loadBranchCompanyFailed({
                        err: compErr.message,
                      })
                    );
                  })
                );
              } else {
                return of(branchAction);
              }
            }),
            catchError((branchErr) => {
              console.error('Error in branchService:', branchErr);
              return of(
                PurchaseInvoiceActions.loadBranchCompanyFailed({
                  err: branchErr.message,
                })
              );
            })
          );
        } else {
          return EMPTY;
        }
      })
    )
  );

  selectChildItemPricingLink$ = createEffect(() => this.actions$.pipe(
    ofType(PurchaseInvoiceActions.selectChildItemPricingLink),
    withLatestFrom(
      this.store.select(PurchaseInvoiceSelectors.selectChildItems),
      this.store.select(PurchaseInvoiceSelectors.selectPricingSchemeHdr)
    ),
    mergeMap(([a, childItem, pricingSchemeHdr]) => {
      const guids: any[] = a.child.map(item => item.item_hdr_guid);
      const inputModel = {} as ListingInputModel;
      inputModel.searchColumns = [];
      inputModel.status = ['ACTIVE'];
      inputModel.orderBy = 'date_updated';
      inputModel.order = 'desc';
      inputModel.limit = 100;
      inputModel.offset = 0;
      inputModel.calcTotalRecords = true;
      inputModel.showCreatedBy = false;
      inputModel.showUpdatedBy = true;
      inputModel.filterLogical = 'AND';
      inputModel.filterConditions = [];
      inputModel.filters = {
        "txn_class": "PNS"
      }
      inputModel.filterConditions.push({
        "filterColumn": "hdr.guid",
        "filterValues": guids,
        "filterOperator": "IN"
      });
      inputModel.filterConditions.push({
        "filterColumn": "bl_fi_mst_pricing_scheme_link.guid_pricing_scheme_hdr",
        "filterValues": [pricingSchemeHdr],
        "filterOperator": "="
      });

      inputModel.joins = [
        {
          'tableName': 'bl_fi_mst_pricing_scheme_link',
          'joinColumn': 'hdr.guid=bl_fi_mst_pricing_scheme_link.item_hdr_guid',
          'columns': ["guid_pricing_scheme_hdr", "item_hdr_guid", "purchase_unit_price"],
          'joinType': 'inner join'
        }
      ];
      inputModel.childs = [];

      return this.listingService.get("fi-item", inputModel, this.apiVisa)
        .pipe(
          map(b => {
            b.data.forEach(data => {
              const selectedChild = childItem.find(i => i.item_hdr_guid === data.guid);
              if (selectedChild) {
                const updatedResult = { ...selectedChild, unitPrice: data.bl_fi_mst_pricing_scheme_link_purchase_unit_price };
                this.store.dispatch(PurchaseInvoiceActions.updateChildItem({ child: updatedResult }));
              }
            });

            return PurchaseInvoiceActions.selectChildItemPricingLinkSuccess({ price: [] });
          }),
          catchError(error => {
            this.viewColFacade.showFailedToast(error);
            return of(PurchaseInvoiceActions.selectChildItemPricingLinkFailed({ error }));
          })
        );
    })
  ));

  getInvItem$ = createEffect(() =>
    this.actions$.pipe(
      ofType(PurchaseInvoiceActions.selectLineItem),
      map((action) => {
        let query = `
        SELECT inv.guid as requiredGuid
        FROM bl_inv_mst_item_hdr AS inv
        INNER JOIN bl_fi_mst_item_hdr AS fi
        ON inv.guid_fi_mst_item = fi.guid
        WHERE fi.guid = '${action.lineItem.item_guid}'
      `;
        return {
          subquery: query,
          table: "bl_inv_mst_item_hdr",
        };
      }),
      switchMap((req) =>
        this.subQueryService.post(req, AppConfig.apiVisa).pipe(
          map((resolve) => {
            if (resolve.data.length)
              return PurchaseInvoiceActions.selectInvItem({
                invItem: resolve.data[0],
              });
            else return PurchaseInvoiceActions.selectInvItem({ invItem: null });
          })
        )
      )
    )
  );

  selectCopyDepartmentFromHdrFromEditLine$ = createEffect(() => this.actions$.pipe(
    ofType(PurchaseInvoiceActions.selectCopyDepartmentFromHdrFromEditLine),
    withLatestFrom(
      this.store.select(PurchaseInvoiceSelectors.selectSegment),
      this.store.select(PurchaseInvoiceSelectors.selectProject),
      this.store.select(PurchaseInvoiceSelectors.selectProfitCenter),
      this.store.select(PurchaseInvoiceSelectors.selectDimension)
    ),
    exhaustMap(([a, segmentHdr, projectHdr, profitCenterHdr, dimensionHdr]) => {
      let result = a.item.guid_segment === segmentHdr?.guid
      && a.item.guid_project === projectHdr?.guid
      && a.item.guid_profit_center === profitCenterHdr?.guid
      && a.item.guid_dimension === dimensionHdr?.guid
      return of(PurchaseInvoiceActions.selectCopyDepartmentFromHdr({ value: result }));
    })
  ));

  loadSegmentLine$ = createEffect(() => this.actions$.pipe(
    ofType(PurchaseInvoiceActions.loadSegmentLine),
    withLatestFrom(
      this.store.select(PurchaseInvoiceSelectors.selectSegment)
    ),
    exhaustMap(([a, segmentHdr]) => {
      if(a.guid!==segmentHdr?.guid){
        return this.segmentCoaService.getByGuid(a.guid, this.apiVisa).pipe(
          map((c: any) => {
            return PurchaseInvoiceActions.loadSegmentLineSuccess({ segment: c.data.bl_fi_mst_segment });
          }),
          catchError(err => {
            return of(PurchaseInvoiceActions.loadSegmentLineFailed({ error: err.message }));
          })
        )
      }else{
        return of(PurchaseInvoiceActions.loadSegmentLineSuccess({ segment: segmentHdr }));
      }
  }
  )));

  loadProjectLine$ = createEffect(() => this.actions$.pipe(
    ofType(PurchaseInvoiceActions.loadProjectLine),
    withLatestFrom(
      this.store.select(PurchaseInvoiceSelectors.selectProject)
    ),
    exhaustMap(([a, projectHdr]) => {
      if(a.guid!==projectHdr?.guid){
        return this.projectCoaService.getByGuid(a.guid, this.apiVisa).pipe(
          map((c: any) => {
            return PurchaseInvoiceActions.loadProjectLineSuccess({ project: c.data.bl_fi_mst_project });
          }),
          catchError(err => {
            return of(PurchaseInvoiceActions.loadProjectLineFailed({ error: err.message }));
          })
        )
      }else{
        return of(PurchaseInvoiceActions.loadProjectLineSuccess({ project: projectHdr }));
      }
    }
  )));

  loadProfitCenterLine$ = createEffect(() => this.actions$.pipe(
    ofType(PurchaseInvoiceActions.loadProfitCenterLine),
    withLatestFrom(
      this.store.select(PurchaseInvoiceSelectors.selectProfitCenter)
    ),
    exhaustMap(([a, profitCenterHdr]) => {
      if(a.guid!==profitCenterHdr?.guid){
        return this.profitCenterService.getByGuid(a.guid, this.apiVisa).pipe(
          map((c: any) => {
            return PurchaseInvoiceActions.loadProfitCenterLineSuccess({ profitCenter: c.data.bl_fi_mst_profit_center });
          }),
          catchError(err => {
            return of(PurchaseInvoiceActions.loadProfitCenterLineFailed({ error: err.message }));
          })
        )
      }else{
        return of(PurchaseInvoiceActions.loadProfitCenterLineSuccess({ profitCenter: profitCenterHdr }));
      }
    }
  )));

  loadDimensionLine$ = createEffect(() => this.actions$.pipe(
    ofType(PurchaseInvoiceActions.loadDimensionLine),
    withLatestFrom(
      this.store.select(PurchaseInvoiceSelectors.selectDimension)
    ),
    exhaustMap(([a, dimensionHdr]) => {
      if(a.guid!==dimensionHdr?.guid){
        return this.glDimensionService.getByGuid(a.guid, this.apiVisa).pipe(
          map((c: any) => {
            return PurchaseInvoiceActions.loadDimensionLineSuccess({ dimension: c.data.bl_fi_mst_gl_dimension });
          }),
          catchError(err => {
            return of(PurchaseInvoiceActions.loadDimensionLineFailed({ error: err.message }));
          })
        )
      }else{
        return of(PurchaseInvoiceActions.loadDimensionLineSuccess({ dimension: dimensionHdr }));
      }
    }
  )));

  loadSegment$ = createEffect(() => this.actions$.pipe(
    ofType(PurchaseInvoiceActions.loadSegment),
    exhaustMap(a => this.segmentCoaService.getByGuid(a.guid, this.apiVisa).pipe(
      map((c: any) => {
        return PurchaseInvoiceActions.loadSegmentSuccess({ segment: c.data.bl_fi_mst_segment });
      }),
      catchError(err => {
        return of(PurchaseInvoiceActions.loadSegmentFailed({ error: err.message }));
      })
    )
  )));

  loadProject$ = createEffect(() => this.actions$.pipe(
    ofType(PurchaseInvoiceActions.loadProject),
    exhaustMap(a => this.projectCoaService.getByGuid(a.guid, this.apiVisa).pipe(
      map((c: any) => {
        return PurchaseInvoiceActions.loadProjectSuccess({ project: c.data.bl_fi_mst_project });
      }),
      catchError(err => {
        return of(PurchaseInvoiceActions.loadProjectFailed({ error: err.message }));
      })
    )
  )));

  loadProfitCenter$ = createEffect(() => this.actions$.pipe(
    ofType(PurchaseInvoiceActions.loadProfitCenter),
    exhaustMap(a => this.profitCenterService.getByGuid(a.guid, this.apiVisa).pipe(
      map((c: any) => {
        return PurchaseInvoiceActions.loadProfitCenterSuccess({ profitCenter: c.data.bl_fi_mst_profit_center });
      }),
      catchError(err => {
        return of(PurchaseInvoiceActions.loadProfitCenterFailed({ error: err.message }));
      })
    )
  )));

  loadDimension$ = createEffect(() => this.actions$.pipe(
    ofType(PurchaseInvoiceActions.loadDimension),
    exhaustMap(a => this.glDimensionService.getByGuid(a.guid, this.apiVisa).pipe(
      map((c: any) => {
        return PurchaseInvoiceActions.loadDimensionSuccess({ dimension: c.data.bl_fi_mst_gl_dimension });
      }),
      catchError(err => {
        return of(PurchaseInvoiceActions.loadDimensionFailed({ error: err.message }));
      })
    )
  )));

  loadPurchaseInvoice$ = createEffect(() =>
    this.actions$.pipe(
      ofType(PurchaseInvoiceActions.loadPurchaseInvoiceInit),
      switchMap((action) =>
        this.piService
          .getByCriteriaSnapshot(action.pagination, this.apiVisa)
          .pipe(
            mergeMap((b) => {
              const source: Observable<GenericDocContainerModel>[] = [];
              b.data.forEach((doc) => {
                source.push(
                  zip(
                    doc.bl_fi_generic_doc_hdr.code_branch
                      ? of(null)
                      : doc.bl_fi_generic_doc_hdr.guid_branch
                      ? this.branchService
                          .getByGuid(
                            doc.bl_fi_generic_doc_hdr.guid_branch?.toString(),
                            this.apiVisa
                          )
                          .pipe(catchError((err) => of(err)))
                      : of(null),
                    (<any>doc.bl_fi_generic_doc_hdr.doc_entity_hdr_json)
                      ?.entityName
                      ? (<any>doc.bl_fi_generic_doc_hdr.doc_entity_hdr_json)
                          .entityName
                      : doc.bl_fi_generic_doc_hdr.doc_entity_hdr_guid
                      ? this.supplierService
                          .getByGuid(
                            doc.bl_fi_generic_doc_hdr.doc_entity_hdr_guid?.toString(),
                            this.apiVisa
                          )
                          .pipe(catchError((err) => of(err)))
                      : of(null),
                    this.profileService
                      .getProfileName(
                        this.apiVisa,
                        doc.bl_fi_generic_doc_hdr.created_by_subject_guid
                          ? doc.bl_fi_generic_doc_hdr.created_by_subject_guid.toString()
                          : null
                      )
                      .pipe(catchError((err) => of(err))),
                    doc.bl_fi_generic_doc_hdr.pic_entity_01_name
                      ? of(null)
                      : doc.bl_fi_generic_doc_hdr.pic_entity_01
                      ? this.empService
                          .getByGuid(
                            doc.bl_fi_generic_doc_hdr.pic_entity_01.toString(),
                            this.apiVisa
                          )
                          .pipe(catchError((err) => of(err)))
                      : of(null),
                    doc.bl_fi_generic_doc_hdr.guid_dimension
                    ? this.glDimensionService.getByGuidPromise(
                        doc.bl_fi_generic_doc_hdr.guid_dimension.toString(),
                        this.apiVisa
                      )
                    : of (null),
                    doc.bl_fi_generic_doc_hdr.guid_segment
                    ? this.segmentCoaService.getByGuidPromise(
                        doc.bl_fi_generic_doc_hdr.guid_segment.toString(),
                        this.apiVisa
                      )
                    : of (null),
                    doc.bl_fi_generic_doc_hdr.guid_profit_center
                    ? this.profitCenterService.getByGuidPromise(
                        doc.bl_fi_generic_doc_hdr.guid_profit_center.toString(),
                        this.apiVisa
                      )
                    : of (null),
                    doc.bl_fi_generic_doc_hdr.guid_project
                    ? this.projectCoaService.getByGuidPromise(
                        doc.bl_fi_generic_doc_hdr.guid_project.toString(),
                        this.apiVisa
                      )
                    : of (null),
                  ).pipe(
                    map(([b_a, b_b, b_c, b_d, glDimension, segment, profitCenter, project]: any[]) => {
                      doc = Object.assign(
                        {
                          branch_code: doc.bl_fi_generic_doc_hdr.code_branch
                            ? doc.bl_fi_generic_doc_hdr.code_branch
                            : b_a.error
                            ? b_a.error.code
                            : b_a.data.bl_fi_mst_branch.code,
                        },
                        {
                          supplierName: (<any>(
                            doc.bl_fi_generic_doc_hdr.doc_entity_hdr_json
                          ))?.entityName
                            ? (<any>(
                                doc.bl_fi_generic_doc_hdr.doc_entity_hdr_json
                              )).entityName
                            : b_b.error
                            ? b_b.error.code
                            : b_b.data.bl_fi_mst_entity_hdr.name,
                        },
                        {
                          created_by_name: b_c.error
                            ? b_c.error.code
                            : b_c.data,
                        },
                        {
                          purchaser: doc.bl_fi_generic_doc_hdr
                            .pic_entity_01_name
                            ? doc.bl_fi_generic_doc_hdr.pic_entity_01_name
                            : b_d?.error
                            ? ""
                            : b_d?.data.bl_fi_mst_entity_hdr.name,
                        },
                        {
                          glDimension: glDimension ? glDimension.data.bl_fi_mst_gl_dimension.code : ''
                        },
                        {
                          segment: segment ? segment.data.bl_fi_mst_segment.code : ''
                        },
                        {
                          profitCenter: profitCenter ? profitCenter.data.bl_fi_mst_profit_center.code : ''
                        },
                        {
                          project: project ? project.data.bl_fi_mst_project.code : ''
                        },
                        doc
                      );
                      return doc;
                    })
                  )
                );
              });
              return iif(
                () => b.data.length > 0,
                forkJoin(source).pipe(
                  map((b_inner) => {
                    b.data = b_inner;
                    return b;
                  })
                ),
                of(b)
              );
            })
          )
          .pipe(
            map((a) => {
              if (a.data.length > 0) {
                // this.previousSnapshot = a.data[0].bl_fi_generic_doc_hdr.guid.toString();
                this.snapshot =
                  a.data[
                    a.data.length - 1
                  ].bl_fi_generic_doc_hdr.guid.toString();
              }
              return PurchaseInvoiceActions.loadPurchaseInvoiceSuccess({
                purchaseInvoice: a.data,
                snapshotGuid: this.snapshot,
              });
            }),
            catchError((err) => {
              console.log("Error", err);
              return of(
                PurchaseInvoiceActions.loadPurchaseInvoiceFailed({
                  error: err.message,
                })
              );
            })
          )
      )
    )
  );
  snapshot: string;

  searchPurchaseInvoice$ = createEffect(() =>
    this.actions$.pipe(
      ofType(PurchaseInvoiceActions.searchPurchaseInvoiceInit),
      switchMap((action) =>
        this.piService
          .getBySnapshotCustomHdrLinesQuery(action.searchDto, this.apiVisa)
          .pipe(
            mergeMap((b) => {
              const source: Observable<GenericDocContainerModel>[] = [];
              b.data.forEach((doc) => {
                source.push(
                  zip(
                    doc.bl_fi_generic_doc_hdr.code_branch
                      ? of(null)
                      : doc.bl_fi_generic_doc_hdr.guid_branch
                      ? this.branchService
                          .getByGuid(
                            doc.bl_fi_generic_doc_hdr.guid_branch?.toString(),
                            this.apiVisa
                          )
                          .pipe(catchError((err) => of(err)))
                      : of(null),
                    (<any>doc.bl_fi_generic_doc_hdr.doc_entity_hdr_json)
                      ?.entityName
                      ? (<any>doc.bl_fi_generic_doc_hdr.doc_entity_hdr_json)
                          .entityName
                      : doc.bl_fi_generic_doc_hdr.doc_entity_hdr_guid
                      ? this.supplierService
                          .getByGuid(
                            doc.bl_fi_generic_doc_hdr.doc_entity_hdr_guid?.toString(),
                            this.apiVisa
                          )
                          .pipe(catchError((err) => of(err)))
                      : of(null),
                    doc.bl_fi_generic_doc_hdr.pic_entity_01_name
                      ? of(null)
                      : doc.bl_fi_generic_doc_hdr.pic_entity_01
                      ? this.empService
                          .getByGuid(
                            doc.bl_fi_generic_doc_hdr.pic_entity_01.toString(),
                            this.apiVisa
                          )
                          .pipe(catchError((err) => of(err)))
                      : of(null),

                    this.entityService
                      .getByGuid(
                        doc.bl_fi_generic_doc_hdr.doc_entity_hdr_guid.toString(),
                        this.apiVisa
                      )
                      .pipe(catchError((err) => of(err))),
                    doc.bl_fi_generic_doc_hdr.guid_dimension
                    ? this.glDimensionService.getByGuidPromise(
                        doc.bl_fi_generic_doc_hdr.guid_dimension.toString(),
                        this.apiVisa
                      )
                    : of (null),
                    doc.bl_fi_generic_doc_hdr.guid_segment
                    ? this.segmentCoaService.getByGuidPromise(
                        doc.bl_fi_generic_doc_hdr.guid_segment.toString(),
                        this.apiVisa
                      )
                    : of (null),
                    doc.bl_fi_generic_doc_hdr.guid_profit_center
                    ? this.profitCenterService.getByGuidPromise(
                        doc.bl_fi_generic_doc_hdr.guid_profit_center.toString(),
                        this.apiVisa
                      )
                    : of (null),
                    doc.bl_fi_generic_doc_hdr.guid_project
                    ? this.projectCoaService.getByGuidPromise(
                        doc.bl_fi_generic_doc_hdr.guid_project.toString(),
                        this.apiVisa
                      )
                    : of (null),
                  ).pipe(
                    map(([b_a, b_b, b_d, entity, glDimension, segment, profitCenter, project]: any[]) => {
                      doc = Object.assign(
                        {
                          branch_code: doc.bl_fi_generic_doc_hdr.code_branch
                            ? doc.bl_fi_generic_doc_hdr.code_branch
                            : b_a.error
                            ? b_a.error.code
                            : b_a.data.bl_fi_mst_branch.code,
                        },
                        {
                          supplierName: (<any>(
                            doc.bl_fi_generic_doc_hdr.doc_entity_hdr_json
                          ))?.entityName
                            ? (<any>(
                                doc.bl_fi_generic_doc_hdr.doc_entity_hdr_json
                              )).entityName
                            : entity.data.bl_fi_mst_entity_hdr
                            ? entity.data.bl_fi_mst_entity_hdr.name
                            : b_b.error
                            ? b_b.error.code
                            : b_b.data.bl_fi_mst_entity_hdr.name,
                        },
                        {
                          purchaser: doc.bl_fi_generic_doc_hdr
                            .pic_entity_01_name
                            ? doc.bl_fi_generic_doc_hdr.pic_entity_01_name
                            : b_d
                            ? b_d.error
                              ? b_d.error.code
                              : b_d.data.bl_fi_mst_entity_hdr.name
                            : "",
                        },
                        {
                          glDimension: glDimension ? glDimension.data.bl_fi_mst_gl_dimension.code : ''
                        },
                        {
                          segment: segment ? segment.data.bl_fi_mst_segment.code : ''
                        },
                        {
                          profitCenter: profitCenter ? profitCenter.data.bl_fi_mst_profit_center.code : ''
                        },
                        {
                          project: project ? project.data.bl_fi_mst_project.code : ''
                        },
                        doc
                      );
                      return doc;
                    })
                  )
                );
              });
              return iif(
                () => b.data.length > 0,
                forkJoin(source).pipe(
                  map((b_inner) => {
                    b.data = b_inner;
                    return b;
                  })
                ),
                of(b)
              );
            })
          )
          .pipe(
            map((a) => {
              if (a.data.length > 0) {
                // this.previousSnapshot = a.data[0].bl_fi_generic_doc_hdr.guid.toString();
                this.snapshot =
                  a.data[
                    a.data.length - 1
                  ].bl_fi_generic_doc_hdr.guid.toString();
              }
              return PurchaseInvoiceActions.loadPurchaseInvoiceSuccess({
                purchaseInvoice: a.data,
                snapshotGuid: this.snapshot,
              });
            }),
            catchError((err) => {
              console.log("Error", err);
              return of(
                PurchaseInvoiceActions.loadPurchaseInvoiceFailed({
                  error: err.message,
                })
              );
            })
          )
      )
    )
  );

  getTotalRecords$ = createEffect(() =>
    this.actions$.pipe(
      ofType(PurchaseInvoiceActions.getTotalRecords),
      switchMap((action) =>
        this.piService
          .getBySnapshotCountQuery(action.searchDto, this.apiVisa)
          .pipe(
            map((a) => {
              return PurchaseInvoiceActions.getTotalRecordsSuccess({
                totalRecords: +a.data,
              });
            }),
            catchError((err) => {
              return of(
                PurchaseInvoiceActions.getTotalRecordsFailed({
                  error: err.message,
                })
              );
            })
          )
      )
    )
  );

  convertToActive$ = createEffect(
    () =>
      this.actions$.pipe(
        ofType(PurchaseInvoiceActions.convertToActiveInit),
        withLatestFrom(
          this.draftStore.select(HDRSelectors.selectHdr),
          this.draftStore.select(PNSSelectors.selectAll),
          this.draftStore.select(PaymentSelectors.selectAll),
          this.store.select(PurchaseInvoiceSelectors.selectTempDoc),
          this.store.select(PurchaseInvoiceSelectors.getMyConversionActionState)
        ),
        filter(
          ([action, hdr, pns, stl, temp, myConversionActionState]) =>
            !myConversionActionState
        ),
        map(([action, hdr, pns, stl, temp]) => {
          const container = new GenericDocContainerModel();
          (hdr.guid = temp.bl_fi_generic_doc_hdr.guid),
            (container.bl_fi_generic_doc_hdr = hdr);
          container.bl_fi_generic_doc_hdr.revision =
            temp.bl_fi_generic_doc_hdr.revision;
          container.bl_fi_generic_doc_hdr.status = "ACTIVE";
          container.bl_fi_generic_doc_hdr.doc_source_type = "INTERNAL";
          container.bl_fi_generic_doc_hdr.amount_signum = -1;
          container.bl_fi_generic_doc_hdr.created_by_subject_guid =
            localStorage.getItem("guid");
          container.bl_fi_generic_doc_hdr.client_doc_type =
            "INTERNAL_PURCHASE_INVOICE_NO_STOCK_IN";
          container.bl_fi_generic_doc_hdr.xtn_doc_ref_1 = hdr.xtn_doc_ref_1;
          container.bl_fi_generic_doc_hdr.xtn_doc_ref_2 = hdr.xtn_doc_ref_2;
          container.bl_fi_generic_doc_hdr.xtn_doc_ref_3 = hdr.xtn_doc_ref_3;
          container.bl_fi_generic_doc_hdr.xtn_doc_ref_4 = hdr.xtn_doc_ref_4;
          container.bl_fi_generic_doc_hdr.xtn_doc_ref_5 = hdr.xtn_doc_ref_5;
          container.bl_fi_generic_doc_hdr.date_txn = new Date(
            container.bl_fi_generic_doc_hdr.date_txn
          );

          stl.forEach((l) => (l.guid = null));

          pns.forEach((item: any) => {
            item.date_txn = container.bl_fi_generic_doc_hdr.date_txn;
            // If condition doesnt matter
            if (
              item.item_sub_type === "SERIAL_NUMBER" &&
              item.serial_no &&
              Array.isArray(item.serial_no)
            ) {
              const serialArr = item.serial_no.map((sn) => sn["sn_id"]);
              item.serial_no = {
                serialNumbers: serialArr,
              };
            }
            if (
              item.line_property_json &&
              (<any>item.line_property_json).delivery_instructions
            ) {
              const ext = new bl_fi_generic_doc_ext_RowClass();
              ext.guid_doc_hdr = hdr.guid.toString();
              ext.guid_doc_line = item.guid;
              ext.param_code = "REQUESTED_DELIVERY_DATE";
              ext.param_name = "REQUESTED_DELIVERY_DATE";
              ext.param_type = "DATE";
              ext.value_datetime = (<any>(
                item.line_property_json
              )).delivery_instructions.deliveryDate;
              ext.value_json = (<any>(
                item.line_property_json
              )).delivery_instructions;
              container.bl_fi_generic_doc_ext.push(ext);
            }
            if (hdr.delivery_branch_guid && hdr.delivery_location_guid) {
              item.delivery_branch_guid = hdr.delivery_branch_guid
                ? hdr.delivery_branch_guid
                : item.delivery_branch_guid;
              item.delivery_location_guid = hdr.delivery_location_guid
                ? hdr.delivery_location_guid
                : item.delivery_location_guid;
              item.delivery_branch_code = hdr.delivery_branch_code
                ? hdr.delivery_branch_code
                : item.delivery_branch_code;
              item.delivery_location_code = hdr.delivery_location_code
                ? hdr.delivery_location_code
                : item.delivery_location_code;
            }
          });

          container.bl_fi_generic_doc_line = [...pns, ...stl];
          // Create Extension

          return container;
        }),
        exhaustMap((e) => {
          if (e.bl_fi_generic_doc_hdr.pic_entity_01 !== null) {
            // check if pic_entity_01 is not null
            return this.entityService
              .getByGuid(
                e.bl_fi_generic_doc_hdr.pic_entity_01.toString(),
                this.apiVisa
              )
              .pipe(
                map((b_inner) => {
                  //  assign name
                  e.bl_fi_generic_doc_hdr.pic_entity_01_name =
                    b_inner.data.bl_fi_mst_entity_hdr.name;
                  return e;
                })
              );
          } else {
            return of(e);
          }
        }),
        exhaustMap((a) =>
          this.branchService
            .getByGuid(
              a.bl_fi_generic_doc_hdr.guid_branch.toString(),
              this.apiVisa
            )
            .pipe(
              map((b_inner) => {
                a.bl_fi_generic_doc_hdr.guid_comp =
                  b_inner.data.bl_fi_mst_branch.comp_guid;
                //  assign code_branch
                a.bl_fi_generic_doc_hdr.code_branch =
                  b_inner.data.bl_fi_mst_branch.code;
                a.bl_fi_generic_doc_line.forEach((lineItem) => {
                  lineItem.guid_comp = a.bl_fi_generic_doc_hdr.guid_comp;
                  lineItem.guid_store = a.bl_fi_generic_doc_hdr.guid_store;
                  lineItem.base_doc_line_ccy =
                    a.bl_fi_generic_doc_hdr.base_doc_ccy;
                  lineItem.base_doc_line_xrate =
                    a.bl_fi_generic_doc_hdr.base_doc_xrate;
                  lineItem.doc_ccy = a.bl_fi_generic_doc_hdr.doc_ccy;
                  lineItem.foreign_ccy = a.bl_fi_generic_doc_hdr.foreign_ccy;
                });
                return a;
              })
            )
        ),
        // to get the company code
        exhaustMap((b) =>
          this.compService
            .getByGuid(
              b.bl_fi_generic_doc_hdr.guid_comp.toString(),
              this.apiVisa
            )
            .pipe(
              map((c_inner) => {
                // assign the code_company
                b.bl_fi_generic_doc_hdr.code_company =
                  c_inner.data.bl_fi_mst_comp.code;
                return b;
              })
            )
        ),
        // to get the location code
        exhaustMap((c) =>
          this.locService
            .getByGuid(
              c.bl_fi_generic_doc_hdr.guid_store.toString(),
              this.apiVisa
            )
            .pipe(
              map((d_inner) => {
                // assign the code_location
                c.bl_fi_generic_doc_hdr.code_location =
                  d_inner.data.bl_inv_mst_location.code;
                return c;
              })
            )
        ),
        exhaustMap((e) =>
          forkJoin({
            deliveryBranch: e.bl_fi_generic_doc_hdr?.delivery_branch_guid
              ? this.branchService.getByGuid(
                  e.bl_fi_generic_doc_hdr.delivery_branch_guid.toString(),
                  this.apiVisa
                )
              : of(null), // Emit null if delivery_branch_guid is empty

            deliveryLocation: e.bl_fi_generic_doc_hdr?.delivery_location_guid
              ? this.locService.getByGuid(
                  e.bl_fi_generic_doc_hdr.delivery_location_guid.toString(),
                  this.apiVisa
                )
              : of(null), // Emit null if delivery_location_guid is empty
          }).pipe(
            map(({ deliveryBranch, deliveryLocation }) => {
              // Handle the result of both API calls
              if (e.bl_fi_generic_doc_hdr.delivery_branch_guid !== null) {
                e.bl_fi_generic_doc_hdr.delivery_branch_code =
                  deliveryBranch?.data?.bl_fi_mst_branch.code || "";
                e.bl_fi_generic_doc_line.forEach((lineItem) => {
                  lineItem.delivery_branch_code =
                    deliveryBranch?.data?.bl_fi_mst_branch.code || "";
                });
              }
              if (e.bl_fi_generic_doc_hdr.delivery_location_guid !== null) {
                e.bl_fi_generic_doc_hdr.delivery_location_code =
                  deliveryLocation?.data?.bl_inv_mst_location.code || "";
                e.bl_fi_generic_doc_line.forEach((lineItem) => {
                  lineItem.delivery_location_code =
                    deliveryLocation?.data?.bl_inv_mst_location.code || "";
                });
              }
              return e;
            })
          )
        ),
        exhaustMap((d) =>
          this.piService.put(d, this.apiVisa).pipe(
            map((a: any) => {
              this.store
                .select(PurchaseInvoiceSelectors.getKOAttachments)
                .pipe(
                  take(1) // Ensure that the subscription is automatically unsubscribed after the first emission
                )
                .subscribe((data) => {
                  data.forEach((value) => {
                    if (
                      d.bl_fi_generic_doc_hdr.guid ===
                      a.data.bl_fi_generic_doc_hdr.guid
                    ) {
                      this.genDocAttachmentService
                        .copyAttachment(
                          value,
                          a.data.bl_fi_generic_doc_hdr.guid,
                          this.apiVisa
                        )
                        .subscribe(
                          () => console.log("Attachment copied successfully"),
                          (err) =>
                            console.error("Error copying attachment:", err)
                        );
                    }
                  });
                });
              setTimeout(() => {
                this.updateDraft(a.data);
              }, 2000);
              return PurchaseInvoiceActions.convertToActiveSuccess({
                hdrGuid: a.data.bl_fi_generic_doc_hdr.guid,
              });
            }),
            catchError((err) => {
              let errMsg = err.message;
              if (err.data.length) {
                if (
                  err.error.data[0].errorCode ===
                  "API_TNT_DM_ERP_GEN_DOC_LINE_DRAFT_LOCK_SERIAL_NUMBER_OBJECT_SN_ID_COMP_GUID_INV_ITEM_GUID_SERVER_DOC_TYPE_COMBINATION_ALREADY_EXISTS"
                ) {
                  errMsg = "One of the serial numbers is already locked";
                }
              }
              this.toastr.error(errMsg, "Error", {
                tapToDismiss: true,
                progressBar: true,
                timeOut: 0,
                extendedTimeOut: 0,
              });
              return of(
                PurchaseInvoiceActions.convertToActiveFailed({
                  error: err.message,
                })
              );
            })
          )
        )
      ) as any
  );

  knockOffDocument$ = createEffect(() =>
    this.actions$.pipe(
      ofType(PurchaseInvoiceActions.knockOffDocument),
      withLatestFrom(
        this.store.select(PurchaseInvoiceSelectors.selectTempDoc),
      ),
      map(([action, temp]) => {
        const container = {
          ...action.link,
          targetGenDocGuid: temp?.bl_fi_generic_doc_hdr?.guid
        };
        return container;
      }),
      exhaustMap((container) =>
        this.genDocLinkService.knockOffDoc(container, this.apiVisa).pipe(
          tap((response: any) => {
            console.log("DOC CREATED", response.data);
            // Delay updateDraft by 2 seconds using a timer
            timer(2000).subscribe(() => {
              this.updateDraft(response.data);
            });
          }),
          map(() =>
            PurchaseInvoiceActions.knockOffDocumentSuccess()
          ),
          catchError((err) => {
            console.log("Error", err);
            this.toastr.error(err.error.code, "Error", {
              tapToDismiss: true,
              progressBar: true,
              timeOut: 1300,
            });
            return of(
              PurchaseInvoiceActions.knockOffDocumentFail({
                error: err.error.code,
              })
            );
          })
        )
      )
    )
  );

  createGenDocLink$ = createEffect(() =>
    this.actions$.pipe(
      ofType(
        PurchaseInvoiceActions.convertToActiveSuccess,
        PurchaseInvoiceActions.editPurchaseInvoiceSuccess
      ),
      withLatestFrom(
        this.store.select(LinkSelectors.selectAll),
        this.store.select(HDRSelectors.selectHdr)
      ),
      map(([action, link, hdr]) => {
        let container: GenDocLinkContainerModel[] = [];

        link.forEach((l) => {
          if (l.status === "DRAFT") {
            let docLink: GenDocLinkContainerModel =
              new GenDocLinkContainerModel();
            l.guid = null;
            if (action.hdrGuid) {
              l.guid_doc_2_hdr = action.hdrGuid;
            } else {
              l.guid_doc_2_hdr = hdr.guid;
            }
            l.status = "ACTIVE";
            docLink.bl_fi_generic_doc_link = l;
            docLink.bl_fi_generic_doc_link.created_date = new Date();
            docLink.bl_fi_generic_doc_link.updated_date = new Date();
            container.push(docLink);
          }
        });

        console.log("Doc Link Container", container);
        return container;
      }),
      exhaustMap((d) =>
        this.genDocLinkService.post(d, this.apiVisa).pipe(
          map((a: any) => {
            return PurchaseInvoiceActions.createPurchaseInvoiceGenDocLinkSuccess();
          }),
          catchError((err) => {
            this.toastr.error(err.message, "Error", {
              tapToDismiss: true,
              progressBar: true,
              timeOut: 0,
              extendedTimeOut: 0,
            });
            return of(
              PurchaseInvoiceActions.createPurchaseInvoiceGenDocLinkFailed({
                error: err.message,
              })
            );
          })
        )
      )
    )
  );

  editPurchaseInvoice$ = createEffect(() =>
    this.actions$.pipe(
      ofType(PurchaseInvoiceActions.editPurchaseInvoiceInit),
      withLatestFrom(
        this.store.select(PurchaseInvoiceSelectors.selectInvoice),
        this.draftStore.select(HDRSelectors.selectHdr),
        this.draftStore.select(PNSSelectors.selectAll),
        this.draftStore.select(PaymentSelectors.selectAll)
        //this.draftStore.select(LinkSelectors.selectAll)
      ),
      map(([action, genDoc, hdr, pns, stl]) => {
        console.log("HDR" , hdr);
        genDoc.bl_fi_generic_doc_hdr = hdr;

        if (!genDoc.bl_fi_generic_doc_hdr.delivery_branch_guid) {
          genDoc.bl_fi_generic_doc_hdr.delivery_branch_guid = genDoc.bl_fi_generic_doc_hdr.guid_branch;
          genDoc.bl_fi_generic_doc_hdr.delivery_branch_code = genDoc.bl_fi_generic_doc_hdr.code_branch;
        }


        // Check for delivery location
        if (!genDoc.bl_fi_generic_doc_hdr.delivery_location_guid) {
          genDoc.bl_fi_generic_doc_hdr.delivery_location_guid = genDoc.bl_fi_generic_doc_hdr.guid_store;
          genDoc.bl_fi_generic_doc_hdr.delivery_location_code = genDoc.bl_fi_generic_doc_hdr.code_location;
        }

        stl.forEach((l) => {
          // hacky condition to check if payment exist before
          if (l.guid.toString().length !== 36) l.guid = null;
        });
        genDoc.bl_fi_generic_doc_line = [...pns, ...stl];
        //genDoc.bl_fi_generic_doc_link = updatedLink;
        genDoc.bl_fi_generic_doc_link = null;
        // extension logic here
        pns.forEach((item: any) => {
          // If condition doesn't matter
          if (
            item.item_sub_type === "SERIAL_NUMBER" &&
            item.serial_no &&
            Array.isArray(item.serial_no)
          ) {
            const serialArr = item.serial_no.map((sn) => sn["sn_id"]);
            item.serial_no = {
              serialNumbers: serialArr,
            };
          }
          if (
            item.line_property_json &&
            (<any>item.line_property_json).delivery_instructions
          ) {
            const extIndex = genDoc.bl_fi_generic_doc_ext.findIndex(
              (x) =>
                x.param_code === "REQUESTED_DELIVERY_DATE" &&
                x.guid_doc_line === item.guid
            );
            if (extIndex >= 0) {
              genDoc.bl_fi_generic_doc_ext[extIndex].value_datetime = (<any>(
                item.line_property_json
              )).delivery_instructions.deliveryDate;
              genDoc.bl_fi_generic_doc_ext[extIndex].value_json = (<any>(
                item.line_property_json
              )).delivery_instructions;
              genDoc.bl_fi_generic_doc_ext[extIndex].status = item.status;
            } else {
              const ext = new bl_fi_generic_doc_ext_RowClass();
              ext.guid_doc_hdr = hdr.guid.toString();
              ext.guid_doc_line = item.guid;
              ext.param_code = "REQUESTED_DELIVERY_DATE";
              ext.param_name = "REQUESTED_DELIVERY_DATE";
              ext.param_type = "DATE";
              ext.value_datetime = (<any>(
                item.line_property_json
              )).delivery_instructions.deliveryDate;
              ext.value_json = (<any>(
                item.line_property_json
              )).delivery_instructions;
              genDoc.bl_fi_generic_doc_ext.push(ext);
            }
          }
        });
        return genDoc;
      }),
      exhaustMap((a) =>
        this.piService
          .getByGuid(a.bl_fi_generic_doc_hdr.guid.toString(), this.apiVisa)
          .pipe(
            map((b_inner) => {
              a.bl_fi_generic_doc_hdr.server_doc_1 =
                b_inner.data.bl_fi_generic_doc_hdr.server_doc_1;
              a.bl_fi_generic_doc_hdr.server_doc_2 =
                b_inner.data.bl_fi_generic_doc_hdr.server_doc_2;
              a.bl_fi_generic_doc_hdr.server_doc_3 =
                b_inner.data.bl_fi_generic_doc_hdr.server_doc_3;
              a.bl_fi_generic_doc_hdr.server_doc_4 =
                b_inner.data.bl_fi_generic_doc_hdr.server_doc_4;
              a.bl_fi_generic_doc_hdr.server_doc_5 =
                b_inner.data.bl_fi_generic_doc_hdr.server_doc_5;

              a.bl_fi_generic_doc_hdr.client_doc_1 =
                b_inner.data.bl_fi_generic_doc_hdr.client_doc_1;
              a.bl_fi_generic_doc_hdr.client_doc_2 =
                b_inner.data.bl_fi_generic_doc_hdr.client_doc_2;
              a.bl_fi_generic_doc_hdr.client_doc_3 =
                b_inner.data.bl_fi_generic_doc_hdr.client_doc_3;
              a.bl_fi_generic_doc_hdr.client_doc_4 =
                b_inner.data.bl_fi_generic_doc_hdr.client_doc_4;
              a.bl_fi_generic_doc_hdr.client_doc_5 =
                b_inner.data.bl_fi_generic_doc_hdr.client_doc_5;
              a.bl_fi_generic_doc_hdr.revision =
                b_inner.data.bl_fi_generic_doc_hdr.revision;

              return a;
            })
          )
      ),
      exhaustMap((a) =>
        this.branchService
          .getByGuid(
            a.bl_fi_generic_doc_hdr.guid_branch.toString(),
            this.apiVisa
          )
          .pipe(
            map((b_inner) => {
              a.bl_fi_generic_doc_hdr.guid_comp =
                b_inner.data.bl_fi_mst_branch.comp_guid;
              //  assign code_branch
              a.bl_fi_generic_doc_hdr.code_branch =
                b_inner.data.bl_fi_mst_branch.code;
              a.bl_fi_generic_doc_line.forEach((lineItem) => {
                lineItem.guid_comp = a.bl_fi_generic_doc_hdr.guid_comp;
                lineItem.guid_store = a.bl_fi_generic_doc_hdr.guid_store;
                lineItem.base_doc_line_ccy =
                  a.bl_fi_generic_doc_hdr.base_doc_ccy;
                lineItem.base_doc_line_xrate =
                  a.bl_fi_generic_doc_hdr.base_doc_xrate;
                lineItem.doc_ccy = a.bl_fi_generic_doc_hdr.doc_ccy;
                lineItem.foreign_ccy = a.bl_fi_generic_doc_hdr.foreign_ccy;
              });
              return a;
            })
          )
      ),
      // to get the company code
      exhaustMap((b) =>
        this.compService
          .getByGuid(b.bl_fi_generic_doc_hdr.guid_comp.toString(), this.apiVisa)
          .pipe(
            map((c_inner) => {
              // assign the code_company
              b.bl_fi_generic_doc_hdr.code_company =
                c_inner.data.bl_fi_mst_comp.code;
              return b;
            })
          )
      ),
      // to get the location code
      exhaustMap((c) =>
        this.locService
          .getByGuid(
            c.bl_fi_generic_doc_hdr.guid_store.toString(),
            this.apiVisa
          )
          .pipe(
            map((d_inner) => {
              // assign the code_location
              c.bl_fi_generic_doc_hdr.code_location =
                d_inner.data.bl_inv_mst_location.code;
              return c;
            })
          )
      ),
      exhaustMap((d) =>
        this.piService.put(d, this.apiVisa).pipe(
          map((a: any) => {
            this.viewColFacade.showSuccessToast(
              ToastConstants.editPurchaseInvoiceSuccess
            );
            this.viewColFacade.updateInstance(0, {
              deactivateAdd: false,
              deactivateList: false,
            });
            this.viewColFacade.resetIndex(0);
            return PurchaseInvoiceActions.editPurchaseInvoiceSuccess({
              hdrGuid: a.data.bl_fi_generic_doc_hdr.guid,
            });
          }),
          catchError((err) => {
            let errMsg = err.message;
            if (err.error.data.length) {
              if (
                err.error.data[0].errorCode ===
                "API_TNT_DM_ERP_GEN_DOC_LINE_DRAFT_LOCK_SERIAL_NUMBER_OBJECT_SN_ID_COMP_GUID_INV_ITEM_GUID_SERVER_DOC_TYPE_COMBINATION_ALREADY_EXISTS"
              ) {
                errMsg = "One of the serial numbers is already locked";
              }
            }
            this.toastr.error(errMsg, "Error", {
              tapToDismiss: true,
              progressBar: true,
              timeOut: 0,
              extendedTimeOut: 0,
            });
            return of(
              PurchaseInvoiceActions.editPurchaseInvoiceFailed({
                error: err.message,
              })
            );
          })
        )
      )
    )
  );

  updateGenDocLink$ = createEffect(() =>
    this.actions$.pipe(
      ofType(
        PurchaseInvoiceActions.editPurchaseInvoiceSuccess,
      ),
      withLatestFrom(
        this.store.select(HDRSelectors.selectHdr),
        this.store.select(LinkSelectors.selectAll)
      ),
      map(([action, hdr, link]) => {
        let container: GenDocLinkContainerModel[] = [];

        // let updatedLink = link.filter((a) => a.status === "ACTIVE");
        link.forEach((l) => {
          if (
            (l.status === "ACTIVE" || l.status === "DELETED") &&
            l.guid !== null
          ) {
            let docLink: GenDocLinkContainerModel =
              new GenDocLinkContainerModel();
            l.guid_doc_2_hdr = hdr.guid;
            docLink.bl_fi_generic_doc_link = l;
            container.push(docLink);
          }
        });
        console.log("Updated Doc Link Container", container);
        return container;
      }),
      exhaustMap((d) =>
        this.genDocLinkService.put(d, this.apiVisa).pipe(
          map((a: any) => {
            return PurchaseInvoiceActions.editPurchaseInvoiceGenDocLinkSuccess();
          }),
          catchError((err) => {
            this.toastr.error(err.message, "Error", {
              tapToDismiss: true,
              progressBar: true,
              timeOut: 0,
              extendedTimeOut: 0,
            });
            return of(
              PurchaseInvoiceActions.editPurchaseInvoiceGenDocLinkFailed({
                error: err.message,
              })
            );
          })
        )
      )
    )
  );

  updateGenDocLinkonFinal$ = createEffect(() =>
    this.actions$.pipe(
      ofType(PurchaseInvoiceActions.editPurchaseInvoiceFinalSuccess),
      withLatestFrom(
        this.store.select(HDRSelectors.selectHdr),
        this.store.select(LinkSelectors.selectAll)
      ),
      map(([action, hdr, link]) => {
        let container: GenDocLinkContainerModel[] = [];

        link.forEach((l) => {
          if ((l.status === "ACTIVE" || l.status === "DELETED") && l.guid !== null) {
            let docLink: GenDocLinkContainerModel = new GenDocLinkContainerModel();
            l.guid_doc_2_hdr = hdr.guid;
            docLink.bl_fi_generic_doc_link = l;
            container.push(docLink);
          }
        });

        return { container, originalAction: action }; // Preserve original action
      }),
      exhaustMap(({ container, originalAction }) =>
        this.genDocLinkService.put(container, this.apiVisa).pipe(
          map(() =>
            PurchaseInvoiceActions.editGenDocLinkFinalSuccess({
              status: { posting_status: "FINAL" },
              doc: originalAction.doc, // <-- Pass the original doc here
            })
          ),
          catchError((err) => {
            this.toastr.error(err.message, "Error", {
              tapToDismiss: true,
              progressBar: true,
              timeOut: 1300,
            });
            return of(
              PurchaseInvoiceActions.editGenDocLinkFinalFailed({
                error: err.message,
              })
            );
          })
        )
      )
    )
  );

  unlockDocument$ = createEffect(() =>
    this.actions$.pipe(
      ofType(
        PurchaseInvoiceActions.editPurchaseInvoiceSuccess,
        PurchaseInvoiceActions.editPurchaseInvoiceFinalSuccess,
        PurchaseInvoiceActions.unlockDocument
      ),
      map((action) => {
        let genDocLockDto = new GenericDocEditingLockDto();
        genDocLockDto.generic_doc_hdr_guid = action.hdrGuid;

        console.log("genDocLockDto", genDocLockDto);
        return genDocLockDto;
      }),
      exhaustMap((d) =>
        this.genericDocLockService.unblockDocument(d, this.apiVisa).pipe(
          map((a: any) => {
            return PurchaseInvoiceActions.unlockDocumentSuccess();
          }),
          catchError((err) => {
            this.toastr.error(err.message, "Error", {
              tapToDismiss: true,
              progressBar: true,
              timeOut: 0,
              extendedTimeOut: 0,
            });
            return of(
              PurchaseInvoiceActions.unlockDocumentFailed({
                error: err.message,
              })
            );
          })
        )
      )
    )
  );

  deletePurchaseInvoice$ = createEffect(() =>
    this.actions$.pipe(
      ofType(PurchaseInvoiceActions.deletePurchaseInvoiceInit),
      withLatestFrom(this.store.select(HDRSelectors.selectHdr)),
      exhaustMap(([a, b]) =>
        this.piService.delete(b.guid.toString(), this.apiVisa).pipe(
          map(() => {
            this.viewColFacade.showSuccessToast(
              ToastConstants.deletePurchaseInvoiceSuccess
            );
            this.viewColFacade.updateInstance(0, {
              deactivateAdd: false,
              deactivateList: false,
            });
            this.viewColFacade.resetIndex(0);
            // this.viewColFacade.resetDraft();
            return PurchaseInvoiceActions.deletePurchaseInvoiceSuccess();
          }),
          catchError((err) => {
            this.viewColFacade.showFailedToast(err);
            return of(
              PurchaseInvoiceActions.deletePurchaseInvoiceFailed({
                error: err.message,
              })
            );
          })
        )
      )
    )
  );

  selectEntity$ = createEffect(() =>
    this.actions$.pipe(
      ofType(PurchaseInvoiceActions.selectPurchaseInvoice),
      exhaustMap((action) =>
        this.entityService
          .getEntityByHdrGuids(
            action.genDoc.bl_fi_generic_doc_hdr.doc_entity_hdr_guid.toString(),
            this.apiVisa
          )
          .pipe(
            map((a: any) => {
              return PurchaseInvoiceActions.selectEntityOnEdit({
                entity: { entity: a.data[0], contact: null },
              });
            })
          )
      )
    )
  );

  printJasperPdf$ = createEffect(() =>
    this.actions$.pipe(
      ofType(PurchaseInvoiceActions.printJasperPdfInit),
      withLatestFrom(
        this.draftStore.select(HDRSelectors.selectHdr),
        this.store.select(PurchaseInvoiceSelectors.selectPrintableFormatGuid)
      ),
      exhaustMap(([action, hdr, printableGuid]) =>
        this.piService
          .printJasperPdf(
            hdr.guid.toString(),
            "INTERNAL_PURCHASE_ORDER_PRINT_SERVICE",
            printableGuid,
            this.apiVisa
          )
          .pipe(
            map((a) => {
              const downloadURL = window.URL.createObjectURL(a);
              const link = document.createElement("a");
              link.href = downloadURL;
              link.download = `${hdr.server_doc_1}.pdf`;
              link.click();
              link.remove();
              this.viewColFacade.showSuccessToast(
                ToastConstants.exportPurchaseInvoiceSuccess
              );
              return PurchaseInvoiceActions.printJasperPdfSuccess();
            }),
            catchError((err) => {
              this.viewColFacade.showFailedToast(err);
              return of(PurchaseInvoiceActions.printJasperPdfFailed());
            })
          )
      )
    )
  );

  printMultipleJasperPdf$ = createEffect(() =>
    this.actions$.pipe(
      ofType(PurchaseInvoiceActions.printMultipleJasperPdfInit),
      exhaustMap((action) => {
        const printBlobArray$ = action.guids.map((guid) =>
          this.piService.printJasperPdf(
            guid.toString(),
            "INTERNAL_PURCHASE_ORDER_PRINT_SERVICE",
            action.printable,
            AppConfig.apiVisa
          )
        );
        return forkJoin(printBlobArray$).pipe(
          exhaustMap(async (blobs) => {
            const mergedPdf = await PDFDocument.create();
            for (const blob of blobs) {
              const arrayBuffer = await blob.arrayBuffer();
              const pdf = await PDFDocument.load(arrayBuffer);
              const copiedPages = await mergedPdf.copyPages(pdf, pdf.getPageIndices());
              copiedPages.forEach((page) => mergedPdf.addPage(page));
            }
            const mergedPdfBlob = new Blob([await mergedPdf.save()], { type: 'application/pdf' });
            const downloadURL = window.URL.createObjectURL(mergedPdfBlob);

            let fileName = 'INTERNAL_PURCHASE_INVOICE_NO_STOCK_IN';
            if (action.docNumbers.length === 1) {
              fileName += `-${action.docNumbers[0]}`;
            } else {
              fileName += '-PURINV';
            }
            if (action.preview) {

              const previewWindow = window.open('', '_blank');
              const blobURL = window.URL.createObjectURL(mergedPdfBlob);
              previewWindow.document.write(`
                <html>
                  <head>
                    <title>${fileName}</title>
                    <style>
                      body {
                        font-family: Arial, sans-serif;
                        margin: 0;
                        padding: 0;
                        background-color: #f4f4f4;
                        text-align: center;
                      }
                      iframe {
                        width: 100%;
                        height: 90vh;
                        border: none;
                      }
                      .download-container {
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        height: 10vh;
                      }
                      #downloadBtn {
                        color: #007bff;
                        border: 2px solid #007bff;
                        padding: 10px 20px;
                        font-size: 16px;
                        border-radius: 5px;
                        cursor: pointer;
                        background: none;
                        font-weight: bold;
                        transition: color 0.3s ease, border-color 0.3s ease;
                      }
                      #downloadBtn:hover {
                        color: #0056b3;
                        border-color: #0056b3;
                      }
                      #downloadBtn:active {
                        color: #004085;
                        border-color: #004085;
                      }
                    </style>
                  </head>
                  <body>
                    <iframe src="${blobURL}"></iframe>
                    <div class="download-container">
                      <button id="downloadBtn" aria-label="Download PDF">Download PDF</button>
                    </div>
                    <script>
                      document.getElementById('downloadBtn').addEventListener('click', function() {
                        const a = document.createElement('a');
                        a.href = '${blobURL}';
                        a.download = '${fileName}.pdf'; // Set file name for download
                        a.click();
                      });
                    </script>
                  </body>
                </html>
              `);
            } else {

              const downloadButton = document.createElement('a');
              downloadButton.href = downloadURL;
              downloadButton.download = `${fileName}.pdf`;
              downloadButton.style.display = 'none';
              document.body.appendChild(downloadButton);
              downloadButton.click();
              document.body.removeChild(downloadButton);
            }
            return PurchaseInvoiceActions.printJasperPdfSuccess();
          }),
          catchError((err) => {
            this.viewColFacade.showFailedToast(err);
            return of(PurchaseInvoiceActions.printJasperPdfFailed());
          })
        );
      })
    )
  );

  // addContra$ = createEffect(() =>
  //   this.actions$.pipe(
  //     ofType(PurchaseInvoiceActions.addContraInit),
  //     withLatestFrom(
  //       this.draftStore.select(HDRSelectors.selectHdr),
  //       this.store.select(PurchaseInvoiceSelectors.selectContraDoc)
  //     ),
  //     map(([action, hdr, contra]) => {
  //       action.contra.bl_fi_generic_doc_arap_contra = {
  //         ...action.contra.bl_fi_generic_doc_arap_contra,
  //         guid_doc_1_hdr: hdr.guid.toString(),
  //         guid_doc_2_hdr: contra.bl_fi_generic_doc_hdr.guid.toString(),
  //         server_doc_type_doc_1: hdr.server_doc_type.toString(),
  //         server_doc_type_doc_2:
  //           contra.bl_fi_generic_doc_hdr.server_doc_type.toString(),
  //         date_doc_1: hdr.date_txn.toString(),
  //         date_doc_2: contra.bl_fi_generic_doc_hdr.date_txn.toString(),
  //       };
  //       return action.contra;
  //     }),
  //     exhaustMap((a) =>
  //       this.arapService.post(a, this.apiVisa).pipe(
  //         map((a_a) => {
  //           this.viewColFacade.showSuccessToast("Contra Added Successfully");
  //           this.viewColFacade.resetIndex(2);
  //           return PurchaseInvoiceActions.addContraSuccess();
  //         }),
  //         catchError((err) => {
  //           this.viewColFacade.showFailedToast(err);
  //           return of(
  //             PurchaseInvoiceActions.addContraFailed({ error: err.message })
  //           );
  //         })
  //       )
  //     )
  //   )
  // );

  updateContra$ = createEffect(() =>
    this.actions$.pipe(
      ofType(PurchaseInvoiceActions.updateContraInit),
      withLatestFrom(
        this.store.select(PurchaseInvoiceSelectors.selectContraLink)
      ),
      exhaustMap(([action, link]) =>
        this.arapService
          .getByGuid(link.bl_fi_generic_doc_arap_contra.guid, this.apiVisa)
          .pipe(
            map(a => {
              a.data.bl_fi_generic_doc_arap_contra.date_txn = action.txn_date
              return a.data;
            }),
            exhaustMap(b => this.arapService.updateContraTxnDate(b, this.apiVisa).pipe(
              map(a_a => {
                this.toastr.success(
                  'Contra details saved successfully',
                  'Success',
                  {
                    tapToDismiss: true,
                    progressBar: true,
                    timeOut: 1300
                  }
                );
                return PurchaseInvoiceActions.updateContraSuccess();
              }),
              catchError(err => {
                this.toastr.error(
                  err.message,
                  'Error',
                  {
                    tapToDismiss: true,
                    progressBar: true,
                    timeOut: 0,
                    extendedTimeOut: 0
                  }
                );
                return of(PurchaseInvoiceActions.updateContraFailed({ error: err.message }));
              })
            )),
            catchError(err => {
              this.toastr.error(
                err.message,
                'Error',
                {
                  tapToDismiss: true,
                  progressBar: true,
                  timeOut: 0,
                  extendedTimeOut: 0
                }
              );
              return of(PurchaseInvoiceActions.updateContraFailed({ error: err.message }));
            })
          )
      )
    )
  );

  deleteContra$ = createEffect(() =>
    this.actions$.pipe(
      ofType(PurchaseInvoiceActions.deleteContraInit),
      withLatestFrom(
        this.store.select(PurchaseInvoiceSelectors.selectContraLink)
      ),
      exhaustMap(([action, link]) =>
        this.arapService
          .delete(link.bl_fi_generic_doc_arap_contra.guid, this.apiVisa)
          .pipe(
            map((a_a) => {
              this.viewColFacade.showSuccessToast(
                "Contra Deleted Successfully"
              );
              this.viewColFacade.resetIndex(2);
              return PurchaseInvoiceActions.deleteContraSuccess();
            }),
            catchError((err) => {
              this.viewColFacade.showFailedToast(err);
              return of(
                PurchaseInvoiceActions.deleteContraFailed({
                  error: err.message,
                })
              );
            })
          )
      )
    )
  );

  // refactor
  selectPricingLink$ = createEffect(() =>
    this.actions$.pipe(
      ofType(PurchaseInvoiceActions.selectPricingSchemeLink),
      mergeMap((action) => {
        const paging = new Pagination();
        paging.conditionalCriteria.push({
          columnName: "item_hdr_guid",
          operator: "=",
          value: action.item.item_guid.toString(),
        });
        return this.pslService.getByCriteria(paging, this.apiVisa).pipe(
          mergeMap((result: any) => {
            let allIds = result.data.map((id) =>
              this.getPricing(
                id.bl_fi_mst_pricing_scheme_link.guid_pricing_scheme_hdr.toString()
              )
            );
            return forkJoin(...allIds).pipe(
              map((idDataArray) => {
                result.data.forEach((eachContact, index) => {
                  eachContact.pricing_hdr =
                    idDataArray[index]?.data.bl_fi_mst_pricing_scheme_hdr.name;
                });
                return PurchaseInvoiceActions.selectPricingSchemeLinkSuccess({
                  pricing: result.data,
                });
              })
            );
          })
        );
      })
    )
  );
  prevIndex: number;
  prevLocalState: any;

  getPricing(guid: string) {
    return this.pricingService.getByGuid(guid, this.apiVisa);
  }

  addPricingSchemeLink$ = createEffect(() =>
    this.actions$.pipe(
      ofType(PurchaseInvoiceActions.addPricingSchemeLinkInit),
      exhaustMap((action) =>
        this.pslService.post(action.link, this.apiVisa).pipe(
          map((resp) => {
            this.viewColFacade.showSuccessToast(
              "Pricing Scheme Added Successfully"
            );
            return PurchaseInvoiceActions.addPricingSchemeLinkSuccess();
          }),
          catchError((err) => {
            this.viewColFacade.showFailedToast(err);
            return of(
              PurchaseInvoiceActions.addPricingSchemeLinkFailed({
                error: err.message,
              })
            );
          })
        )
      )
    )
  );

  editPricingSchemeLink$ = createEffect(() =>
    this.actions$.pipe(
      ofType(PurchaseInvoiceActions.editPricingSchemeLinkInit),
      exhaustMap((action) =>
        this.pslService.put(action.link, this.apiVisa).pipe(
          map((resp) => {
            this.viewColFacade.showSuccessToast(
              "Pricing Scheme Updated Successfully"
            );
            return PurchaseInvoiceActions.editPricingSchemeLinkSuccess();
          }),
          catchError((err) => {
            this.viewColFacade.showFailedToast(err);
            return of(
              PurchaseInvoiceActions.editPricingSchemeLinkFailed({
                error: err.message,
              })
            );
          })
        )
      )
    )
  );

  updatePostingStatus$ = createEffect(() =>
    this.actions$.pipe(
      ofType(
        PurchaseInvoiceActions.updatePostingStatus,
        PurchaseInvoiceActions.editGenDocLinkFinalSuccess
      ),
      mergeMap((action) =>
        this.piService
          .updatePostingStatus(
            action.status,
            this.apiVisa,
            action.doc.bl_fi_generic_doc_hdr.guid.toString()
          )
          .pipe(
            map((a) => {
              this.viewColFacade.showSuccessToast("Posting Successfully");
              this.viewColFacade.updateInstance(0, {
                deactivateAdd: false,
                deactivateList: false,
              });
              this.viewColFacade.resetIndex(0);
              return PurchaseInvoiceActions.updatePostingStatusSuccess({
                doc: a.data,
              });
            }),
            catchError((err) => {
              let errMsg = err.message;
              if (err.error.data.length) {
                const hasSerialErrors = err.error.data
                  .map((error) => error.errorCode)
                  .filter(
                    (message) =>
                      message.includes(
                        "FISCAL_PERIOD_LOCKED",
                        "BL_INV_SERIAL_NUMBER_HDR_OBJECT_SERIAL_NUMBER_DOES_NOT_EXIST_AT_LOCATION"
                      ) ||
                      message.includes(
                        "GENERIC_DOC_LINE_QTY_BASE_AND_SERIAL_NUMBER_QTY_DOES_NOT_MATCH"
                      ) ||
                      message.includes(
                        "GENERIC_DOC_LINE_SERIAL_NUMBER_ID_IS_DUPLICATED"
                      )
                  );
                //console.log('hasSerialErrors',hasSerialErrors);
                if (hasSerialErrors) {
                  const uniqueErrorMessages = new Set<string>();
                  const filteredErrorMessages = err.error.data
                    .map((error) => error.shortMessage)
                    .filter((message) => {
                      if (!uniqueErrorMessages.has(message)) {
                        uniqueErrorMessages.add(message);
                        return true;
                      }
                      return false;
                    });

                  if (filteredErrorMessages.length > 0) {
                    errMsg = filteredErrorMessages.join(".\n") + ".\n";
                  }
                }
              }
              this.toastr.error(errMsg, "Error", {
                tapToDismiss: true,
                progressBar: true,
                timeOut: 0,
                extendedTimeOut: 0,
                enableHtml: true,
              });
              return of(
                PurchaseInvoiceActions.updatePostingStatusFailed({
                  error: err.message,
                })
              );
            })
          )
      )
    )
  );

autoPopUpPrintable$ = createEffect(() =>
  this.actions$.pipe(
    ofType(PurchaseInvoiceActions.updatePostingStatusSuccess),
    withLatestFrom(
      this.sessionStore.select(SessionSelectors.selectMasterSettings)
    ),
    exhaustMap(([action, settings,]) => {
      if (!settings?.ENABLE_AUTO_POPUP) return EMPTY;

      if (!settings?.PRINTABLE) {
        this.viewColFacade.showFailedToast({ message: 'No Default Printable Selected' });
        return of(PurchaseInvoiceActions.autoPopUpPrintableFailed({ error: 'No Default Printable Selected' }));
      }

      return this.piService.printJasperPdf(
        action.doc.bl_fi_generic_doc_hdr.guid.toString(),
        "INTERNAL_PURCHASE_ORDER_PRINT_SERVICE",
        settings.PRINTABLE,
        this.apiVisa
      ).pipe(
        tap((blob) => {
           const url = window.URL.createObjectURL(blob);
           window.open(url);
        }),
        map(() => PurchaseInvoiceActions.autoPopUpPrintableSuccess()),
        catchError((error) => {
          this.viewColFacade.showFailedToast({ message: 'Failed to generate printable' });
          return of(PurchaseInvoiceActions.autoPopUpPrintableFailed({ error }));
        })
      );
    })
  )
);

  landedCostsAllocation$ = createEffect(() =>
    this.actions$.pipe(
      ofType(PurchaseInvoiceActions.landedCostsAllocationInit),
      exhaustMap((a) =>
        this.genericDocLineService
          .setLandingCost(a.landnedCosts, this.apiVisa)
          .pipe(
            tap((response) => {
              let updatedLines: any[] = [];
              response.data.forEach((line) => {
                let updatedLine = { ...line.bl_fi_generic_doc_line };
                updatedLines.push(updatedLine);
              });
              console.log(updatedLines);
              this.draftStore.dispatch(PNSActions.editAllPNS({ updatedLines }));
            }),
            map((response) => {
              this.viewColFacade.showSuccessToast(
                ToastConstants.landedCostAllocationSuccess
              );
              return PurchaseInvoiceActions.landedCostsAllocationSuccess();
            }),
            catchError((err) => {
              this.viewColFacade.showFailedToast(err);
              return of(
                PurchaseInvoiceActions.landedCostsAllocationFailed({
                  error: err.message,
                })
              );
            })
          )
      )
    )
  );

  loadIntercompanySalesInvoices$ = createEffect(() =>
    this.actions$.pipe(
      ofType(PurchaseInvoiceActions.loadIntercompanySalesInvoiceInit),
      switchMap((action) =>
        this.siService
          .getByCriteriaSnapshot(action.pagination, this.apiVisa)
          .pipe(
            map((b) => {
              let filteredData = b.data.filter(
                (doc: any) =>
                  doc.bl_fi_generic_doc_hdr.entity_branch_hdr_guid != null &&
                  doc.bl_fi_generic_doc_hdr.intercompany_hdr_guid === null
              );

              if (filteredData.length > 0) {
                this.snapshot =
                  filteredData[
                    filteredData.length - 1
                  ].bl_fi_generic_doc_hdr.guid.toString();
              }

              console.log("Intercompany sales invoice data", filteredData);

              return PurchaseInvoiceActions.loadIntercompanySalesInvoiceSuccess(
                {
                  salesInvoices: filteredData,
                  snapshotGuid: this.snapshot,
                }
              );
            }),
            catchError((err) => {
              console.log(err);
              return of(
                PurchaseInvoiceActions.loadIntercompanySalesInvoiceFailed({
                  error: err.message,
                })
              );
            })
          )
      )
    )
  );

  createIntercompanyTransactions$ = createEffect(() =>
    this.actions$.pipe(
      ofType(PurchaseInvoiceActions.createIntercompanyTransactions),
      exhaustMap((action) =>
        this.intercompanyService
          .fromSalesInvToPurchaseInv(action.payload, this.apiVisa)
          .pipe(
            map((response: any) => {
              console.log("Intercompany transaction response", response);
              this.viewColFacade.updateInstance(0, {
                deactivateAdd: false,
                deactivateList: false,
              });
              this.viewColFacade.resetIndex(0);
              this.viewColFacade.showSuccessToast(
                "Intercompany Transaction Created Successfully"
              );
              return PurchaseInvoiceActions.createIntercompanyTransactionsSuccess(
                { response: response }
              );
            }),
            catchError((err) => {
              this.toastr.error(
                err.message,
                "Error: Please ensure that the Intercompany Branch is the same as the one selected under Main Details tab",
                {
                  tapToDismiss: true,
                  progressBar: true,
                  timeOut: 0,
                  extendedTimeOut: 0,
                }
              );
              return of(
                PurchaseInvoiceActions.createIntercompanyTransactionsFailed({
                  error: err.message,
                })
              );
            })
          )
      )
    )
  );
  discardPurchaseInvoice$ = createEffect(() =>
    this.actions$.pipe(
      ofType(PurchaseInvoiceActions.discardInit),
      exhaustMap((action) => {
        const discardArray$ = action.guids.map((guid) =>
          this.piService.discardGenericDocument(this.apiVisa, guid).pipe(
            map((resp) => {
              return { guid: guid, error: null };
            }),
            catchError((err) => of({ guid: guid, error: err }))
          )
        );
        return forkJoin(discardArray$).pipe(
          map((combinedResp) => {
            const discardPileLength = combinedResp.length;
            const failedRequests = combinedResp.filter((resp) => resp.error);
            const totalSuccess = discardPileLength - failedRequests.length;
            if (totalSuccess > 0) {
              const msg = !action.fromEdit
                ? `${totalSuccess}/${discardPileLength} documents discarded successfully`
                : `Document discarded successfully`;
              this.viewColFacade.showSuccessToast(msg);
              if (action.fromEdit) {
                this.viewColFacade.updateInstance(0, {
                  deactivateAdd: false,
                  deactivateList: false,
                });
                this.viewColFacade.resetIndex(0);
              }
            }
            if (failedRequests.length > 0) {
              const failMsg = !action.fromEdit
                ? `${failedRequests.length}/${discardPileLength} documents discarded unsuccessfully`
                : `Document discarded unsuccessfully`;
              this.viewColFacade.showFailedToast({
                message: failMsg,
              });
              failedRequests.forEach((fail) =>
                console.error(`${fail.guid}::`, fail.error)
              );
            }
            return PurchaseInvoiceActions.discardComplete({
              total: discardPileLength,
              successCount: totalSuccess,
              failureCount: failedRequests.length,
            });
          }),
          catchError((err) => {
            this.viewColFacade.showFailedToast(err);
            return of(PurchaseInvoiceActions.discardFailure({ error: err }));
          })
        );
      })
    )
  );

  getLatestGenDoc$ = createEffect(() =>
    this.actions$.pipe(
      ofType(
        PurchaseInvoiceActions.addContraSuccess,
        PurchaseInvoiceActions.deleteContraSuccess
      ),
      withLatestFrom(this.store.select(HDRSelectors.selectHdr)),
      map(([action, hdr]) => {
        return hdr;
      }),
      exhaustMap((b) =>
        this.piService.getByGuid(b.guid.toString(), AppConfig.apiVisa).pipe(
          map((a) => {
            return PurchaseInvoiceActions.updateAfterContra({ genDoc: a.data });
          }),
          catchError((err) => {
            // this.viewColFacade.showFailedToast(err);
            return of(
              PurchaseInvoiceActions.updateAfterContraFailed({ error: err })
            );
          })
        )
      )
    )
  );

  voidSalesInvoiceFinalInit$ = createEffect(() =>
    this.actions$.pipe(
      ofType(PurchaseInvoiceActions.voidPurchaseInvoiceInit),
      mergeMap((action) =>
        this.piService
          .voidGenericDocument(
            action.status,
            this.apiVisa,
            action.doc.bl_fi_generic_doc_hdr.guid.toString()
          )
          .pipe(
            map((a) => {
              this.viewColFacade.showSuccessToast(
                "Successfully Void the Document"
              );
              this.viewColFacade.updateInstance(0, {
                deactivateAdd: false,
                deactivateList: false,
              });
              this.viewColFacade.resetIndex(0);
              return PurchaseInvoiceActions.voidPurchaseInvoiceSuccess({
                doc: a.data,
              });
            }),
            catchError((err) => {
              let errMsg = err.message;
              if (err.error.code === "GENERIC_DOCUMENT_HAS_TARGET_LINKS") {
                errMsg = "The purchase invoice has already been returned.";
              }
              this.toastr.error(errMsg, "Error", {
                tapToDismiss: true,
                progressBar: true,
                timeOut: 0,
                extendedTimeOut: 0,
              });

              return of(
                PurchaseInvoiceActions.voidPurchaseInvoiceFailed({
                  error: err.message,
                })
              );
            })
          )
      )
    )
  );

  updateDraft(entity: GenericDocContainerModel) {
    this.piService
      .getByGuid(
        entity.bl_fi_generic_doc_hdr.guid.toString(),
        AppConfig.apiVisa
      )
      .pipe(
        take(1) // Ensure that the subscription is automatically unsubscribed after the first emission
      )
      .subscribe((response) => {
        const genDoc = { ...response.data };
        // this one action points to many reducer functions instead of firing many actions
        this.store.dispatch(
          PurchaseInvoiceActions.selectGUID({
            guid: entity.bl_fi_generic_doc_hdr.guid.toString(),
          })
        );
        this.store.dispatch(
          PurchaseInvoiceActions.selectPurchaseInvoice({ genDoc })
        );

        this.store.dispatch(
          PurchaseInvoiceActions.refreshArapListing({
            refreshArapListing: true,
          })
        );
        this.store.dispatch(
          PurchaseInvoiceActions.setEditMode({ editMode: true })
        );
        this.store.dispatch(
          PurchaseInvoiceActions.editMode({ editMode: true })
        );
        this.store.dispatch(
          PurchaseInvoiceActions.selectCompanyGuid({
            compGuid: response.data.bl_fi_generic_doc_hdr.guid_comp.toString(),
          })
        );
        genDoc.bl_fi_generic_doc_line.forEach((line: any) => {
          if (line.item_sub_type === "SERIAL_NUMBER") {
            if (!Array.isArray(line.serial_no)) {
              if (line.serial_no && line.serial_no.serialNumbers.length) {
                if (genDoc.bl_fi_generic_doc_hdr.posting_status !== "FINAL") {
                  this.draftStore.dispatch(
                    PNSActions.validatePNSSerialNo({ line })
                  );
                } else {
                  this.draftStore.dispatch(
                    PNSActions.mapToSerialNumberObject({
                      line: line,
                      postingStatus: "FINAL",
                    })
                  );
                }
              } else {
                this.draftStore.dispatch(
                  PNSActions.validatePNSNoSerialNo({ line })
                );
              }
            }
          }
        });
      });
  }

  editPurchaseInvoiceBeforeContra$ = createEffect(() =>
    this.actions$.pipe(
      ofType(
        PurchaseInvoiceActions.editInternalPurchaseInvoiceBeforeContraInit
      ),
      withLatestFrom(
        this.store.select(PurchaseInvoiceSelectors.selectInvoice),
        this.draftStore.select(HDRSelectors.selectHdr),
        this.draftStore.select(PNSSelectors.selectAll),
        this.draftStore.select(PaymentSelectors.selectAll)
        //this.draftStore.select(LinkSelectors.selectAll)
      ),
      map(([action, genDoc, hdr, pns, stl]) => {
        genDoc.bl_fi_generic_doc_hdr = hdr;

        if (!genDoc.bl_fi_generic_doc_hdr.delivery_branch_guid) {
          genDoc.bl_fi_generic_doc_hdr.delivery_branch_guid = genDoc.bl_fi_generic_doc_hdr.guid_branch;
          genDoc.bl_fi_generic_doc_hdr.delivery_branch_code = genDoc.bl_fi_generic_doc_hdr.code_branch;
        }


        // Check for delivery location
        if (!genDoc.bl_fi_generic_doc_hdr.delivery_location_guid) {
          genDoc.bl_fi_generic_doc_hdr.delivery_location_guid = genDoc.bl_fi_generic_doc_hdr.guid_store;
          genDoc.bl_fi_generic_doc_hdr.delivery_location_code = genDoc.bl_fi_generic_doc_hdr.code_location;
        }
        stl.forEach((l) => {
          // hacky condition to check if payment exist before
          if (l.guid.toString().length !== 36) l.guid = null;
        });
        genDoc.bl_fi_generic_doc_line = [...pns, ...stl];
        //genDoc.bl_fi_generic_doc_link = updatedLink;
        genDoc.bl_fi_generic_doc_link = null;
        // extension logic here
        pns.forEach((item: any) => {
          // If condition doesn't matter
          if (
            item.item_sub_type === "SERIAL_NUMBER" &&
            item.serial_no &&
            Array.isArray(item.serial_no)
          ) {
            const serialArr = item.serial_no.map((sn) => sn["sn_id"]);
            item.serial_no = {
              serialNumbers: serialArr,
            };
          }
          if (
            item.line_property_json &&
            (<any>item.line_property_json).delivery_instructions
          ) {
            const extIndex = genDoc.bl_fi_generic_doc_ext.findIndex(
              (x) =>
                x.param_code === "REQUESTED_DELIVERY_DATE" &&
                x.guid_doc_line === item.guid
            );
            if (extIndex >= 0) {
              genDoc.bl_fi_generic_doc_ext[extIndex].value_datetime = (<any>(
                item.line_property_json
              )).delivery_instructions.deliveryDate;
              genDoc.bl_fi_generic_doc_ext[extIndex].value_json = (<any>(
                item.line_property_json
              )).delivery_instructions;
              genDoc.bl_fi_generic_doc_ext[extIndex].status = item.status;
            } else {
              const ext = new bl_fi_generic_doc_ext_RowClass();
              ext.guid_doc_hdr = hdr.guid.toString();
              ext.guid_doc_line = item.guid;
              ext.param_code = "REQUESTED_DELIVERY_DATE";
              ext.param_name = "REQUESTED_DELIVERY_DATE";
              ext.param_type = "DATE";
              ext.value_datetime = (<any>(
                item.line_property_json
              )).delivery_instructions.deliveryDate;
              ext.value_json = (<any>(
                item.line_property_json
              )).delivery_instructions;
              genDoc.bl_fi_generic_doc_ext.push(ext);
            }
          }
        });
        return genDoc;
      }),
      exhaustMap((a) =>
        this.piService
          .getByGuid(a.bl_fi_generic_doc_hdr.guid.toString(), this.apiVisa)
          .pipe(
            map((b_inner) => {
              a.bl_fi_generic_doc_hdr.server_doc_1 =
                b_inner.data.bl_fi_generic_doc_hdr.server_doc_1;
              a.bl_fi_generic_doc_hdr.server_doc_2 =
                b_inner.data.bl_fi_generic_doc_hdr.server_doc_2;
              a.bl_fi_generic_doc_hdr.server_doc_3 =
                b_inner.data.bl_fi_generic_doc_hdr.server_doc_3;
              a.bl_fi_generic_doc_hdr.server_doc_4 =
                b_inner.data.bl_fi_generic_doc_hdr.server_doc_4;
              a.bl_fi_generic_doc_hdr.server_doc_5 =
                b_inner.data.bl_fi_generic_doc_hdr.server_doc_5;

              a.bl_fi_generic_doc_hdr.client_doc_1 =
                b_inner.data.bl_fi_generic_doc_hdr.client_doc_1;
              a.bl_fi_generic_doc_hdr.client_doc_2 =
                b_inner.data.bl_fi_generic_doc_hdr.client_doc_2;
              a.bl_fi_generic_doc_hdr.client_doc_3 =
                b_inner.data.bl_fi_generic_doc_hdr.client_doc_3;
              a.bl_fi_generic_doc_hdr.client_doc_4 =
                b_inner.data.bl_fi_generic_doc_hdr.client_doc_4;
              a.bl_fi_generic_doc_hdr.client_doc_5 =
                b_inner.data.bl_fi_generic_doc_hdr.client_doc_5;
              return a;
            })
          )
      ),
      exhaustMap((a) =>
        this.branchService
          .getByGuid(
            a.bl_fi_generic_doc_hdr.guid_branch.toString(),
            this.apiVisa
          )
          .pipe(
            map((b_inner) => {
              a.bl_fi_generic_doc_hdr.guid_comp =
                b_inner.data.bl_fi_mst_branch.comp_guid;
              //  assign code_branch
              a.bl_fi_generic_doc_hdr.code_branch =
                b_inner.data.bl_fi_mst_branch.code;
              a.bl_fi_generic_doc_line.forEach((lineItem) => {
                lineItem.guid_comp = a.bl_fi_generic_doc_hdr.guid_comp;
                lineItem.guid_store = a.bl_fi_generic_doc_hdr.guid_store;
                lineItem.base_doc_line_ccy =
                  a.bl_fi_generic_doc_hdr.base_doc_ccy;
                lineItem.base_doc_line_xrate =
                  a.bl_fi_generic_doc_hdr.base_doc_xrate;
                lineItem.doc_ccy = a.bl_fi_generic_doc_hdr.doc_ccy;
                lineItem.foreign_ccy = a.bl_fi_generic_doc_hdr.foreign_ccy;
              });
              return a;
            })
          )
      ),
      // to get the company code
      exhaustMap((b) =>
        this.compService
          .getByGuid(b.bl_fi_generic_doc_hdr.guid_comp.toString(), this.apiVisa)
          .pipe(
            map((c_inner) => {
              // assign the code_company
              b.bl_fi_generic_doc_hdr.code_company =
                c_inner.data.bl_fi_mst_comp.code;
              return b;
            })
          )
      ),
      // to get the location code
      exhaustMap((c) =>
        this.locService
          .getByGuid(
            c.bl_fi_generic_doc_hdr.guid_store.toString(),
            this.apiVisa
          )
          .pipe(
            map((d_inner) => {
              // assign the code_location
              c.bl_fi_generic_doc_hdr.code_location =
                d_inner.data.bl_inv_mst_location.code;
              return c;
            })
          )
      ),
      exhaustMap((d) =>
        this.piService.put(d, this.apiVisa).pipe(
          map((a: any) => {
            this.updateDraft(a.data);
            return PurchaseInvoiceActions.addContraInit();
          }),
          catchError((err) => {
            let errMsg = err.message;
            if (err.error.data.length) {
              if (
                err.error.data[0].errorCode ===
                "API_TNT_DM_ERP_GEN_DOC_LINE_DRAFT_LOCK_SERIAL_NUMBER_OBJECT_SN_ID_COMP_GUID_INV_ITEM_GUID_SERVER_DOC_TYPE_COMBINATION_ALREADY_EXISTS"
              ) {
                errMsg = "One of the serial numbers is already locked";
              }
            }
            this.toastr.error(errMsg, "Error", {
              tapToDismiss: true,
              progressBar: true,
              timeOut: 0,
              extendedTimeOut: 0,
            });
            return of(
              PurchaseInvoiceActions.editInternalPurchaseInvoiceBeforeContraFailed(
                {
                  error: err.message,
                }
              )
            );
          })
        )
      )
    )
  );

  addContraAfterSavingPI$ = createEffect(() =>
    this.actions$.pipe(
      ofType(PurchaseInvoiceActions.addContraInit),
      withLatestFrom(
        this.store.select(HDRSelectors.selectHdr),
        this.store.select(PurchaseInvoiceSelectors.selectContraDoc),
        this.store.select(PurchaseInvoiceSelectors.selectAddedContraDoc)
      ),
      map(([action, hdr, contraDoc, addedContraDoc]) => {
        addedContraDoc.bl_fi_generic_doc_arap_contra = {
          ...addedContraDoc.bl_fi_generic_doc_arap_contra,
          guid_doc_1_hdr: hdr.guid.toString(),
          guid_doc_2_hdr: contraDoc.bl_fi_generic_doc_hdr.guid.toString(),
          server_doc_type_doc_1: hdr.server_doc_type.toString(),
          server_doc_type_doc_2:
            contraDoc.bl_fi_generic_doc_hdr.server_doc_type.toString(),
          date_doc_1: hdr.date_txn.toString(),
          date_doc_2: contraDoc.bl_fi_generic_doc_hdr.date_txn.toString(),
        };
        return addedContraDoc;
      }),
      exhaustMap((a) =>
        this.arapService.post(a, this.apiVisa).pipe(
          map((a_a) => {
            this.viewColFacade.showSuccessToast(
              ToastConstants.contraAddedSuccess
            );
            this.onReturn();
            return PurchaseInvoiceActions.addContraSuccess();
          }),
          catchError((err) => {
            this.viewColFacade.showFailedToast(err);
            return of(
              PurchaseInvoiceActions.addContraFailed({
                error: err.message,
              })
            );
          })
        )
      )
    )
  );

  editPurchaseInvoiceBeforeFinal$ = createEffect(() =>
    this.actions$.pipe(
      ofType(PurchaseInvoiceActions.editPurchaseInvoiceFinalInit),
      withLatestFrom(
        this.store.select(PurchaseInvoiceSelectors.selectInvoice),
        this.draftStore.select(HDRSelectors.selectHdr),
        this.draftStore.select(PNSSelectors.selectAll),
        this.draftStore.select(PaymentSelectors.selectAll)
        //this.draftStore.select(LinkSelectors.selectAll)
      ),
      map(([action, genDoc, hdr, pns, stl]) => {
        genDoc.bl_fi_generic_doc_hdr = hdr;

        if (!genDoc.bl_fi_generic_doc_hdr.delivery_branch_guid) {
          genDoc.bl_fi_generic_doc_hdr.delivery_branch_guid = genDoc.bl_fi_generic_doc_hdr.guid_branch;
          genDoc.bl_fi_generic_doc_hdr.delivery_branch_code = genDoc.bl_fi_generic_doc_hdr.code_branch;
        }


        // Check for delivery location
        if (!genDoc.bl_fi_generic_doc_hdr.delivery_location_guid) {
          genDoc.bl_fi_generic_doc_hdr.delivery_location_guid = genDoc.bl_fi_generic_doc_hdr.guid_store;
          genDoc.bl_fi_generic_doc_hdr.delivery_location_code = genDoc.bl_fi_generic_doc_hdr.code_location;
        }

        stl.forEach((l) => {
          // hacky condition to check if payment exist before
          if (l.guid.toString().length !== 36) l.guid = null;
        });
        genDoc.bl_fi_generic_doc_line = [...pns, ...stl];
        //genDoc.bl_fi_generic_doc_link = updatedLink;
        genDoc.bl_fi_generic_doc_link = null;
        // extension logic here
        pns.forEach((item: any) => {
          // If condition doesn't matter
          if (
            item.item_sub_type === "SERIAL_NUMBER" &&
            item.serial_no &&
            Array.isArray(item.serial_no)
          ) {
            const serialArr = item.serial_no.map((sn) => sn["sn_id"]);
            item.serial_no = {
              serialNumbers: serialArr,
            };
          }
          if (
            item.line_property_json &&
            (<any>item.line_property_json).delivery_instructions
          ) {
            const extIndex = genDoc.bl_fi_generic_doc_ext.findIndex(
              (x) =>
                x.param_code === "REQUESTED_DELIVERY_DATE" &&
                x.guid_doc_line === item.guid
            );
            if (extIndex >= 0) {
              genDoc.bl_fi_generic_doc_ext[extIndex].value_datetime = (<any>(
                item.line_property_json
              )).delivery_instructions.deliveryDate;
              genDoc.bl_fi_generic_doc_ext[extIndex].value_json = (<any>(
                item.line_property_json
              )).delivery_instructions;
              genDoc.bl_fi_generic_doc_ext[extIndex].status = item.status;
            } else {
              const ext = new bl_fi_generic_doc_ext_RowClass();
              ext.guid_doc_hdr = hdr.guid.toString();
              ext.guid_doc_line = item.guid;
              ext.param_code = "REQUESTED_DELIVERY_DATE";
              ext.param_name = "REQUESTED_DELIVERY_DATE";
              ext.param_type = "DATE";
              ext.value_datetime = (<any>(
                item.line_property_json
              )).delivery_instructions.deliveryDate;
              ext.value_json = (<any>(
                item.line_property_json
              )).delivery_instructions;
              genDoc.bl_fi_generic_doc_ext.push(ext);
            }
          }
        });
        return genDoc;
      }),
      exhaustMap((a) =>
        this.piService
          .getByGuid(a.bl_fi_generic_doc_hdr.guid.toString(), this.apiVisa)
          .pipe(
            map((b_inner) => {
              a.bl_fi_generic_doc_hdr.server_doc_1 =
                b_inner.data.bl_fi_generic_doc_hdr.server_doc_1;
              a.bl_fi_generic_doc_hdr.server_doc_2 =
                b_inner.data.bl_fi_generic_doc_hdr.server_doc_2;
              a.bl_fi_generic_doc_hdr.server_doc_3 =
                b_inner.data.bl_fi_generic_doc_hdr.server_doc_3;
              a.bl_fi_generic_doc_hdr.server_doc_4 =
                b_inner.data.bl_fi_generic_doc_hdr.server_doc_4;
              a.bl_fi_generic_doc_hdr.server_doc_5 =
                b_inner.data.bl_fi_generic_doc_hdr.server_doc_5;

              a.bl_fi_generic_doc_hdr.client_doc_1 =
                b_inner.data.bl_fi_generic_doc_hdr.client_doc_1;
              a.bl_fi_generic_doc_hdr.client_doc_2 =
                b_inner.data.bl_fi_generic_doc_hdr.client_doc_2;
              a.bl_fi_generic_doc_hdr.client_doc_3 =
                b_inner.data.bl_fi_generic_doc_hdr.client_doc_3;
              a.bl_fi_generic_doc_hdr.client_doc_4 =
                b_inner.data.bl_fi_generic_doc_hdr.client_doc_4;
              a.bl_fi_generic_doc_hdr.client_doc_5 =
                b_inner.data.bl_fi_generic_doc_hdr.client_doc_5;
              a.bl_fi_generic_doc_hdr.revision =
                b_inner.data.bl_fi_generic_doc_hdr.revision;
              return a;
            })
          )
      ),
      exhaustMap((a) =>
        this.branchService
          .getByGuid(
            a.bl_fi_generic_doc_hdr.guid_branch.toString(),
            this.apiVisa
          )
          .pipe(
            map((b_inner) => {
              a.bl_fi_generic_doc_hdr.guid_comp =
                b_inner.data.bl_fi_mst_branch.comp_guid;
              //  assign code_branch
              a.bl_fi_generic_doc_hdr.code_branch =
                b_inner.data.bl_fi_mst_branch.code;
              a.bl_fi_generic_doc_line.forEach((lineItem) => {
                lineItem.guid_comp = a.bl_fi_generic_doc_hdr.guid_comp;
                lineItem.guid_store = a.bl_fi_generic_doc_hdr.guid_store;
                lineItem.base_doc_line_ccy =
                  a.bl_fi_generic_doc_hdr.base_doc_ccy;
                lineItem.base_doc_line_xrate =
                  a.bl_fi_generic_doc_hdr.base_doc_xrate;
                lineItem.doc_ccy = a.bl_fi_generic_doc_hdr.doc_ccy;
                lineItem.foreign_ccy = a.bl_fi_generic_doc_hdr.foreign_ccy;
              });
              return a;
            })
          )
      ),
      // to get the company code
      exhaustMap((b) =>
        this.compService
          .getByGuid(b.bl_fi_generic_doc_hdr.guid_comp.toString(), this.apiVisa)
          .pipe(
            map((c_inner) => {
              // assign the code_company
              b.bl_fi_generic_doc_hdr.code_company =
                c_inner.data.bl_fi_mst_comp.code;
              return b;
            })
          )
      ),
      // to get the location code
      exhaustMap((c) =>
        this.locService
          .getByGuid(
            c.bl_fi_generic_doc_hdr.guid_store.toString(),
            this.apiVisa
          )
          .pipe(
            map((d_inner) => {
              // assign the code_location
              c.bl_fi_generic_doc_hdr.code_location =
                d_inner.data.bl_inv_mst_location.code;
              return c;
            })
          )
      ),
      exhaustMap((d) =>
        this.piService.put(d, this.apiVisa).pipe(
          map((a: any) => {
            this.viewColFacade.updateInstance(0, {
              deactivateAdd: false,
              deactivateList: false,
            });
            this.viewColFacade.resetIndex(0);
            return PurchaseInvoiceActions.editPurchaseInvoiceFinalSuccess({
              status: { posting_status: "FINAL" },
              doc: a.data,
              hdrGuid: a.data.bl_fi_generic_doc_hdr.guid
            });
          }),
          catchError((err) => {
            let errMsg = err.message;
            if (err.error.data.length) {
              if (
                err.error.data[0].errorCode ===
                "API_TNT_DM_ERP_GEN_DOC_LINE_DRAFT_LOCK_SERIAL_NUMBER_OBJECT_SN_ID_COMP_GUID_INV_ITEM_GUID_SERVER_DOC_TYPE_COMBINATION_ALREADY_EXISTS"
              ) {
                errMsg = "One of the serial numbers is already locked";
              }
            }
            this.toastr.error(errMsg, "Error", {
              tapToDismiss: true,
              progressBar: true,
              timeOut: 0,
              extendedTimeOut: 0,
            });
            return of(
              PurchaseInvoiceActions.editInternalPurchaseInvoiceBeforeContraFailed(
                {
                  error: err.message,
                }
              )
            );
          })
        )
      )
    )
  );

  initUpdateToSelfBilledInvoice$ = createEffect(() =>
    this.actions$.pipe(
      ofType(
        PurchaseInvoiceActions.initUpdateToSelfBilledInvoice
      ),
      map((action) => {
        let dtoObject = {};
        let genDocHrGuids = [];
        if(action.selectedGenDocs.length){
          action.selectedGenDocs.forEach(genDoc => {
            genDocHrGuids.push(genDoc.bl_fi_generic_doc_hdr.guid)
          })
        }
        dtoObject['gendocHdrGuids'] = genDocHrGuids;
        return dtoObject;
      }),
      mergeMap((dtoObject) =>
        this.eInvoiceSelfBilledService
          .postSelfBilled(dtoObject, this.apiVisa)
          .pipe(
            map((selfBilled: any) => {
              this.toastr.success(
                "Update to Self Billed Invoice Success",
                "Success",
                {
                  tapToDismiss: true,
                  progressBar: true,
                  timeOut: 1300,
                }
              );
              return PurchaseInvoiceActions.updateToSelfBilledInvoiceSuccess();
            }),
            catchError((err) => {
              this.toastr.error(err.error.code, "Error", {
                tapToDismiss: true,
                progressBar: true,
                timeOut: 0,
                extendedTimeOut: 0,
              });
              return of(
                PurchaseInvoiceActions.updateToSelfBilledFailed(
                  {
                    error: err.error.code,
                  }
                )
              );
            })
          )
      )
    )
  );

  cloneDocument$ = createEffect(() => this.actions$.pipe(
    ofType(PurchaseInvoiceActions.cloneDocumentInit),
    withLatestFrom(this.store.select(PurchaseInvoiceSelectors.selectInvoice)),
    exhaustMap(([action, doc]) => {
      const guid = doc?.bl_fi_generic_doc_hdr?.guid?.toString();
      const dto: GenericDocumentCloneDTO = {
        sourceGenericDocHdrGuid: guid,
        cloneGenericDocHdrGuid: null, // will be populated by the server in the response
        includeAttachments: action.includeAttachments ?? false
      };
      return this.piService.cloneDocument(dto, this.apiVisa).pipe(
        map(resp => {
          this.viewColFacade.showSuccessToast("Document Clone In-Progress");
          return PurchaseInvoiceActions.cloneDocumentSuccess({ dto: resp.data });
        }),
        catchError(err => {
          this.viewColFacade.showFailedToast("Document Clone Failed");
          return of(PurchaseInvoiceActions.cloneDocumentFail({ error: err }));
        })
      );
    })
  ));

  cloneDocumentFromSearch$ = createEffect(() => this.actions$.pipe(
    ofType(PurchaseInvoiceActions.cloneDocumentFromSearchInit),
    exhaustMap((action) => {
      const dto: GenericDocumentCloneDTO = {
        sourceGenericDocHdrGuid: action.guid,
        cloneGenericDocHdrGuid: null, // will be populated by the server in the response
        includeAttachments: action.includeAttachments ?? false
      };
      return this.piService.cloneDocument(dto, this.apiVisa).pipe(
        map(resp => {
          this.viewColFacade.showSuccessToast("Document Clone In-Progress");
          let genDoc = new GenericDocContainerModel();
          genDoc.bl_fi_generic_doc_hdr.guid = resp.data.cloneGenericDocHdrGuid;
          setTimeout(() => {
            this.updateDraft(genDoc);
          }, 2000);
          return PurchaseInvoiceActions.cloneDocumentFromSearchSuccess({ dto: resp.data });
        }),
        catchError(err => {
          this.viewColFacade.showFailedToast("Document Clone Failed");
          return of(PurchaseInvoiceActions.cloneDocumentFromSearchFail({ error: err }));
        })
      );
    })
  ));

  initPollingAfterClone$ = createEffect(() => this.actions$.pipe(
    ofType(PurchaseInvoiceActions.cloneDocumentSuccess),
    delay(AppletConstants.twoSeconds),
    map(action => PurchaseInvoiceActions.pollClonedDocumentInit({ guid: action.dto.cloneGenericDocHdrGuid?.toString(), currentRequestCount: 1, maxRetries: AppletConstants.pollCloneMaxRetries }))
  ));

  pollClonedDocument$ = createEffect(() => this.actions$.pipe(
    ofType(PurchaseInvoiceActions.pollClonedDocumentInit),
    mergeMap(action => this.piService.getByGuid(action.guid, this.apiVisa).pipe(
      map(resp => {
        this.viewColFacade.showSuccessToast("Cloned Document Ready");
        this.snackBarWithAction(resp.data);
        return PurchaseInvoiceActions.pollClonedDocumentSuccess({ payload: resp.data, currentRequestCount: action.currentRequestCount, maxRetries: action.maxRetries });
      }),
      catchError(err => {
        // if doc is not found (404), the cloning process might not have completed. So continue polling...
        if (err?.status === 404 && action.currentRequestCount <= action.maxRetries) {
          return of(PurchaseInvoiceActions.pollClonedDocumentInit({ guid: action.guid, currentRequestCount: action.currentRequestCount + 1, maxRetries: AppletConstants.pollCloneMaxRetries })).pipe(delay(AppletConstants.twoSeconds));
        }
        this.viewColFacade.showFailedToast("Cloned document not found. Reload listing or Reclone");
        return of(PurchaseInvoiceActions.pollClonedDocumentFail({ guid: action.guid, currentRequestCount: action.currentRequestCount, maxRetries: action.maxRetries, error: err }));
      })
    ))
  ));

  loadCloneSourceGenDoc$ = createEffect(() => this.actions$.pipe(
    ofType(PurchaseInvoiceActions.loadCloneSourceGenDocHdrInit),
    switchMap(action => this.piService.getByGuid(action.guid, this.apiVisa).pipe(
      map(resp => PurchaseInvoiceActions.loadCloneSourceGenDocHdrSuccess({ hdr: resp.data?.bl_fi_generic_doc_hdr })),
      catchError(err => {
        this.viewColFacade.showFailedToast("Source Document could not be found");
        return of(PurchaseInvoiceActions.loadCloneSourceGenDocHdrFail({ error: err }));
      })
    ))
  ))

  getCompanyEinvoiceDetails$ = createEffect(() => this.actions$.pipe(
    ofType(PurchaseInvoiceActions.getCompanyEinvoiceDetails),
    switchMap(action => this.compService.getByGuid(action.compGuid, this.apiVisa).pipe(
      map(resp => {
        console.log("Einvoice enabled",resp.data);
        const eInvoiceEnabled = (resp.data && resp.data.bl_fi_mst_comp.einvoice_status === 'ENABLED') ? true : false;
       //return PurchaseInvoiceActions.selectEInvoiceEnabled({ val: eInvoiceEnabled })
       return PurchaseInvoiceActions.getCompanyEinvoiceDetailsSuccess({ company: resp.data })
      }),
      catchError(err => {
        return of(PurchaseInvoiceActions.getCompanyEinvoiceDetailsFailed({ error: err }));
      })
    ))
  ))

  printEInvoiceJasperPdf$ = createEffect(() =>
  this.actions$.pipe(
    ofType(PurchaseInvoiceActions.printEInvoiceJasperPdfInit),
    withLatestFrom(
      this.store.select(HDRSelectors.selectHdr)
    ),
    exhaustMap(([action, draftHdr]) =>
      this.myEInvoiceToIRBHdrLinesService.getByGuid(action.hdr, this.apiVisa).pipe(
        switchMap(hdr => {
          const irb = hdr.data.bl_fi_my_einvoice_to_irb_hdr;
          const pagination = new Pagination();
          pagination.conditionalCriteria = [
            { columnName: 'server_doc_type', operator: '=', value: "INTERNAL_PURCHASE_INVOICE_NO_STOCK_IN" },
            { columnName: 'company_guids', operator: '=', value: draftHdr.guid_comp?.toString() },
          ];
          pagination.sortCriteria = [
            { columnName: 'orderBy', value: 'priority' },
            { columnName: 'order', value: 'DESC' }
          ];
          return this.companyEInvoicePrintableFormatHdrService.getByCriteria(pagination, this.apiVisa).pipe(
            switchMap(criteriaResult => {
              const printApiCall = criteriaResult.data.length > 0
                ? this.apiService.printEinvoiceFromCompanyPrintable(
                    irb.guid.toString(),criteriaResult.data[0].bl_fi_mst_comp_einvoice_printable_format_hdr.printable_format_hdr_guid?.toString(),
                    this.apiVisa
                  )
                : this.myEInvoiceToIRBHdrLinesService.printJasperPdf(
                    irb.guid.toString(),
                    this.apiVisa
                  );
                  return printApiCall.pipe(
                    map(pdfBlob => {
                      const downloadURL = window.URL.createObjectURL(pdfBlob);
                      const link = document.createElement('a');
                      const formattedDate = moment(irb.einvoice_datetime).format('YYYY-MM-DD');
                      link.href = downloadURL;
                      link.download = `E-invoice-${irb.code_company}-${irb.generic_doc_hdr_server_doc_type}-${formattedDate}-${irb.generic_doc_hdr_server_doc_1}.pdf`;
                      link.click();
                      link.remove();
                      this.viewColFacade.showSuccessToast('E-Invoice Exported Successfully');
                      return PurchaseInvoiceActions.printEInvoiceJasperPdfSuccess();
                    }),
                    catchError(err => {
                      this.viewColFacade.showFailedToast(err);
                      return of(PurchaseInvoiceActions.printEInvoiceJasperPdfFailed());
                    }
                  )
              );
            }),
            catchError(err => {
              this.viewColFacade.showFailedToast(err);
              return of(PurchaseInvoiceActions.printEInvoiceJasperPdfFailed());
            })
          );
        }),
        catchError(err => {
          this.viewColFacade.showFailedToast(err);
          return of(PurchaseInvoiceActions.printEInvoiceJasperPdfFailed());
        })
      )
    )
  )
);

selectSettingItemFilter$ = createEffect(() => this.actions$.pipe(
  ofType(PurchaseInvoiceActions.selectSettingItemFilter),
  mergeMap((action) => {
    const paging = new Pagination();
    paging.conditionalCriteria.push({ columnName: 'applet_guid', operator: '=', value: sessionStorage.getItem('appletGuid') });
    paging.conditionalCriteria.push({ columnName: 'guid_branch', operator: '=', value: action.branch });
    return this.itemFilterSettingService.getByCriteria(paging, this.apiVisa).pipe(
      map(res => {
        return PurchaseInvoiceActions.selectSettingItemFilterSuccess({ setting: res.data });
      }),
      catchError(err => {
        return of(PurchaseInvoiceActions.selectSettingItemFilterFailed({ error: err.message }));
      })
    )
  })
));
saveSettingItemFilter$ = createEffect(() =>
this.actions$.pipe(
  ofType(PurchaseInvoiceActions.saveSettingItemFilter),
  withLatestFrom(this.store.select(PurchaseInvoiceSelectors.selectItemCategoryFilter)),
  mergeMap(([action, settingItem]) => {
    const apiCalls = [];
    const processItemCategory = (lineTable, guids) => {
      const matchingSetting = settingItem.find(s => {
        const settingLevelValue = (s.bl_applet_setting_filter_fi_item_category_link as any)?.level_value;
        const settingGuidBranch = (s.bl_applet_setting_filter_fi_item_category_link as any)?.guid_branch;
        return settingLevelValue == lineTable && settingGuidBranch == action.branch;
      });
      //console.log('matchingSetting:', matchingSetting);
      let itemFilter = new AppletSettingFilterItemCategoryLinkContainerModel();
      (itemFilter as any).bl_applet_setting_filter_fi_item_category_link.category_json = <any>{ guids };
      itemFilter.bl_applet_setting_filter_fi_item_category_link.applet_guid = <any>sessionStorage.getItem('appletGuid');
      (itemFilter as any).bl_applet_setting_filter_fi_item_category_link.level_value = lineTable;
      (itemFilter as any).bl_applet_setting_filter_fi_item_category_link.guid_branch = action.branch;
      itemFilter.bl_applet_setting_filter_fi_item_category_link.line_table = lineTable;
      if (matchingSetting) {
        itemFilter = matchingSetting;
        (itemFilter as any).bl_applet_setting_filter_fi_item_category_link.category_json = <any>{ guids };
        itemFilter.bl_applet_setting_filter_fi_item_category_link.guid = matchingSetting.bl_applet_setting_filter_fi_item_category_link.guid;
        apiCalls.push(this.itemFilterSettingService.put(itemFilter, this.apiVisa));
      } else {
        if (guids) {
          apiCalls.push(this.itemFilterSettingService.postOne(itemFilter, this.apiVisa));
        }
      }
    };
    for (let i = 0; i <= 10; i++) {
      const categoryValue = action.form.value[`ITEM_CATEGORY_${i}`];
      processItemCategory(i.toString(), categoryValue);
    }
    return forkJoin(apiCalls).pipe(
      tap(() => {
        this.toastr.success(
          'Item Category filters has been updated successfully',
          'Success',
          {
            tapToDismiss: true,
            progressBar: true,
            timeOut: 3000
          }
        );
        this.viewColFacade.resetIndex(0);
        this.store.dispatch(PurchaseInvoiceActions.loadSettingItemFilter());
      }),
      map(() => PurchaseInvoiceActions.saveSettingItemFilterSuccess()),
      catchError(err => of(PurchaseInvoiceActions.saveSettingItemFilterFailed({ error: err.message })))
    );
  })
)
);

createPostToProcessInit$ = createEffect(() =>
  this.actions$.pipe(
    ofType(
      PurchaseInvoiceActions.einvoiceNtfQueueProcessInit
    ),
    mergeMap((action) =>
      this.einvoiceNotificationQueueService
        .postMultipleToProcess(action.dto, this.apiVisa)
        .pipe(
          map((stockReplenishmentRun: any) => {
            this.toastr.success(
              "Email Sent",
              "Success",
              {
                tapToDismiss: true,
                progressBar: true,
                timeOut: 1300,
              }
            );
            this.viewColFacade.resetIndex(0);
            return PurchaseInvoiceActions.einvoiceNtfQueueProcessSuccess();
          }),
          catchError((err) => {
            this.toastr.error(err.message, "Error", {
              tapToDismiss: true,
              progressBar: true,
              timeOut: 0,
              extendedTimeOut: 0,
            });
            this.viewColFacade.resetIndex(0);
            return of(
              PurchaseInvoiceActions.einvoiceNtfQueueProcessFailed(
                {
                  error: err.messsage,
                }
              )
            );
          })
        )
    )
  )
);

pushBackToQueues$ = createEffect(() =>
  this.actions$.pipe(
    ofType(
      PurchaseInvoiceActions.createPushBackToQueuesInit
    ),
    mergeMap((action) =>
      this.incomingEInvoiceMatchedHistoryService
        .pushBackToQueues(action.dto, this.apiVisa)
        .pipe(
          map((stockReplenishmentRun: any) => {
            this.toastr.success(
              "Sent To Queue",
              "Success",
              {
                tapToDismiss: true,
                progressBar: true,
                timeOut: 1300,
              }
            );
            this.viewColFacade.resetIndex(0);
            return PurchaseInvoiceActions.createPushBackToQueuesSuccess();
          }),
          catchError((err) => {
            this.toastr.error(err.message, "Error", {
              tapToDismiss: true,
              progressBar: true,
              timeOut: 0,
              extendedTimeOut: 0,
            });
            this.viewColFacade.resetIndex(0);
            return of(
              PurchaseInvoiceActions.createPushBackToQueuesFailed(
                {
                  error: err.messsage,
                }
              )
            );
          })
        )
    )
  )
);

loadBranchCompanyAndGroupDiscountItem$ = createEffect(() =>
  this.actions$.pipe(
    ofType(PurchaseInvoiceActions.loadBranchCompanySuccess),
    mergeMap((action) => {
      const roundingItem = action.branch.bl_fi_mst_branch.rounding_item_guid ?? action.company.bl_fi_mst_comp.rounding_item_guid;
      const groupDiscountItem = action.branch.bl_fi_mst_branch.group_discount_item_guid ?? action.company.bl_fi_mst_comp.group_discount_item_guid;
      
      const roundingItem$ = roundingItem ? this.fiService.getByGuid(roundingItem.toString(), this.apiVisa) : of(null);
      const groupDiscountItem$ = groupDiscountItem ? this.fiService.getByGuid(groupDiscountItem.toString(), this.apiVisa) : of(null);
      
      return forkJoin([roundingItem$, groupDiscountItem$]).pipe(
        mergeMap(([roundingItemRes, groupDiscountItemRes]) => {
          const actions = [];
          if (roundingItemRes) {
            actions.push(PurchaseInvoiceActions.loadRoundingItemSuccess({
              item: roundingItemRes.data,
              isRounding: action.branch.rounding_five_cent
            }));
          }
          
          if (groupDiscountItemRes) {
            actions.push(PurchaseInvoiceActions.loadGroupDiscountItemSuccess({
              item: groupDiscountItemRes.data
            }));
          }

          return from(actions); 
        }),
        catchError((err) => of(
          PurchaseInvoiceActions.loadRoundingItemFailed({
            err: err.message,
          })
        ))
      );
    })
  ));

addGroupDiscount$ = createEffect(() =>
  this.actions$.pipe(
    ofType(PurchaseInvoiceActions.addGroupDiscount),
    withLatestFrom(
      this.store.select(PurchaseInvoiceSelectors.selectGroupDiscountItem),
      this.store.select(PNSSelectors.selectAll),
      this.store.select(PurchaseInvoiceSelectors.selectInvoice)
    ),
    map(([action, groupDiscountItem, pns, selectedInvoice]) => {
      if (groupDiscountItem) {
        const total = pns.length
        ? pns
            .filter(
              (r) =>
                r.item_txn_type !== "DOC_HEADER_ADJUSTMENT" &&
                r.item_txn_type !== "GROUP_DISCOUNT" &&
                r.status === "ACTIVE"
            )
            .map((r) => parseFloat(r.amount_txn?.toString()))
            .reduce((acc, c) => acc + c)
        : 0;

        const lineItem = pns.find(
          (p) =>
            p.item_txn_type === "GROUP_DISCOUNT" &&
            p.status === "ACTIVE"
        );
        let discountAmt = 0;
        if(action.discPercentage){
          discountAmt = Number(((action.discPercentage/100) * Number(total)).toFixed(2));
        }else if(action.discAmount){
          discountAmt = action.discAmount;
        }

        if (total > 0 && discountAmt > 0) {
          if (!lineItem) {
            const discLine = new bl_fi_generic_doc_line_RowClass();
            discLine.guid = UUID.UUID().toLowerCase();
            discLine.item_guid = groupDiscountItem.bl_fi_mst_item_hdr.guid;
            discLine.item_code = groupDiscountItem.bl_fi_mst_item_hdr.code;
            discLine.item_name = groupDiscountItem.bl_fi_mst_item_hdr.name;
            discLine.amount_discount = <any> discountAmt;
            discLine.amount_txn = <any> (discountAmt*(-1));
            discLine.amount_std =  <any> 0;
            discLine.amount_net = discLine.amount_txn;
            discLine.unit_discount = <any> discLine.amount_discount;
            discLine.unit_price_txn = <any>discLine.amount_txn;
            discLine.unit_price_std = <any>discLine.amount_std;
            discLine.unit_price_net = <any>discLine.amount_txn;
            discLine.amount_signum = 1;
            discLine.quantity_base = 1;
            discLine.quantity_signum = 1;
            discLine.txn_type = "PNS";
            discLine.server_doc_type = "INTERNAL_PURCHASE_INVOICE";
            discLine.date_txn = new Date();
            discLine.item_txn_type = groupDiscountItem.bl_fi_mst_item_hdr.txn_type;
            discLine.item_sub_type = groupDiscountItem.bl_fi_mst_item_hdr.sub_item_type;
            discLine.status = "ACTIVE";
            discLine.position_id = "1001";
            this.store.dispatch(PNSActions.addPNS({ pns: discLine }));
          }
          else {
            if (lineItem.amount_txn !== discountAmt) {
              const line = { ...lineItem };
              line.amount_txn =  <any> (discountAmt*(-1));
              line.amount_net = line.amount_txn;
              line.amount_discount = <any> discountAmt;
              line.unit_price_txn = <any>line.amount_txn;
              line.unit_price_net = <any>line.amount_txn;
              line.status  = 'ACTIVE';
              const diffLine = this.calculateDiffLine(line, lineItem);
              this.store.dispatch(PNSActions.editPNS({ pns: line }));
              this.store.dispatch(
                HDRActions.updateBalance({ pns: diffLine })
              );
            }
          }
        } else if (total > 0 && discountAmt === 0) {
          if(lineItem){
            let index;
            if (selectedInvoice) {
              index = selectedInvoice.bl_fi_generic_doc_line.findIndex(
                (x) => x.guid === lineItem.guid
              );
            }
            if (index >= 0) {
              const line = { ...lineItem, status: "DELETED" };
              const diffLine = this.calculateDiffLine(null, line);
              this.store.dispatch(PNSActions.editPNS({ pns: line }));
              this.store.dispatch(HDRActions.updateBalance({ pns: diffLine }));
            } else {
              const diffLine = this.calculateDiffLine(null, lineItem);
              this.store.dispatch(
                PNSActions.deletePNS({ guid: lineItem.guid.toString() })
              );
              this.store.dispatch(HDRActions.updateBalance({ pns: diffLine }));
            }
          }
        }
        this.store.dispatch(PurchaseInvoiceActions.calculateRounding());
        return PurchaseInvoiceActions.addGroupDiscountSuccess({discPercentage:action.discPercentage});
      } else {
        this.toastr.error("Please configure the default group discount item code", "Error", {
          tapToDismiss: true,
          progressBar: true,
          timeOut: 0,
          extendedTimeOut: 0,
        });
        return PurchaseInvoiceActions.addGroupDiscountFailed();
      }
    })
  ));

  recalculateGroupDiscount$ = createEffect(() =>
    this.actions$.pipe(
      ofType(PurchaseInvoiceActions.recalculateGroupDiscount),
      withLatestFrom(
        this.store.select(PurchaseInvoiceSelectors.selectGroupDiscountItem),
        this.store.select(PNSSelectors.selectAll),
        this.store.select(PurchaseInvoiceSelectors.selectGroupDiscountPercentage),
      ),
      map(([action, groupDiscountItem,  pns, groupDiscountPercentage]) => {
        if (groupDiscountItem) {
          const total = pns.length
          ? pns
              .filter(
                (r) =>
                  r.item_txn_type !== "GROUP_DISCOUNT" &&
                  r.status === "ACTIVE"
              )
              .map((r) => parseFloat(r.amount_txn?.toString()))
              .reduce((acc, c) => acc + c)
          : 0;

          const lineItem = pns.find(
            (p) =>
              p.item_txn_type === "GROUP_DISCOUNT" &&
              p.status === "ACTIVE"
          );
          let discountAmt = 0;
          if(groupDiscountPercentage){
            discountAmt = Number(((groupDiscountPercentage/100) * Number(total)).toFixed(2));
          }
         
          if (total > 0 && discountAmt > 0) { 
            if (lineItem && (lineItem.amount_txn !== discountAmt)) {
                const line = { ...lineItem };
                line.amount_txn =  <any> (discountAmt*(-1));
                line.amount_std = line.amount_txn;
                line.amount_net = line.amount_txn;
                line.amount_discount = <any> discountAmt; 
                line.unit_price_std = <any>line.amount_txn;
                line.unit_price_txn = <any>line.amount_txn;
                line.unit_price_net = <any>line.amount_txn;
                const diffLine = this.calculateDiffLine(line, lineItem);
                this.store.dispatch(PNSActions.editPNS({ pns: line }));
                this.store.dispatch(
                  HDRActions.updateBalance({ pns: diffLine })
                );
            }
          } 
          return PurchaseInvoiceActions.recalculateGroupDiscountSuccess();
        } else {
         
          return PurchaseInvoiceActions.recalculateGroupDiscountFailed();
        }
      }
    )
  ));

  calculateDiffLine(
    line: bl_fi_generic_doc_line_RowClass,
    lineItem: bl_fi_generic_doc_line_RowClass
  ) {
    const amount_discount = line ? line.amount_discount : 0;
    const amount_net = line ? line.amount_net : 0;
    const amount_std = line ? line.amount_std : 0;
    const amount_tax_gst = line ? line.amount_tax_gst : 0;
    const amount_tax_wht = line ? line.amount_tax_wht : 0;
    const amount_txn = line ? line.amount_txn : 0;
    const diffLine = new bl_fi_generic_doc_line_RowClass();
    diffLine.amount_discount = <any>(
      (parseFloat(<any>amount_discount) -
        parseFloat(<any>lineItem.amount_discount))
    );
    diffLine.amount_net = <any>(
      (parseFloat(<any>amount_net) - parseFloat(<any>lineItem.amount_net))
    );
    diffLine.amount_std = <any>(
      (parseFloat(<any>amount_std) - parseFloat(<any>lineItem.amount_std))
    );
    diffLine.amount_tax_gst = <any>(
      (parseFloat(<any>amount_tax_gst) -
        parseFloat(<any>lineItem.amount_tax_gst))
    );
    diffLine.amount_tax_wht = <any>(
      (parseFloat(<any>amount_tax_wht) -
        parseFloat(<any>lineItem.amount_tax_wht))
    );
    diffLine.amount_txn = <any>(
      (parseFloat(<any>amount_txn) - parseFloat(<any>lineItem.amount_txn))
    );
    return diffLine;
  }

  calculateRounding$ = createEffect(() =>
    this.actions$.pipe(
      ofType(PurchaseInvoiceActions.calculateRounding),
      withLatestFrom(
        this.store.select(PurchaseInvoiceSelectors.selectRoundingItem),
        this.store.select(PurchaseInvoiceSelectors.selectRoundingFiveCent),
        this.store.select(PurchaseInvoiceSelectors.selectInvoice),
        this.store.select(PNSSelectors.selectAll)
      ),
      map(([action, roundingItem,roundingFiveCent, selectedInvoice, pns]) => {
        if (roundingItem && roundingFiveCent) {
          const total = pns.length
            ? pns
                .filter(
                  (r) =>
                    r.item_txn_type !== "DOC_HEADER_ADJUSTMENT" &&
                    r.status === "ACTIVE"
                )
                .map((r) => parseFloat(r.amount_txn?.toString()))
                .reduce((acc, c) => acc + c)
            : 0;
          //console.log('total',total)
          let roundingValue = 0;
          let roundedAmt = parseFloat(
            (Math.round(total / 0.05) * 0.05).toFixed(2)
          );

          if (!isNaN(roundedAmt)) {
            roundingValue = parseFloat((roundedAmt - total).toFixed(2));
          }

          const lineItem = pns.find(
            (p) =>
              p.item_txn_type === "DOC_HEADER_ADJUSTMENT"
          );
          //console.log('roundingValue',roundingValue)
          if (roundingValue != 0) {
            if (!lineItem) {
              const roundingLine = new bl_fi_generic_doc_line_RowClass();
              roundingLine.guid = UUID.UUID().toLowerCase();
              roundingLine.item_guid = roundingItem.bl_fi_mst_item_hdr.guid;
              roundingLine.item_code = roundingItem.bl_fi_mst_item_hdr.code;
              roundingLine.item_name = roundingItem.bl_fi_mst_item_hdr.name;
              roundingLine.amount_txn = <any>roundingValue;
              roundingLine.amount_std = roundingLine.amount_txn;
              roundingLine.amount_net = roundingLine.amount_txn;
              roundingLine.amount_open_balance = roundingLine.amount_txn;
              roundingLine.unit_price_std = <any>roundingLine.amount_txn;
              roundingLine.unit_price_txn = <any>roundingLine.amount_txn;
              roundingLine.unit_price_net = <any>roundingLine.amount_txn;
              roundingLine.amount_discount = <any>0;
              roundingLine.amount_tax_wht = <any>0;
              roundingLine.amount_signum = 1;
              roundingLine.quantity_base = 1;
              roundingLine.quantity_signum = 1;
              roundingLine.txn_type = "PNS";
              roundingLine.server_doc_type = "INTERNAL_PURCHASE_INVOICE";
              roundingLine.date_txn = new Date();
              roundingLine.item_txn_type =
                roundingItem.bl_fi_mst_item_hdr.txn_type;
              roundingLine.item_sub_type =
                roundingItem.bl_fi_mst_item_hdr.sub_item_type;
              roundingLine.status = "ACTIVE";
              roundingLine.position_id = "1002";
              this.store.dispatch(PNSActions.addPNS({ pns: roundingLine }));
            } else {
                const line = { ...lineItem };
                line.amount_txn = <any>roundingValue;
                line.amount_std = line.amount_txn;
                line.amount_std = line.amount_txn;
                line.amount_net = line.amount_txn;
                line.amount_open_balance = line.amount_txn;
                line.unit_price_std = <any>line.amount_txn;
                line.unit_price_txn = <any>line.amount_txn;
                line.unit_price_net = <any>line.amount_txn;
                line.status = 'ACTIVE';
                const diffLine = this.calculateDiffLine(line, lineItem);
                this.store.dispatch(PNSActions.editPNS({ pns: line }));
                this.store.dispatch(
                  HDRActions.updateBalance({ pns: diffLine })
                );
            }
          } else {
            //console.log('lineItem',lineItem)
            if(lineItem){
              let index;
              if (selectedInvoice) {
                index = selectedInvoice.bl_fi_generic_doc_line.findIndex(
                  (x) => x.guid === lineItem.guid
                );
              }
              if (index >= 0) {
                const line = { ...lineItem, status: "DELETED" };
                const diffLine = this.calculateDiffLine(null, line);
                this.store.dispatch(PNSActions.editPNS({ pns: line }));
                this.store.dispatch(HDRActions.updateBalance({ pns: diffLine }));
              } else {
                const diffLine = this.calculateDiffLine(null, lineItem);
                this.store.dispatch(
                  PNSActions.deletePNS({ guid: lineItem.guid.toString() })
                );
                this.store.dispatch(HDRActions.updateBalance({ pns: diffLine }));
              }
            }

          }
          return PurchaseInvoiceActions.calculateRoundingSuccess();
        } else {
          return PurchaseInvoiceActions.calculateRoundingFailed();
        }
      })
    )
  );

  constructor(
    private actions$: Actions,
    private viewColFacade: ViewColumnFacade,
    private apiService: ApiService,
    private piService: InternalPurchaseInvoiceNoStockInService,
    private toastr: ToastrService,
    private entityService: EntityService,
    private branchService: BranchService,
    private locService: LocationService,
    private compService: CompanyService,
    private arapService: ARAPService,
    private pricingService: PricingSchemeService,
    private pslService: PricingSchemeLinkService,
    private genDocLinkService: GenericDocLinkBackofficeEPService,
    private draftStore: Store<DraftStates>,
    private supplierService: SupplierService,
    private empService: EmployeeService,
    private genericDocLineService: GenericDocLineService,
    private profileService: TenantUserProfileService,
    private siService: SalesInvoiceService,
    private customerService: CustomerService,
    private intercompanyService: IntercompanyService,
    private store: Store<PurchaseInvoiceStates>,
    private subQueryService: SubQueryService,
    private genDocAttachmentService: GenericDocAttachmentService,
    private glDimensionService: DimensionService,
    private segmentCoaService: SegmentCoaService,
    private profitCenterService: ProfitCenterService,
    private projectCoaService: ProjectCoaService,
    private eInvoiceSelfBilledService : EInvoiceSelfBilledService,
    private snackBar: MatSnackBar,
    private myEInvoiceToIRBHdrLinesService: MyEInvoiceToIRBHdrLinesService,
    protected listingService: ListingService,
    private itemFilterSettingService: AppletSettingFilterItemCategoryLinkService,
    private sessionStore: Store<SessionStates>,
    private companyService: CompanyService,
    private genericDocLockService: GenericDocLockService,
    private einvoiceNotificationQueueService: EinvoiceNotificationQueueService,
    private incomingEInvoiceMatchedHistoryService: IncomingEInvoiceMatchedHistoryService,
    private companyEInvoicePrintableFormatHdrService : CompanyEInvoicePrintableFormatHdrService,
    private fiService: FinancialItemService,
  ) {
    this.viewColFacade.prevIndex$.subscribe(
      (resolve) => (this.prevIndex = resolve)
    );
    this.viewColFacade
      .prevLocalState$()
      .subscribe((resolve) => (this.prevLocalState = resolve));
  }

  onReturn() {
    this.viewColFacade.updateInstance(this.prevIndex, {
      ...this.prevLocalState,
      deactivateAdd: false,
      deactivateList: false,
      deactivateReturn: false,
    });
    this.viewColFacade.onPrev(this.prevIndex);
  }

  /** Displays the snackbar with an option to view the cloned gen doc. When clicked, the new cloned gen doc is loaded to draft
   * No need to dispatch actions to validate the Serial number because serial_no, bin_no and batch_no are not copied when cloned
  */
  snackBarWithAction(container: GenericDocContainerModel) {
    const docNo = container?.bl_fi_generic_doc_hdr?.server_doc_1?.toString();
    const snackbarRef = this.snackBar.open(`Cloned Document #${docNo} Ready`, "View", { duration: AppletConstants.tenSeconds });
    snackbarRef.onAction().pipe(take(1)).subscribe(() => {
      // reset ngrx
      this.store.dispatch(PurchaseInvoiceActions.resetDraft());
      this.store.dispatch(PurchaseInvoiceActions.resetContra());
      this.store.dispatch(PurchaseInvoiceActions.resetconvertActionDispatchedState());

      // load cloned gen doc
      this.store.dispatch(PurchaseInvoiceActions.editMode({ editMode: true }));
      this.store.dispatch(PurchaseInvoiceActions.selectGUID({ guid: container.bl_fi_generic_doc_hdr.guid.toString()}));
      this.store.dispatch(PurchaseInvoiceActions.selectPurchaseInvoice({ genDoc: container }));
      this.store.dispatch(PurchaseInvoiceActions.refreshArapListing({ refreshArapListing: true }));
      this.store.dispatch(PurchaseInvoiceActions.selectCompanyGuid({ compGuid: container.bl_fi_generic_doc_hdr.guid_comp.toString()}));
      this.viewColFacade.updateInstance(this.prevIndex, {
        ...this.prevLocalState,
        selectedRow: container.bl_fi_generic_doc_hdr?.guid
      });
    });
  }
}
