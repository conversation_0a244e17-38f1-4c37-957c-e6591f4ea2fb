import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  Component,
  <PERSON><PERSON><PERSON><PERSON>,
  On<PERSON><PERSON>roy,
  Input,
  Output,
  EventEmitter,
} from "@angular/core";
import { Store } from "@ngrx/store";
import { Observable, combineLatest } from "rxjs";
import { SubSink } from "subsink2";
import {
  bl_fi_generic_doc_hdr_RowClass,
  bl_fi_generic_doc_line_RowClass,
  bl_fi_generic_doc_link_RowClass,
  FinancialItemService,
  LandedCostsDtoModel,
} from "blg-akaun-ts-lib";
import { ClientSidePermissionsSelectors } from "projects/shared-utilities/modules/permission/client-side-permissions-controller/selectors";
import { ClientSideViewModel } from "projects/shared-utilities/modules/permission/client-side-permissions-controller/states/client-side-permission.states";
import { PermissionStates } from "projects/shared-utilities/modules/permission/permission-controller";
import { SessionSelectors } from "projects/shared-utilities/modules/session/session-controller/selectors";
import { SessionStates } from "projects/shared-utilities/modules/session/session-controller/states";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>er<PERSON><PERSON>hecker } from "projects/shared-utilities/utilities/client-side-permission-checker";
import { formatMoneyInList, formatMoneyToPrecision } from "projects/shared-utilities/format.utils";
import { PurchaseInvoiceSearchModel } from "../../../../models/advanced-search-models/purchase-invoice.model";
import { SlideRendererComponent } from "../../../utilities/slide-renderer/slide-renderer.component";
import { DraftStates } from "../../../../state-controllers/draft-controller/store/states";
import { LinkSelectors, PNSSelectors } from "../../../../state-controllers/draft-controller/store/selectors";
import { PurchaseInvoiceStates } from "../../../../state-controllers/purchase-invoice-controller/store/states";
import { PurchaseInvoiceSelectors } from "../../../../state-controllers/purchase-invoice-controller/store/selectors";
import { PurchaseInvoiceActions } from "../../../../state-controllers/purchase-invoice-controller/store/actions";
import { FormControl, FormGroup } from "@angular/forms";
import { ColumnViewModelStates } from "../../../../state-controllers/generic-doc-view-model-controller/states";
import { Column4ViewSelectors } from "../../../../state-controllers/generic-doc-view-model-controller/selectors";
import { distinctUntilChanged, debounceTime, map } from "rxjs/operators";
import { HDRActions } from "../../../../state-controllers/draft-controller/store/actions";
import { UtilitiesModule } from "projects/shared-utilities/utilities/utilities.module";
import { Column4ViewModelActions } from "../../../../state-controllers/generic-doc-view-model-controller/actions";
import { ButtonDeleteRendererComponent } from "../../../button-del-renderer/button-del-renderer.component";
import { ViewColumnFacade } from "../../../../facades/view-column.facade";
import { AppConfig } from "projects/shared-utilities/visa";
import { AppletSettings, DEFAULTS } from "../../../../models/applet-settings.model";
import { PNSActions } from '../../../../state-controllers/draft-controller/store/actions'

@Component({
  selector: "app-purchase-invoice-line-items-listing",
  templateUrl: "./line-items-listing.component.html",
  styleUrls: ["./line-items-listing.component.css"],
})
export class PurchaseInvoiceLineItemsListingComponent
  implements OnInit, OnDestroy
{
  @Input() localState: any;
  @Input() draft$: Observable<bl_fi_generic_doc_hdr_RowClass>;

  @Output() lineItemCreate = new EventEmitter();
  @Output() lineItemEdit = new EventEmitter();

  private subs = new SubSink();
  public form: FormGroup;

  pns$ = this.draftStore.select(PNSSelectors.selectAll);
  groupDiscount$ = this.store.select(PurchaseInvoiceSelectors.selectGroupDiscountItem);
  readonly invoice$ = this.store.select(PurchaseInvoiceSelectors.selectInvoice);
  readonly clientSidePermissions$ = this.permissionStore.select(
    ClientSidePermissionsSelectors.selectAll
  );
  readonly userRank$ = this.sessionStore.select(
    SessionSelectors.selectUserRank
  );
  readonly editMode$ = this.store.select(
    PurchaseInvoiceSelectors.selectEditMode
  );
  genDocLock$ = this.store.select(PurchaseInvoiceSelectors.selectGenDocLock);

  searchModel = PurchaseInvoiceSearchModel;
  gridApi;
  gridOptions = {
    getRowStyle :  this.getRowStylePNS
  };
  rowData: bl_fi_generic_doc_line_RowClass[];
  total = "0.00";
  tax = "0.00";
  discount = '0.00';
  hidePriceFlag: boolean;
  postingStatus;
  status;
  totalExpense;
  amountSignum= -1;
  hdr: any;
  genDocLock:boolean;
  HIDE_LANDED_COST: boolean;
  appletSettings: AppletSettings;
  defaultColDef = {
    filter: "agTextColumnFilter",
    floatingFilter: true,
    floatingFilterComponentParams: { suppressFilterButton: true },
    minWidth: 200,
    flex: 2,
    sortable: true,
    resizable: true,
    suppressCsvExport: true,
    onCellClicked: (params) => this.onRowClicked(params.data),
  };

  // columnsDefs = [
  //   { headerName: 'Item Code', field: 'item_code', cellStyle: () => ({ 'text-align': 'left' }) },
  //   { headerName: 'Item Name', field: 'item_name', cellStyle: () => ({ 'text-align': 'left' }) },
  //   { headerName: 'UOM', field: 'item_property_json.uom', cellStyle: () => ({ 'text-align': 'left '}) },
  //   // { headerName: 'Delivery', field: 'delivery', cellRenderer: 'slideCellRenderer',
  //   //   onCellClicked: (params) => null},
  //   { headerName: 'Qty', field: 'quantity_base', type: 'numericColumn' },
  //   { headerName: 'Unit Price', field: 'item_property_json.unitPrice', type: 'numericColumn' },
  //   { headerName: 'SST/VAT/GST', field: 'amount_tax_gst', type: 'numericColumn',
  //     valueFormatter: (params) => parseFloat(params.value).toFixed(2) },
  //   { headerName: 'Txn Amount', field: 'amount_txn', type: 'numericColumn',
  //     valueFormatter: (params) => parseFloat(params.value).toFixed(2) },
  // ];

  frameworkComponents = {
    slideCellRenderer: SlideRendererComponent,
    buttonRenderer: ButtonDeleteRendererComponent
  };
  prevIndex: number;
  hideSerialFlag = false;
  showGroupDiscount = false;
  detailCellRendererParams;
  DECIMAL_PRECISION: number;
  isRowMaster = dataItem => {
    let arr = this.getDoclines(dataItem.item_child_json,dataItem)
    return Array.isArray(arr) && arr.length > 0;
  }
  constructor(
    protected readonly draftStore: Store<DraftStates>,
    protected readonly store: Store<PurchaseInvoiceStates>,
    private readonly permissionStore: Store<PermissionStates>,
    private readonly sessionStore: Store<SessionStates>,
    private readonly viewModelStore: Store<ColumnViewModelStates>,
   private fiService: FinancialItemService,
    private readonly viewColFacade: ViewColumnFacade,
  ) {
    this.detailCellRendererParams = {
      detailGridOptions: {
        columnDefs: [
          { headerName: "Item Code", field: "item_code" , cellStyle: () => ({ 'text-align': 'left' }) },
          { headerName: "Item Name", field: "item_name" , cellStyle: () => ({ 'text-align': 'left' })},
          { headerName: "Qty", field: "quantity_base"},
          {
            headerName: "Amt Std",
            field: "amount_std",
            cellStyle: { 'text-align': "right" },
            type: 'numericColumn',
            valueFormatter: params => {
              const value = params.value;
              if (typeof value === 'number') {
                return value.toFixed(2);
              } else if (value == null) {
                return "0.00";
              } else {
                return value;
              }
            }
          },
          {
            headerName: "Amt Discount",
            field: "amount_discount",
            cellStyle: { 'text-align': "right" },
            type: 'numericColumn',
            valueFormatter: params => {
              const value = params.value;
              if (typeof value === 'number') {
                return value.toFixed(2);
              } else if (value == null) {
                return "0.00";
              } else {
                return value;
              }
            }
          },
          {
            headerName: "Amt Txn",
            field: "amount_txn",
            cellStyle: { 'text-align': "right" },
            type: 'numericColumn',
            valueFormatter: params => {
              const value = params.value;
              if (typeof value === 'number') {
                return value.toFixed(2);
              } else if (value == null) {
                return "0.00";
              } else {
                return value;
              }
            }
          }
        ],
        defaultColDef: { flex: 1, resizable: true },
      },
      getDetailRowData: (params) => {
        setTimeout(() => {
          let arr = this.getDoclines(params.data?.item_child_json,params.data)
          params.successCallback(arr);
        }, 100);
      }
    };
  }

  masterSettings$ = this.sessionStore.select(SessionSelectors.selectMasterSettings);

  ngOnInit() {
    this.subs.sink = this.genDocLock$.subscribe(lock=>{
      this.genDocLock = lock;
    })
    this.subs.sink = this.viewColFacade.prevIndex$.subscribe(resolve => this.prevIndex = resolve);
    this.subs.sink = this.pns$.subscribe({
      next: (resolve) => {
        this.rowData = resolve.filter(
          (x) => x.status === "ACTIVE" || x.status === "DRAFT"
        );
      },
    });

    this.form = new FormGroup({
      landedCost: new FormControl(""),
      aportionType: new FormControl(""),
      discPercentage: new FormControl(),
      discAmt: new FormControl(),
    });

    let userPermissions: ClientSideViewModel[];
    this.subs.sink = this.clientSidePermissions$.subscribe(
      (permissions) => (userPermissions = permissions)
    );

    let userRank: string;
    //this.subs.sink = this.appletLoginSubjectLink$.subscribe(appletLoginSubjectLink => userRank = appletLoginSubjectLink?.rank?.toString());
    this.subs.sink = this.userRank$.subscribe(
      (rank) =>{
        console.log(rank);
        (userRank = rank)
      }
    );

    const permissionChecker = new ClientSidePermissionChecker(
      userPermissions,
      userRank
    );

    this.hidePriceFlag = !permissionChecker.checkPermission(
      "INTERNAL_PURCHASE_INVOICE_NO_STOCK_IN_DISPLAY_PRICING"
    );
    this.subs.sink = this.draft$.subscribe(
      (resolve) => {
        this.hdr = resolve;
        this.postingStatus = resolve.posting_status;
        this.status = resolve.status;
        this.form.patchValue({
          landedCost: resolve.landed_cost_amount,
          aportionType: resolve.apportion_type
        })

        this.total = this.rowData.length ? this.rowData.map(r =>
          parseFloat(r.amount_txn?.toString())).reduce((acc, c) =>
            (acc + c)).toFixed(2) : '0.00';

          this.totalExpense = Number(this.total) * this.amountSignum;
          this.store.dispatch(PurchaseInvoiceActions.selectTotalExpense({totalExpense : this.totalExpense}))

        if (this.total === 'NaN') {
          this.total = '0.00';
          this.totalExpense = '0.00';
          this.store.dispatch(PurchaseInvoiceActions.selectTotalExpense({totalExpense : this.totalExpense}))
        }
        this.tax = this.rowData.length ? this.rowData.map(r =>
          parseFloat(r.amount_tax_gst?.toString())).reduce((acc, c) =>
            (acc + c)).toFixed(2) : '0.00';
        if (this.tax === 'NaN') {
          this.tax = '0.00';
        }
        this.discount = this.rowData.length ? this.rowData.map(r =>
          parseFloat(r.amount_discount?.toString())).reduce((acc, c) =>
            (acc + c)).toFixed(2) : '0.00';
        if (this.tax === 'NaN') {
          this.tax = '0.00';
        }
      }
    );
    console.log("this.hidePriceFlag", this.hidePriceFlag);

    this.checkGroupDiscount();

    this.subs.sink = this.form.valueChanges
    .pipe(debounceTime(100), distinctUntilChanged())
    .subscribe({
      next: (form) => {
        console.log(form);
        this.draftStore.dispatch(HDRActions.updateLandedCostAmountAndApportionType({form: this.form}));
        // this.updateMainDetails.emit(form);
      },
    });

    this.subs.sink = this.masterSettings$.subscribe({
      next: (resolve: AppletSettings) => {
        this.appletSettings = resolve;
        this.DECIMAL_PRECISION = resolve.DEFAULT_DECIMAL_PRECISION || DEFAULTS.DECIMAL_PRECISION;
        this.HIDE_LANDED_COST = this.appletSettings?.HIDE_LANDED_COST ? true : this.appletSettings.HIDE_LANDED_COST;
      },
    });
  }

  posting() {
    if(this.genDocLock){
      return false;
    }

    if ((this.postingStatus === "FINAL")|| (this.postingStatus === "VOID") || (this.postingStatus === "DISCARDED") || (this.status !== "ACTIVE" && this.status !== "TEMP" && this.status !== null)) {
      return false;
    } else {
      return true;
    }
  }

  checkGroupDiscount() {
    console.log('checkGroupDiscount')
    this.subs.sink = combineLatest([this.groupDiscount$, this.draft$])
      .pipe(
        map(([groupDiscountValue, draftValue]) => {
          this.showGroupDiscount = (groupDiscountValue?true:false) && (!draftValue.posting_status  || draftValue.posting_status === "DRAFT");
        })
      )
      .subscribe();
  }

  onGridReady(params) {
    this.gridApi = params.api;
    this.gridApi.closeToolPanel();
    this.gridApi.forEachNode((a) => {
      if (a.data.guid === this.localState.selectedLine) {
        a.setSelected(true);
      }
    });
    this.gridApi.setColumnDefs([
      {
        headerName: "Item Code",
        field: "item_code",
        cellStyle: () => ({ "text-align": "left" }),
        cellRenderer: "agGroupCellRenderer"
      },
      {
        headerName: "Item Name",
        field: "item_name",
        cellStyle: () => ({ "text-align": "left" }),
        cellRenderer: function(param){
          let itemName = param.data.item_name;
          let hasInvalidSerial = false;
          if(param.data.item_sub_type==='SERIAL_NUMBER'
          ){
            hasInvalidSerial = UtilitiesModule.checkSerialValid(<any> param.data.serial_no);
          }
          if(hasInvalidSerial){
          itemName += "<br>Serial numbers are empty or invalid";
          }
          return itemName;
        }
      },
      {
        headerName: "UOM",
        field: "uom",
        cellStyle: () => ({ "text-align": "left " }),
      },
      {
        headerName: "Remarks",
        field: "item_remarks",
        cellStyle: () => ({ "text-align": "left " }),
      },
      {
        headerName: "Qty",
        field: "quantity_base",
        type: "numericColumn",
        floatingFilter: true,
      },
      { headerName: 'Serial #', field: 'serial_no', cellStyle: () => ({ 'text-align': 'left' }), hide: this.hideSerialFlag,suppressColumnsToolPanel: this.hideSerialFlag, suppressFiltersToolPanel: this.hideSerialFlag,
        cellRenderer: function(param){
          //console.log("param.data?.serial_no",param.data?.serial_no);
          const serialArr = Array.isArray(param.data?.serial_no) // Check if it's an array
          ? param.data.serial_no.map(sn => sn['sn_id'])
          : [];
          const commaSeparatedString = serialArr.join(", ");
          return commaSeparatedString;
        }
      },
      {
        headerName: "Unit Price (Inclusive of Tax)",
        field: "unit_price_txn",
        type: "numericColumn",
        valueFormatter: (params) => params.value ? formatMoneyToPrecision(params.value , this.DECIMAL_PRECISION) : null,
        hide: this.hidePriceFlag,
        suppressColumnsToolPanel: this.hidePriceFlag,
        suppressFiltersToolPanel: this.hidePriceFlag,
      },
      {
        headerName: "SST/VAT/GST",
        field: "amount_tax_gst",
        type: "numericColumn",
        valueFormatter: (params) =>
          params.value ? formatMoneyInList(params.value) : null,
        hide: this.hidePriceFlag,
        suppressColumnsToolPanel: this.hidePriceFlag,
        suppressFiltersToolPanel: this.hidePriceFlag,
      },
      {
        headerName: "Txn Amount",
        field: "amount_txn",
        type: "numericColumn",
        valueFormatter: (params) =>
          params.value ? formatMoneyInList(params.value) : null,
        hide: this.hidePriceFlag,
        suppressColumnsToolPanel: this.hidePriceFlag,
        suppressFiltersToolPanel: this.hidePriceFlag,
      },
      {
        headerName: "Landed Costs",
        field: "landed_txn_amount",
        type: "numericColumn",
        valueFormatter: (params) =>
          params.value ? formatMoneyInList(params.value) : null,
        hide: this.hidePriceFlag,
        suppressColumnsToolPanel: this.hidePriceFlag,
        suppressFiltersToolPanel: this.hidePriceFlag,
      },
      {
        headerName: "Segment Code",
        field: "segment_code",
        cellStyle: () => ({ "text-align": "left" }),
      },
      {
        headerName: "Project Code",
        field: "project_code",
        cellStyle: () => ({ "text-align": "left" }),
      },
      {
        headerName: "Profit Center Code",
        field: "profit_center_code",
        cellStyle: () => ({ "text-align": "left" }),
      },
      {
        headerName: "Dimension Code",
        field: "gl_dimension_code",
        cellStyle: () => ({ "text-align": "left" }),
      },
      {
        headerName: 'Action', field: 'action', minWidth: 180,
        valueGetter: params => params.data.guid,
        hide: !this.deleteCondition(),
        cellRenderer: !this.deleteCondition() ? null : 'buttonRenderer',
        cellRendererParams: { onClick: this.onButtonClick.bind(this) }
      }
    ]);
  }

  deleteCondition() {
    if ((this.postingStatus === "FINAL") || (this.status !== "ACTIVE" && this.status !== null && this.status !== undefined) || this.genDocLock) {
      return false;
    }
    else {
      return true;
    }
  }

  onButtonClick(e) {
   this.deleteLine(e.rowData);
  }
  deleteLine(lineItem) {
    let index;
    this.subs.sink = this.invoice$.subscribe({
      next: resolve => {
        if (resolve)
          index = resolve.bl_fi_generic_doc_line.findIndex(x => x.guid === lineItem.guid)
      }
    });
    if (this.deleteCondition()) {
      if (index >= 0) {
        // Change status of existing line and link to DELETED
        const line = { ...lineItem, status: 'DELETED' };
        const diffLine = new bl_fi_generic_doc_line_RowClass();
        diffLine.amount_discount = <any>(0 - parseFloat(<any>line.amount_discount));
        diffLine.amount_net = <any>(0 - parseFloat(<any>line.amount_net));
        diffLine.amount_std = <any>(0 - parseFloat(<any>line.amount_std));
        diffLine.amount_tax_gst = <any>(0 - parseFloat(<any>line.amount_tax_gst));
        diffLine.amount_tax_wht = <any>(0 - parseFloat(<any>line.amount_tax_wht));
        diffLine.amount_txn = <any>(0 - parseFloat(<any>line.amount_txn));
        console.log('line', line);

        const link = this.getLink(line.guid.toString());
        if (link) {
          link.status = 'DELETED'
          this.viewColFacade.editLink(link);
        }

        this.viewColFacade.deleteExistingLine(line, diffLine);
      } else {
        // Remove the line and link entirely from the draft
        const diffLine = new bl_fi_generic_doc_line_RowClass();
        diffLine.amount_discount = <any>(0 - parseFloat(<any>lineItem.amount_discount));
        diffLine.amount_net = <any>(0 - parseFloat(<any>lineItem.amount_net));
        diffLine.amount_std = <any>(0 - parseFloat(<any>lineItem.amount_std));
        diffLine.amount_tax_gst = <any>(0 - parseFloat(<any>lineItem.amount_tax_gst));
        diffLine.amount_tax_wht = <any>(0 - parseFloat(<any>lineItem.amount_tax_wht));
        diffLine.amount_txn = <any>(0 - parseFloat(<any>lineItem.amount_txn));

        const link = this.getLink(lineItem.guid.toString());
        if (link) {
          this.viewColFacade.deleteLink(link.guid.toString());
        }

        this.viewColFacade.deleteLine(lineItem.guid.toString(), diffLine, this.prevIndex);
      }
    }

  }

  getLink(lineGuid: string): bl_fi_generic_doc_link_RowClass {
    let link;
    this.subs.sink = this.draftStore.select(LinkSelectors.selectAll).subscribe(resolved => {
      link = resolved.find(x => x.guid_doc_2_line === lineGuid);
    })
    return link;
  }

  onAdd() {
    this.lineItemCreate.emit();
  }

  onAllocate() {
    let lineItems: bl_fi_generic_doc_line_RowClass[] = this.getAllRows();

    let aportionType = this.form.value.aportionType;
    let landedCostInput = this.form.value.landedCost;
    let sum;

    switch (aportionType) {
      case "Qty": {
        console.log("cortion By Qty");
        sum = lineItems.reduce(
          (sum, current) => sum + +current.quantity_base,
          0
        );
        lineItems.forEach((lineItem) => {
          lineItem.landed_txn_amount =
            landedCostInput * (+lineItem.quantity_base / sum);
        });
        break;
      }
      case "Txn_Amt": {
        sum = lineItems.reduce((sum, current) => sum + +current.amount_txn, 0);
        lineItems.forEach((lineItem) => {
          lineItem.landed_txn_amount =
            landedCostInput * (+lineItem.amount_txn / sum);
        });
        break;
      }
      //   case "Weight": {
      //     sum = lineItems.reduce((sum, current) => sum + +current., 0);
      //     break;
      //  }
      //  case "Volumetric_Weight": {
      //   //statements;
      //   break;
      // }
      case "Equal": {
        sum = lineItems.length;
        lineItems.forEach((lineItem) => {
          lineItem.landed_txn_amount = landedCostInput / sum;
        });
        break;
      }
      default: {
        //statements;
        break;
      }
    }
    this.rowData = lineItems;
    console.log(this.rowData);
    this.gridApi.setRowData(this.rowData);
  }

  onConfirm() {
    let landedCostsDto: LandedCostsDtoModel[] = [];

    this.rowData.forEach((record) => {
      landedCostsDto.push({
        generic_doc_line_guid: record.guid.toString(),
        landed_cost_amount: +record.landed_txn_amount,
      });
    });

    this.store.dispatch(PurchaseInvoiceActions.landedCostsAllocationInit({landnedCosts:landedCostsDto}))
  }

  getAllRows() {
    let rowData = [];
    this.gridApi.forEachNode((node) => rowData.push(node.data));
    return rowData;
  }

  onRowClicked(item: any) {
    if(item.item_txn_type!=='GROUP_DISCOUNT' && item.item_txn_type!=='DOC_HEADER_ADJUSTMENT'){
      this.store.dispatch(
        PurchaseInvoiceActions.selectPricingSchemeLink({ item })
      );
      if(item.item_sub_type==='SERIAL_NUMBER'){
        this.viewModelStore.dispatch(Column4ViewModelActions.processSerialNumberListing_Reset());
        this.viewModelStore.dispatch(Column4ViewModelActions.setItemDetailsTab_itemType_Value({itemType:item.item_sub_type.toString()}));
        if(item.serial_no &&  Array.isArray(item.serial_no)){
          const hasInvalidSerial = item.serial_no.filter(s=>s.status==="INVALID");
          if(hasInvalidSerial && hasInvalidSerial.length>0){
            this.viewModelStore.dispatch(Column4ViewModelActions.setSerialNumberTabFieldColor({color: "warn"}));
          }
          this.viewModelStore.dispatch(Column4ViewModelActions.setSerialNumberTab_ScanTab_SerialNumbersListing({serialNumberListing:item.serial_no}));
        }else if (Array.isArray(item.serial_no?.serialNumbers) && item.serial_no.serialNumbers.length > 0) {
          this.draftStore.dispatch(PNSActions.validatePNSSerialNo({ line: item }));
        }
      }
      this.subs.sink = this.fiService.getByGuid(item.item_guid.toString(), AppConfig.apiVisa).subscribe(response=>{
        if(response?.data){
          this.viewModelStore.dispatch(Column4ViewModelActions.setFIItem({fiItem:response.data}))
        }
      })
      this.lineItemEdit.emit(item);
    }

    if(item.guid_segment){
      this.store.dispatch(PurchaseInvoiceActions.loadSegmentLine({ guid: item.guid_segment }));
    }
    if(item.guid_project){
      this.store.dispatch(PurchaseInvoiceActions.loadProjectLine({ guid: item.guid_project }));
    }
    if(item.guid_profit_center){
      this.store.dispatch(PurchaseInvoiceActions.loadProfitCenterLine({ guid: item.guid_profit_center }));
    }
    if(item.guid_dimension){
      this.store.dispatch(PurchaseInvoiceActions.loadDimensionLine({ guid: item.guid_dimension }));
    }
    this.store.dispatch(PurchaseInvoiceActions.selectCopyDepartmentFromHdrFromEditLine({ item: item }));
    if(item.item_txn_type==='BUNDLE'){
      this.viewModelStore.dispatch(
        Column4ViewModelActions.setItemChildJson({ itemChildJson: item.item_child_json })
      );
    }
  }

  getRowClassForAgGrid = params => {
    if (params.node.rowIndex % 2 === 1) {
      return 'ag-grid-odd-rows';
    }
  }

  getRowStylePNS(params) {
    let hasInvalidSerial = false;
    if(params.data.item_sub_type==='SERIAL_NUMBER'
    ){
      hasInvalidSerial = UtilitiesModule.checkSerialValid(<any> params.data.serial_no);
    }
    if (hasInvalidSerial) {
         return {
             'color': 'red',
         }
    }

    if (params.data.item_sub_type === 'BATCH_NUMBER') {
      const batches = params.data.batch_no?.batches || [];

      if (!batches.length) {
        return {
          'color': 'red',
        };
      }

      const totalBatchQty = batches
        .map((batch: { qty: number }) => {
          const qty = batch?.qty ? Number(batch.qty) : 0;
          return qty;
        })
        .reduce((sum: number, qty: number) => sum + qty, 0);

      if (totalBatchQty !== params.data.quantity_base) {
        return {
          'color': 'red',
        };
      }
    }
  };

  isForex(){
    return (this.hdr.base_doc_ccy && this.hdr.base_doc_ccy !== this.hdr.doc_ccy);
  }

  getTotalForex() {
    if (this.isForex()) {
      let amt = 0;
      if (this.hdr.base_doc_xrate && this.hdr.base_doc_xrate !== 0) {
        amt = parseFloat(this.total) / this.hdr.base_doc_xrate;
      }
      return '(' + this.hdr.base_doc_ccy + ' ' + UtilitiesModule.currencyFormatter(amt) + ')';
    }
    return '';
  }

  getTaxForex() {
    if (this.isForex()) {
      let amt = 0;
      if (this.hdr.base_doc_xrate && this.hdr.base_doc_xrate !== 0) {
        amt = parseFloat(this.tax) / this.hdr.base_doc_xrate;
      }
      return '(' + this.hdr.base_doc_ccy + ' ' + UtilitiesModule.currencyFormatter(amt) + ')';
    }
    return '';
  }

  getDoclines(json: any, line) {
    let arr = [];

    if (line.item_txn_type==='MADE_TO_ORDER'&& line.posting_mto!=='POSTED' && json) {
      let arrSection = json['mto'];
      if (arrSection) {
        arrSection.forEach(s => {
          let arrComponent = s['component'];
          arrComponent.forEach(c => {
            arr = [...arr, ...c.doclines.filter(x => x.status === 'ACTIVE')]
          })
        })

      }
    }else if(line.item_txn_type==='BUNDLE' && line.posting_bundle!=='DONE' && json?.childItems){
        arr = json.childItems;
    }
    return arr;
  }

  onGroupDiscAmt() {
    const enteredValue = this.form.controls['discAmt'].value;
    //console.log('onGroupDiscAmt',enteredValue)
    if(Number(enteredValue)>0){
      this.form.controls.discPercentage.setValue(null);
      this.store.dispatch(PurchaseInvoiceActions.addGroupDiscount({ discAmount: enteredValue, discPercentage: null }));
    }
   
  }

  onGroupDiscPercent(){
    const enteredValue = this.form.controls['discPercentage'].value;
    //console.log('onGroupDiscPercent',enteredValue)
    if(Number(enteredValue)>0){
      this.form.controls.discAmt.setValue(null);
      
      this.store.dispatch(PurchaseInvoiceActions.addGroupDiscount({ discAmount: null , discPercentage: enteredValue}));
    }
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }
}
