<div [formGroup]="form">
    <div class="view-col-table" fxLayout="column">
        <mat-card-title class="column-title">
            <div fxLayout="row wrap" fxLayoutAlign="space-between end" fxLayoutGap="10px">
                <div fxFlex="3 0 0">
                    <div fxLayout="row" fxLayoutAlign="space-between center" fxLayoutGap="3px">
                        <button ngClass.xs="blg-button-mobile" #navBtn class="blg-button-icon" mat-button matTooltip="Create" type="button" [disabled]="localState.deactivateAdd" (click)="onAdd()" *ngIf="posting()">
              <img
                [ngClass]="navBtn.disabled ? 'blg-button-disabled' : null"
                src="assets/images/add.png"
                alt="add"
                width="40px"
                height="40px"
              />
            </button>
                        <!-- <app-advanced-search fxFlex fxFlex.lt-sm="100" [id]="'internal-pi-line-item'" [advSearchModel]="searchModel" (search)="onSearch()"></app-advanced-search> -->
                    </div>
                </div>
                <div class="blg-accent" fxFlex="1 0 25" fxLayout="row" fxLayoutAlign="end">

                </div>
            </div>
            <div fxLayout="row wrap" *ngIf="editMode$ | async">
                <div style="padding-right: 5px">
                    <mat-form-field appearance="outline" [style.width.px]="110" *ngIf="!HIDE_LANDED_COST">
                        <input matInput placeholder="Landed Cost" formControlName="landedCost" type="text" />
                    </mat-form-field>
                </div>
                <div style="padding-right: 5px">
                    <mat-form-field appearance="outline" [style.width.px]="110" *ngIf="!HIDE_LANDED_COST">
                        <mat-select placeholder="Allocate By" formControlName="aportionType">
                            <mat-option value="Qty">Quanity</mat-option>
                            <mat-option value="Txn_Amt">Transaction Amount</mat-option>
                            <mat-option value="Equal">Equal</mat-option>
                        </mat-select>
                    </mat-form-field>
                </div>
                <div style="padding-right: 5px">
                    <button mat-raised-button color="primary" type="button" (click)="onAllocate()" style="width: 100px; height: 50px" *ngIf="!HIDE_LANDED_COST">
            ALLOCATE
          </button>
                </div>
                <div>
                    <button mat-raised-button color="primary" type="button" (click)="onConfirm()" style="width: 100px; height: 50px" *ngIf="!HIDE_LANDED_COST">
            CONFIRM
          </button>
                </div>
            </div>
        </mat-card-title>

        <form [formGroup]="form" *ngIf="showGroupDiscount">
            <div fxLayout="row" fxLayoutAlign="space-between center">
            <mat-form-field appearance="outline" fxFlex="50%">
                <mat-label>Group Discount Amount</mat-label>
                <input matInput [formControl]="form.controls['discAmt']" autocomplete="off" type="number" (keyup.enter)="onGroupDiscAmt()">
            </mat-form-field>
            
            <div fxLayout="column" fxLayoutAlign="start end">
                <div style="white-space: nowrap;">
                Total Txn Amount: 
                <strong style="color: #1e88e5; margin-right: 8px;">{{ total }}</strong>
                <strong>{{ (draft$ | async).doc_ccy }}</strong>
                </div>
                <div style="text-align: right;">
                Total SST/VAT/GST Amount: <strong style="color: #1e88e5; margin-right: 8px;">{{ tax }}</strong>
                <strong>{{ (draft$ | async).doc_ccy }}</strong>
                </div>
                <div style="text-align: right;"  *ngIf="hasRoundingItem">
                Rounding: <strong style="color: #1e88e5; margin-right: 8px;">{{ rounding }}</strong>
                <strong>{{ (draft$ | async).doc_ccy }}</strong>
                </div>
                <div style="text-align: right;">
                Discount: <strong style="color: #1e88e5; margin-right: 8px;">{{ discount }}</strong>
                <strong>{{ (draft$ | async).doc_ccy }}</strong>
                </div>
            </div>
            </div>
            <div fxLayout="row" fxLayoutAlign="space-between center">
            <mat-form-field appearance="outline" fxFlex="50%">
                <mat-label>Group Discount %</mat-label>
                <input matInput [formControl]="form.controls['discPercentage']" autocomplete="off" type="number"  (keyup.enter)="onGroupDiscPercent()">
            </mat-form-field>
            </div>
        </form>

        <div style="text-align: right" *ngIf="!showGroupDiscount">
            Total Txn Amount: <strong style="color: #1e88e5">{{ total | number:'1.2-2':'en-US' }} </strong><span><strong>{{ (draft$ | async).doc_ccy }}</strong></span>
            {{ getTotalForex() }}
          </div>
          <div style="text-align: right" *ngIf="!appletSettings?.HIDE_TOTAL_SST_VAT_GST_AMOUNT && !showGroupDiscount">
            Total SST/VAT/GST Amount: <strong style="color: #1e88e5">{{ tax | number:'1.2-2':'en-US' }} </strong><span><strong>{{ (draft$ | async).doc_ccy }}</strong></span>
            {{ getTaxForex() }}
          </div>

        <div style="height: 100%">
          <ag-grid-angular #agGrid style="height: 100%;" class="ag-theme-balham" rowSelection="single"
          [getRowClass]="getRowClassForAgGrid" [rowData]="rowData" [animateRows]="true"
          [defaultColDef]="defaultColDef" [suppressRowClickSelection]="false" [sideBar]="true"
          [frameworkComponents]="frameworkComponents" (rowClicked)="onRowClicked($event.data)"
          (gridReady)="onGridReady($event)" [gridOptions]="gridOptions" [rowDragManaged]="true"
          [masterDetail]="true"
          [isRowMaster]="isRowMaster"
          [detailRowAutoHeight]="true"
          [detailCellRendererParams]="detailCellRendererParams"
          >
        </ag-grid-angular>
        </div>
    </div>
</div>
