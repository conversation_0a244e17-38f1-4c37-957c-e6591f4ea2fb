import { ChangeDetectionStrategy, Component, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { Store } from '@ngrx/store';
import { BranchContainerModel, GuidDataFieldInterface } from 'blg-akaun-ts-lib';
import { SessionActions } from 'projects/shared-utilities/modules/session/session-controller/actions';
import { SessionSelectors } from 'projects/shared-utilities/modules/session/session-controller/selectors';
import { SessionStates } from 'projects/shared-utilities/modules/session/session-controller/states';
import { AppConfig } from 'projects/shared-utilities/visa';
import { SubSink } from 'subsink2';
import { AppletSettings, DEFAULTS } from '../../../models/applet-settings.model';
import { PermissionStates } from 'projects/shared-utilities/modules/permission/permission-controller';
import { UserPermInquirySelectors } from 'projects/shared-utilities/modules/permission/user-permissions-inquiry-controller/selectors';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';

@Component({
  selector: 'app-default-settings',
  templateUrl: './default-settings.component.html',
  styleUrls: ['./default-settings.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class DefaultSettingsComponent implements OnInit, OnDestroy {

  private subs = new SubSink();

  form: FormGroup;

  apiVisa = AppConfig.apiVisa;
  selectedBranch: GuidDataFieldInterface;

  branchGuids: any[];
  locationGuids: any[];

  userPermissionTarget$ = this.permissionStore.select(
    UserPermInquirySelectors.selectUserPermInquiry
  );

  constructor(
    private readonly store: Store<SessionStates>,
    protected readonly permissionStore: Store<PermissionStates>,
  ) { }

  ngOnInit() {
    this.form = new FormGroup({
      DEFAULT_BRANCH: new FormControl(),
      DEFAULT_LOCATION: new FormControl(),
      DEFAULT_COMPANY: new FormControl(),
      DEFAULT_DECIMAL_PRECISION: new FormControl(DEFAULTS.DECIMAL_PRECISION, [Validators.min(2), Validators.max(4)]),
      DEFAULT_DECIMAL_STEP: new FormControl((1 / Math.pow(10, DEFAULTS.DECIMAL_PRECISION)).toString().substring(1)),
      GROUP_DISCOUNT_ITEM_CODE: new FormControl(),
      GROUP_DISCOUNT_ITEM_GUID: new FormControl(),
    });
    this.subs.sink = this.store.select(SessionSelectors.selectMasterSettings).subscribe({
      next: (resolve: AppletSettings) => {
        this.form.patchValue({
          DEFAULT_BRANCH: resolve?.DEFAULT_BRANCH,
          DEFAULT_LOCATION: resolve?.DEFAULT_LOCATION,
          DEFAULT_COMPANY: resolve?.DEFAULT_COMPANY,
          DEFAULT_DECIMAL_PRECISION: resolve?.DEFAULT_DECIMAL_PRECISION ?? DEFAULTS.DECIMAL_PRECISION,
          DEFAULT_DECIMAL_STEP: resolve?.DEFAULT_DECIMAL_STEP ??(1 / Math.pow(10, resolve?.DEFAULT_DECIMAL_PRECISION ?? 2)).toFixed(resolve?.DEFAULT_DECIMAL_PRECISION ?? 2).substring(1),
        });
      }
    });

    this.subs.sink = this.userPermissionTarget$.subscribe((targets) => {
      console.log("targets", targets);
      let target = targets.filter(
        (target) =>
          target.permDfn === "TNT_API_DOC_INTERNAL_PURCHASE_INVOICE_NO_STOCK_IN_READ_TGT_GUID"
      );
      // this.branchGuids = (target[0]?.target!==null && Object.keys(target[0]?.target || {}).length !== 0) ? target[0]?.target["bl_fi_mst_branch"] : [];
      // this.locationGuids = (target[0]?.target!==null && Object.keys(target[0]?.target).length !== 0) ? target[0]?.target["bl_inv_mst_location"] : [];
      let adminCreatePermissionTarget = targets.filter(
        (target) => target.permDfn === "TNT_TENANT_ADMIN"
      );
      let ownerCreatePermissionTarget = targets.filter(
        (target) => target.permDfn === "TNT_TENANT_OWNER"
      );
      if(adminCreatePermissionTarget[0]?.hasPermission || ownerCreatePermissionTarget[0]?.hasPermission){
        this.branchGuids = [];
        this.locationGuids = [];
      }
      else {
        this.branchGuids = (target[0]?.target!==null && Object.keys(target[0]?.target || {}).length !== 0) ? target[0]?.target["bl_fi_mst_branch"] : [];
        this.locationGuids = (target[0]?.target!==null && Object.keys(target[0]?.target).length !== 0) ? target[0]?.target["bl_inv_mst_location"] : [];
      }
    });

    this.subs.sink = this.form.valueChanges
    .pipe(debounceTime(100), distinctUntilChanged())
    .subscribe({
      next: (form) => {
        console.log(form);
        this.selectedBranch = form.DEFAULT_BRANCH;
      },
    });
  }

  onBranchSelected(e: BranchContainerModel) {
    this.selectedBranch = e.bl_fi_mst_branch.guid;
    this.form.patchValue({ DEFAULT_COMPANY: e.bl_fi_mst_branch.comp_guid });
    if (e.bl_fi_mst_branch_ext.find(x => x.param_code === "MAIN_LOCATION")) {
      console.log("main location:: ", e.bl_fi_mst_branch_ext.find(x => x.param_code === "MAIN_LOCATION")?.value_string);
      this.form.controls['DEFAULT_LOCATION'].setValue(e.bl_fi_mst_branch_ext.find(x => x.param_code === "MAIN_LOCATION")?.value_string);
    }
  }

  onSave() {
    this.form.patchValue({
      DEFAULT_DECIMAL_STEP: (1 / Math.pow(10, this.form.value.DEFAULT_DECIMAL_PRECISION)).toString().substring(1),
    });
    this.store.dispatch(SessionActions.saveMasterSettingsInit({ settings: this.form.value }));
  }

  onReset() {
    this.store.dispatch(
      SessionActions.saveMasterSettingsInit({
        settings: {
          DEFAULT_BRANCH: null,
          DEFAULT_LOCATION: null,
          DEFAULT_COMPANY: null,
          DEFAULT_DECIMAL_PRECISION: DEFAULTS.DECIMAL_PRECISION, // default decimal precision
          DEFAULT_DECIMAL_STEP: DEFAULTS.DECIMAL_STEP,
        },
      })
    );
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }

}
