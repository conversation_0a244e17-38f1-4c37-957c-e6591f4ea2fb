import { Component, Input, OnInit, Output, EventEmitter } from '@angular/core';
import { FormControl } from '@angular/forms';
import { MAT_MOMENT_DATE_FORMATS, MomentDateAdapter } from '@angular/material-moment-adapter';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material/core';
import { MatDatepicker } from '@angular/material/datepicker';
import * as _moment from 'moment';
import { default as _rollupMoment, Moment } from 'moment';

const moment = _rollupMoment || _moment;

// Week picker date formats
export const WEEK_FORMATS = {
  parse: {
    dateInput: 'YYYY-[W]WW',
  },
  display: {
    dateInput: 'YYYY-[W]WW',
    monthYearLabel: 'YYYY',
    dateA11yLabel: 'LL',
    monthYearA11yLabel: 'YYYY',
  },
};

@Component({
  selector: 'app-week-picker',
  templateUrl: './week-picker.component.html',
  styleUrls: ['./week-picker.component.css'],
  providers: [
    {
      provide: DateAdapter,
      useClass: MomentDateAdapter,
      deps: [MAT_DATE_LOCALE]
    },
    {
      provide: MAT_DATE_FORMATS,
      useValue: WEEK_FORMATS
    }
  ]
})
export class WeekPickerComponent implements OnInit {
  @Input() id: string;
  @Input() date: FormControl = new FormControl();
  @Input() label = 'Week';
  @Input() options: any;
  @Input() min: Date | null = null;
  @Output() dateChanged = new EventEmitter<Date>();

  ngOnInit() {
  }

  onDateSelected(event: any) {
    const selectedDate = moment(event.value);
    this.dateChanged.emit(selectedDate.toDate());
  }

  isRequired() {
    if (this.options) {
      const value = this.options['required'];
      if (typeof value === 'boolean') {
        return value;
      }
      if (Array.isArray(value)) {
        return value.includes(true);
      }
    }
    return false;
  }
}
