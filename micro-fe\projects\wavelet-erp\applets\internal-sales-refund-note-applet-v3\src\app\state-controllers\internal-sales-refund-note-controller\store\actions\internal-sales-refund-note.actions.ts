import { createAction, props } from "@ngrx/store";
import {
  bl_fi_generic_doc_line_RowClass,
  bl_fi_mst_entity_line_RowClass,
  EntityContainerModel,
  GenericDocARAPContainerModel,
  GenericDocContainerModel,
  JsonDatatypeInterface,
  MembershipCardContainerModel,
  Pagination,
  PricingSchemeLinkContainerModel,
  GenericDocSearchCriteriaDtoModel,
  BranchContainerModel,
  FinancialItemContainerModel,
  AppletSettingFilterItemCategoryLinkContainerModel
} from "blg-akaun-ts-lib";

// export const loadInternalSalesRefundNoteInit = createAction('[Internal Sales Refund Note] Load Init', props<{ request: any }>());
// export const loadInternalSalesRefundNoteSuccess = createAction('[Internal Sales Refund Note Load Success', props<{ totalRecords: number }>());
// export const loadInternalSalesRefundNoteFailed = createAction('[Internal Sales Refund Note] Load Failed', props<{ error: string }>());

export const loadInternalSalesRefundNoteInit = createAction(
  "[Internal Sales Refund Note] Load Init",
  props<{ pagination: Pagination }>()
);
export const loadInternalSalesRefundNoteSuccess = createAction(
  "[Internal Sales Refund Note Load Success",
  props<{ SalesRefundNotes: GenericDocContainerModel[]; snapshotGuid?: string }>()
);
export const loadInternalSalesRefundNoteFailed = createAction(
  "[Internal Sales Refund Note] Load Failed",
  props<{ error: string }>()
);
export const searchSalesRefundNoteInit = createAction(
  "[Purchase Order] Search Init",
  props<{ searchDto: GenericDocSearchCriteriaDtoModel }>()
);

export const selectEntity = createAction(
  "[Internal Sales Refund Note] Select Entity",
  props<{
    entity: {
      entity: EntityContainerModel;
      contact: bl_fi_mst_entity_line_RowClass;
    };
  }>()
);
export const selectEntityOnEdit = createAction(
  "[Internal Sales Refund Note] Select Entity On Edit",
  props<{
    entity: {
      entity: EntityContainerModel;
      contact: bl_fi_mst_entity_line_RowClass;
    };
  }>()
);
export const selectMember = createAction(
  "[Internal Sales Refund Note] Select Member",
  props<{ member: MembershipCardContainerModel }>()
);
export const selectShippingAddress = createAction(
  "[Internal Sales Refund Note] Select Shipping Address",
  props<{ shipping_address: JsonDatatypeInterface }>()
);
export const selectBillingAddress = createAction(
  "[Internal Sales Refund Note] Select Billing Address",
  props<{ billing_address: JsonDatatypeInterface }>()
);
export const selectLineItem = createAction(
  "[Internal Sales Refund Note] Select Line Item",
  props<{ lineItem: bl_fi_generic_doc_line_RowClass }>()
);
export const selectSalesRefundNoteForEdit = createAction(
  "[Internal Sales Refund Note] Select Internal Sales Refund Note For Edit",
  props<{ genDoc: GenericDocContainerModel }>()
);
export const selectContraDoc = createAction(
  "[Internal Sales Refund Note] Select Contra Doc",
  props<{ entity: GenericDocContainerModel }>()
);
export const selectContraLink = createAction(
  "[Internal Sales Refund Note] Select Contra Link",
  props<{ link: GenericDocARAPContainerModel }>()
);
export const resetDraft = createAction(
  "[Internal Sales Refund Note] Reset Internal Sales Refund Note"
);
export const selectMode = createAction(
  "[Internal Sales Refund Note] Select Internal Sales Refund Note View Mode",
  props<{ mode: string }>()
);
export const selectSettlement = createAction(
  "[Internal Sales Refund Note] Select Settlement",
  props<{ settlement: bl_fi_generic_doc_line_RowClass }>()
);
export const selectAttachmentGuid = createAction(
  "[Internal Sales Refund Note] Select Attachment",
  props<{ guid: string }>()
);

export const selectPricingSchemeLink = createAction(
  "[Internal Sales Refund Note] Select Pricing Scheme Link",
  props<{ item: any }>()
);
export const selectPricingSchemeLinkSuccess = createAction(
  "[Internal Sales Refund Note] Select Pricing Link Scheme Success",
  props<{ pricing: PricingSchemeLinkContainerModel[] }>()
);
export const selectPricingSchemeLinkFailed = createAction(
  "[Internal Sales Refund Note] Select Pricing Link Scheme Failed",
  props<{ error: string }>()
);

export const selectPricingScheme = createAction(
  "[Internal Sales Refund Note]  Select Pricing Scheme",
  props<{ pricingScheme: any }>()
);
export const addPricingSchemeLinkInit = createAction(
  "[Internal Sales Refund Note]  Add Pricing Scheme Link Init",
  props<{ link: PricingSchemeLinkContainerModel }>()
);
export const addPricingSchemeLinkSuccess = createAction(
  "[Internal Sales Refund Note]  Add Pricing Scheme Link Success"
);
export const addPricingSchemeLinkFailed = createAction(
  "[Internal Sales Refund Note]  Add Pricing Scheme Link Failed",
  props<{ error: string }>()
);
export const editPricingSchemeLinkInit = createAction(
  "[Internal Sales Refund Note]  Edit Pricing Scheme Link Init",
  props<{ link: PricingSchemeLinkContainerModel }>()
);
export const editPricingSchemeLinkSuccess = createAction(
  "[Internal Sales Refund Note]  Edit Pricing Scheme Link Success"
);
export const editPricingSchemeLinkFailed = createAction(
  "[Internal Sales Refund Note]  Edit Pricing Scheme Link Failed",
  props<{ error: string }>()
);

export const editGenLineItemInit = createAction(
  "[Internal Sales Refund Note] Edit Generic Doc Line Item Init"
);
export const editGenLineItemSuccess = createAction(
  "[Internal Sales Refund Note] Edit Generic Doc Line Item Success"
);
export const editGenLineItemFailed = createAction(
  "[Internal Sales Refund Note] Edit Generic Doc Line Item Failed",
  props<{ error: string }>()
);

export const createInternalSalesRefundNoteInit = createAction(
  "[Internal Sales Refund Note] Create Init"
);
export const createInternalSalesRefundNoteSuccess = createAction(
  "[Internal Sales Refund Note] Create Success",
  props<{ hdrGuid: string }>()
);
export const createInternalSalesRefundNoteFailed = createAction(
  "[Internal Sales Refund Note] Create Failed",
  props<{ error: string }>()
);

export const deleteInternalSalesRefundNoteInit = createAction(
  "[Internal Sales Refund Note] Delete Init"
);
export const deleteInternalSalesRefundNoteSuccess = createAction(
  "[Internal Sales Refund Note] Delete Success"
);
export const deleteInternalSalesRefundNoteFailed = createAction(
  "[Internal Sales Refund Note] Delete Failed",
  props<{ error: string }>()
);

export const editInternalSalesRefundNoteInit = createAction(
  "[Internal Sales Refund Note] Edit Init"
);
export const editInternalSalesRefundNoteSuccess = createAction(
  "[Internal Sales Refund Note] Edit Success",
  props<{ hdrGuid: string }>()
);
export const editInternalSalesRefundNoteFailed = createAction(
  "[Internal Sales Refund Note] Edit Failed",
  props<{ error: string }>()
);

export const printJasperPdfInit = createAction(
  "[Internal Sales Refund Note] Print Jasper Pdf Init",
  props<{ guid: string }>()
);
export const printJasperPdfSuccess = createAction(
  "[Internal Sales Refund Note] Print Jasper Pdf Success"
);
export const printJasperPdfFailed = createAction(
  "[Internal Sales Refund Note] Print Jasper Pdf Failed"
);

export const openJasperPdfInit = createAction(
  '[Internal Sales Refund Note] Open Jasper Pdf Init',
  props<{ guid: string }>()
);

export const openJasperPdfSuccess = createAction(
  '[InternalSalesRefundNote] Open Jasper Pdf Success'
);

export const openJasperPdfFailed = createAction(
  '[InternalSalesRefundNote] Open Jasper Pdf Failed',
  props<{ error: any }>()
);

export const addContra = createAction(
  "[Internal Sales Refund Note] Add Contra",
  props<{ contraDoc: GenericDocARAPContainerModel }>()
);
export const addContraInit = createAction(
  "[Internal Sales Refund Note] Add Contra Init"
);
export const addContraSuccess = createAction(
  "[Internal Sales Refund Note] Add Contra Success"
);
export const addContraFailed = createAction(
  "[Internal Sales Refund Note] Add Contra Failed",
  props<{ error: string }>()
);

export const editInternalSalesRefundNoteBeforeContraInit = createAction(
  "[Internal Sales Refund Note] Edit Before Contra Init"
);
export const editInternalSalesRefundNoteBeforeContraSuccess = createAction(
  "[Internal Sales Refund Note] Edit Before Contra Success"
);
export const editInternalSalesRefundNoteBeforeContraFailed = createAction(
  "[Internal Sales Refund Note] Edit Before Contra Failed",
  props<{ error: string }>()
);

export const deleteContraInit = createAction(
  "[Internal Sales Refund Note] Delete Contra Init",
  props<{ guid: string, docHdrGuid: string }>()
);
export const deleteContraSuccess = createAction(
  "[Internal Sales Refund Note] Delete Contra Success"
);
export const deleteContraFailed = createAction(
  "[Internal Sales Refund Note] Delete Contra Failed",
  props<{ error: string }>()
);


export const editedGenDoc = createAction('[Internal Sales Refund Note] Edited Generic Doc', props<{ edited: boolean }>());
export const recalculateDocBalance = createAction('[Internal Sales Refund Note] Recalculate Doc Balance');
export const recalculateDocBalanceSuccess = createAction('[Internal Sales Refund Note] Recalculate Doc Balance Success');

export const addMultipleContraInit = createAction(
  "[Internal Sales Refund Note] Add Multiple Contra Init",
  props<{ contra: GenericDocARAPContainerModel[]}>()
);
export const addMultipleContraSucess = createAction(
  "[Internal Sales Refund Note] Add Multiple Contra Success",
  props<{ contra: GenericDocARAPContainerModel[]}>()
);

export const addMultipleContraFailed = createAction(
  "[Internal Sales Refund Note] Add Multiple Contra Failed",
  props<{ error: string}>()
);

export const resetAgGrid = createAction(
  "[Internal Sales Refund Note] Reset Ag Grid Update"
);

export const updatePostingStatus = createAction(
  "[Internal Sales Refund Note] Update Posting Status",
  props<{ status: any; doc: GenericDocContainerModel }>()
);
export const updatePostingStatusSuccess = createAction(
  "[Internal Sales Refund Note] Update Posting Status Success",
  props<{ doc: GenericDocContainerModel }>()
);
export const updatePostingStatusFailed = createAction(
  "[Internal Sales Refund Note] Update Posting Status Failed",
  props<{ error: string }>()
);

export const updateKnockoffListingConfig = createAction(
  "[Internal Sales Refund Note] Update Knockoff Listing Config",
  props<{ settings: any }>()
);

export const creatInternalSalesRefundNoteGenDocLinkSuccess = createAction(
  "[Internal Sales Refund Note] Create Gen Doc Link Success"
);
export const createInternalSalesRefundNoteGenDocLinkFailed = createAction(
  "[Internal Sales Refund Note] Create Gen Doc Link Failed",
  props<{ error: string }>()
);
export const editInternalSalesRefundNoteGenDocLinkSuccess = createAction(
  "[Internal Sales Refund Note] Edit Gen Doc Link Success"
);
export const editInternalSalesRefundNoteGenDocLinkFailed = createAction(
  "[Internal Sales Refund Note] Edit Gen Doc Link Failed",
  props<{ error: string }>()
);
export const editInternalSalesRefundNoteGenDocLinkFinalSuccess = createAction(
  "[Internal Sales Refund Note] Edit Gen Doc Link on Final Success",
  props<{ status?: any; doc?: GenericDocContainerModel }>()
);
export const editInternalSalesRefundNoteGenDocLinkFinalFailed = createAction(
  "[Internal Sales Refund Note] Edit Gen Doc Link on Final Failed",
  props<{ error: string }>()
);
export const setEditMode = createAction(
  "[Internal Sales Refund Note] Set Edit Mode",
  props<{ editMode: boolean }>()
);

export const selectCustomerForSalesRefundNote = createAction(
  "[Internal Sales Refund Note] Search Select Customer For Sales Refund Note",
  props<{ customerGuid: string }>()
);
export const selectInvoiceForSalesRefundNote = createAction(
  "[Internal Sales Refund Note] Search Select Inovice For Sales Refund Note",
  props<{ invoiceGuid: string }>()
);

export const loadContraInit = createAction(
  "[Contra] Load Init",
  props<{ request: any }>()
);
export const loadContraSuccess = createAction(
  "[Contra] Load Success",
  props<{ totalRecords: number }>()
);
export const loadContraFailure = createAction(
  "[Contra] Load Failure",
  props<{ error: string }>()
);

export const updateAgGridDone = createAction(
  "[Contra] Update AG Grid Done",
  props<{ done: boolean }>()
);

export const selectTotalRevenue = createAction(
  "[Contra] Select Total Revenue",
  props<{ totalRevenue: any }>()
);
export const selectTotalExpense = createAction(
  "[Contra] Select Total Expense",
  props<{ totalExpense: any }>()
);
export const selectTotalSettlement = createAction(
  "[Contra] Select Total Settlement",
  props<{ totalSettlement: any }>()
);
export const selectTotalContra = createAction(
  "[Contra] Select Contra Amount",
  props<{ totalContra: any }>()
);
export const selectDocOpenAmount = createAction(
  "[Contra] Select Doc Open Amount",
  props<{ docOpenAmount: any }>()
);
export const selectDocArapBalance = createAction(
  "[Contra] Select Doc ARAP Balance",
  props<{ docArapBalance: any }>()
);

export const refreshArapListing = createAction(
  "[Contra] Refresh Listing",
  props<{ refreshArapListing: boolean }>()
);
export const loadArapListingInit = createAction(
  "[Contra] Load Contra Init",
  props<{ pagination: Pagination }>()
);
export const loadArapListingSuccess = createAction(
  "[Contra] Load Contra Success",
  props<{ arapListing: GenericDocARAPContainerModel[] }>()
);
export const loadArapListingFailed = createAction(
  "[Contra] Load Contra Failed",
  props<{ error: string }>()
);

export const discardInit = createAction(
  "[Internal Sales Refund Note] Discard Init",
  props<{ guids: string[]; fromEdit?: boolean }>()
);
export const discardComplete = createAction(
  "[Internal Sales Refund Note] Discard Complete",
  props<{ total: number; successCount: number; failureCount: number }>()
);
export const discardFailure = createAction(
  "[Internal Sales Refund Note] Discard Failure",
  props<{ error: any }>()
);

export const selectGUID = createAction(
  "[Internal Sales Refund Note] Select GUID",
  props<{ guid: string }>()
);

export const updateAfterContra = createAction(
  "[Contra] Update Gen Doc After Contra",
  props<{ genDoc: GenericDocContainerModel }>()
);
export const updateAfterContraFailed = createAction(
  "[Contra] Update Gen Doc After Contra Failed",
  props<{ error: string }>()
);

export const resetContra = createAction("[Contra] Reset Contra");

export const voidSalesRefundNoteInit = createAction(
  "[Internal Sales Refund Note] Void  Init",
  props<{ status: any; doc: GenericDocContainerModel }>()
);
export const voidSalesRefundNoteSuccess = createAction(
  "[Internal Sales Refund Note] Void  Success",
  props<{ doc: any }>()
);
export const voidSalesRefundNoteFailed = createAction(
  "[Internal Sales Refund Note] Void  Failed",
  props<{ error: string }>()
);

export const getTotalRecords = createAction(
  "[Internal Sales Refund Note] Get Total Records Init",
  props<{ searchDto: GenericDocSearchCriteriaDtoModel }>()
);
export const getTotalRecordsSuccess = createAction(
  "[Internal Sales Refund Note] Get Total Records Success",
  props<{ totalRecords: number }>()
);
export const getTotalRecordsFailed = createAction(
  "[Internal Sales Refund Note] Get Total Records Failed",
  props<{ error: string }>()
);

export const createTempSalesRefundNoteInit = createAction(
  "[Internal Sales Refund Note] Create Temp SR Init",
  props<{ doc: GenericDocContainerModel }>()
);
export const createTempSalesRefundNoteSuccess = createAction(
  "[Internal Sales Refund Note] Create Temp SR Success",
  props<{ response: GenericDocContainerModel }>()
);
export const createTempSalesRefundNoteFailed = createAction(
  "[Internal Sales Refund Note] Create Temp SR Failed",
  props<{ error: string }>()
);

export const convertToActiveInit = createAction(
  "[Internal Sales Refund Note] Convert to Active Init"
);
export const convertToActiveSuccess = createAction(
  "[Internal Sales Refund Note]  Convert to Active Success",
  props<{ hdrGuid: string }>()
);
export const convertToActiveFailed = createAction(
  "[Internal Sales Refund Note] Convert to Active Failed",
  props<{ error: string }>()
);
export const resetconvertActionDispatchedState = createAction(
  "[Internal Sales Refund Note] Reset Convert Action Dispatched State"
);

export const editSalesRefundNoteFinalInit = createAction(
  "[Internal Sales Refund Note] Edit and Final Init"
);
export const editSalesRefundNoteFinalSuccess = createAction(
  "[Internal Sales Refund Note] Edit and Final Success",
  props<{ status: any; doc: GenericDocContainerModel, hdrGuid?: string }>()
);
export const editSalesRefundNoteFinalFailed = createAction(
  "[Internal Sales Refund Note] Edit and Final Failed",
  props<{ error: string }>()
);

export const updateContraInit = createAction(
  "[Internal Sales Refund Note] Save Contra Init",
  props<{ txn_date: string }>()
);
export const updateContraSuccess = createAction(
"[Internal Sales Refund Note] Save Contra Success",
// props<{ txn_date: string }>()
);
export const updateContraFailed = createAction(
"[Internal Sales Refund Note]  Save Contra Failed",
props<{ error: string }>()
);

export const printMultipleJasperPdfInit = createAction('[Internal Sales Refund Note] Print Multiple Jasper Pdf Init', props<{ guids: string[], printable: string, preview: boolean, docNumbers: string[] }>());
export const printMultipleJasperPdfSuccess = createAction('[Internal Sales Refund Note] Print Multiple Jasper Pdf Success');
export const printMultipleJasperPdfFailed = createAction('[Internal Sales Refund Note] Print Multiple Jasper Pdf Failed');

export const loadRoundingItemSuccess = createAction('[Internal Sales Refund Note]  Load Rounding Item Success', props<{ item: FinancialItemContainerModel,isRounding: boolean  }>());
export const loadRoundingItemFailed = createAction('[Internal Sales Refund Note]  Load Rounding Item Failed', props<{err: String}>());
export const resetRoundingGroupDiscountItem = createAction('[Internal Sales Refund Note]  Reset Rounding Group Discount Item');
export const loadGroupDiscountItemSuccess = createAction('[Internal Sales Refund Note]  Load Group Discount Item Success', props<{ item: FinancialItemContainerModel }>());
export const loadGroupDiscountItemFailed = createAction('[Internal Sales Refund Note]  Load Group Discount Item Failed', props<{err: String}>());
export const loadBranchCompany = createAction('[Internal Sales Refund Note]  Load Branch Company', props<{ compGuid: any,branchGuid:any, changeDefault: boolean, branchObj: BranchContainerModel }>());
export const loadBranchCompanySuccess = createAction('[Internal Sales Refund Note]  Load Branch Company Success', props<{ branch: any,company:any }>());
export const loadBranchCompanFailed = createAction('[Internal Sales Refund Note]  Load Branch Company Failed', props<{err: String}>());
export const selectEInvoiceEnabled = createAction('[Internal Sales Refund Note] Select E-invoice enabled', props<{ val: boolean}>());
export const selectCOA = createAction('[Internal Sales Refund Note] Select COA', props<{ coa: any}>());
export const selectRoundingItem = createAction('[Internal Sales Refund Note]  Select Rounding Item', props<{ item: FinancialItemContainerModel }>());

export const updateEInvoiceDetails = createAction('[E-Invoice] Update E-Invoice Details', props<{ form: any }>());
export const updateSingleGeneralDetails = createAction('[Internal Sales Refund Note] Update Single General Details', props<{ form: any }>());
export const setIsEinvoiceSubmissionAnotherCustomer = createAction('[E-Invoice] Is E-Invoice Another customer', props<{ isEinvoiceSubmissionAnotherCustomer: boolean}>());
export const setEinvoiceSubmissionAnotherCustomerDetails = createAction('[E-Invoice] Set E-Invoice Another customer details', props<{ einvoiceSubmissionAnotherCustomerDetails: any}>());

export const printEInvoiceJasperPdfInit = createAction('[Internal Sales Refund Note] Print E-Invoice Jasper Pdf Init', props<{ hdr: string }>());
export const printEInvoiceJasperPdfSuccess = createAction('[Internal Sales Refund Note] Print E-Invoice Jasper Pdf Success');
export const printEInvoiceJasperPdfFailed = createAction('[Internal Sales Refund Note] Print E-Invoice Jasper Pdf Failed');

export const selectSettingItemFilter = createAction('[Internal Sales Refund Note] select Settings Item Filter', props<{branch: any}>());
export const selectSettingItemFilterSuccess = createAction('[Internal Sales Refund Note] select Settings Item Filter Success', props<{setting: AppletSettingFilterItemCategoryLinkContainerModel[]}>());
export const selectSettingItemFilterFailed = createAction('[Internal Sales Refund Note] select Settings Item Filter Failed', props<{error: string}>());
export const resetSettingItemFilter = createAction('[Internal Sales Refund Note] Reset Selected Settings Item Filter');
export const saveSettingItemFilter = createAction('[Internal Sales Refund Note] Save Settings Item Filter', props<{form: any, branch: any}>());
export const saveSettingItemFilterSuccess = createAction('[Internal Sales Refund Note] Save Settings Item Filter Success');
export const saveSettingItemFilterFailed = createAction('[Internal Sales Refund Note] Save Settings Item Filter Failed', props<{error: string}>());
export const loadSettingItemFilter = createAction('[Internal Sales Refund Note] load Settings Item Filter');
export const loadSettingItemFilterSuccess = createAction('[Internal Sales Refund Note] load Settings Item Filter Success', props<{setting: AppletSettingFilterItemCategoryLinkContainerModel[]}>());
export const loadSettingItemFilterFailed = createAction('[Internal Sales Refund Note] load Settings Item Filter Failed', props<{error: string}>());

export const resetExpansionPanel = createAction('[Internal Sales Refund Note] Reset Expansion Panel Index', props<{ resetIndex: boolean }>());

export const lockDocument = createAction('[Internal Sales Refund Note] Lock Gen Doc');
export const unlockDocument = createAction('[Internal Sales Refund Note] Unlock Gen Doc', props<{hdrGuid:string}>());
export const unlockDocumentSuccess = createAction('[Internal Sales Refund Note] Unlock Gen Doc Success');
export const unlockDocumentFailed = createAction('[Internal Sales Refund Note] Unlock Gen Doc Failed', props<{error: string}>());

export const selectRowData = createAction('[Internal Sales Refund Note] Select Row Data', props<{rowData: []}>());
export const selectTotalRecords = createAction('[Internal Sales Refund Note] Select Total Records', props<{totalRecords: number }>());
export const selectSearchItemRowData = createAction('[Internal Sales Refund Note] Select Search Item Row Data', props<{rowData: []}>());
export const selectSearchItemTotalRecords = createAction('[Internal Sales Refund Note] Select Search Item Total Records', props<{totalRecords: number }>());
export const selectFirstLoadListing = createAction('[Internal Sales Refund Note] Select First Load Listing', props<{firstLoadListing: boolean }>());

export const einvoiceNtfQueueProcessInit = createAction('[Internal Sales Refund Note] Notification Queue Process Init', props<{ dto: any}>());
export const einvoiceNtfQueueProcessSuccess = createAction('[Internal Sales Refund Note] Notification Queue Process Success');
export const einvoiceNtfQueueProcessFailed = createAction('[Internal Sales Refund Note] Notification Queue Process Failed', props<{error: string}>());

export const updateKOStatus = createAction('[Internal Sales Refund Note] update KO Status', props<{status: string }>());

export const selectEInvoiceMainDocRef = createAction('[Internal Sales Refund Note] Select EInvoice Main Doc Ref', props<{ toIrb: any }>());

export const addGroupDiscount = createAction('[Internal Sales Refund Note] Add Group Discount', props<{discAmount: number, discPercentage: number}>());
export const addGroupDiscountSuccess = createAction('[Internal Sales Refund Note] Add Group Discount Success', props<{discPercentage: number}>());
export const addGroupDiscountFailed = createAction('[Internal Sales Refund Note] Add Group Discount Failed');
export const recalculateGroupDiscount = createAction('[Internal Sales Refund Note] Recalculate Group Discount');
export const recalculateGroupDiscountSuccess = createAction('[Internal Sales Refund Note] Recalculate Group Discount Success');
export const recalculateGroupDiscountFailed = createAction('[Internal Sales Refund Note] Recalculate Group Discount Failed');

export const selectSettlementAdjustment = createAction(
  "[Internal Sales Refund Note] Select Settlement Adjustment",
  props<{ settlementAdjustment: bl_fi_generic_doc_line_RowClass }>()
);

export const selectAdjustSettlement = createAction('[Internal Sales Refund Note] Select Adjust Settlement', props<{guid: any}>());
export const selectAdjustSettlementSuccess = createAction('[Internal Sales Refund Note] Select Adjust Settlement Success', props<{adjustment: any}>());
export const selectAdjustSettlementFailed = createAction('[Internal Sales Refund Note] Select Adjust Settlement Failed');

export const selectEditAdjustment = createAction('[Internal Sales Refund Note] Select Edit Settlement Adjustment', props<{value: boolean}>());

export const selectPricingSchemeGuid = createAction('[Internal Sales Refund Note] Select Pricing Scheme Guid', props<{ guid: any }>());
export const selectChildAttributeLink = createAction('[Internal Sales Refund Note] Select Child Attribute Link', props<{ link: any[] }>());
export const updateChildAttributeLink = createAction('[Internal Sales Refund Note] Update Child Attribute Link', props<{ link: any[] }>());
export const selectChildItem = createAction('[Internal Sales Refund Note] Select Child Item', props<{ child: any[] }>());
export const updateChildItem = createAction('[Internal Sales Refund Note] Update Child Item', props<{ child: any }>());
export const selectChildItemPricingLink = createAction('[Internal Sales Refund Note] Select Child Item Pricing Link', props<{ child: any }>());
export const selectChildItemPricingLinkSuccess = createAction('[Internal Sales Refund Note] Select Child Item Pricing Link Success', props<{ price: any }>());
export const selectChildItemPricingLinkFailed = createAction('[Internal Sales Refund Note] Select Child Item Pricing Link Failed', props<{ error: any }>());
export const selectPricingSchemeHdr = createAction('[Internal Sales Refund Note] Select Pricing Scheme Hdr', props<{ pricingSchemeHdr: any }>());

export const setCurrentlySelectedDocumentTypeLinked = createAction('[Internal Sales Refund Note] Set Current Document Type That Linked', props<{ linkedDocType: any }>());
