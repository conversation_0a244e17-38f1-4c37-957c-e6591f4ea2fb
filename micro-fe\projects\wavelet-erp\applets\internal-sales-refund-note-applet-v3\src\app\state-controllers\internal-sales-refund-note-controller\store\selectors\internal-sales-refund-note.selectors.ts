import { createFeatureSelector, createSelector } from '@ngrx/store';
import { InternalSalesRefundNoteFeatureKey } from '../reducers/internal-sales-refund-note.reducers';
import { InternalSalesRefundNoteStates } from '../states';
import { InternalSalesRefundNoteState } from '../states/internal-sales-refund-note.states';
import {
  InternalPaymentVoucherStates
} from "../../../../../../../internal-payment-voucher-applet/src/app/state-controllers/internal-payment-voucher-controller/store/states";

export const selectInternalSalesRefundNoteFeature = createFeatureSelector<InternalSalesRefundNoteState>(InternalSalesRefundNoteFeatureKey);

export const selectTotalRecords = (state: InternalSalesRefundNoteStates) => state.internalSalesRefundNote.totalRecords;
export const selectSalesRefundNotes = (state: InternalSalesRefundNoteStates) => state.internalSalesRefundNote.loadedGenDocs;
export const selectEntity = (state: InternalSalesRefundNoteStates) => state.internalSalesRefundNote.selectedEntity;
export const selectMember = (state: InternalSalesRefundNoteStates) => state.internalSalesRefundNote.selectedMember;
export const selectLineItem = (state: InternalSalesRefundNoteStates) => state.internalSalesRefundNote.selectedLineItem;
export const selectSalesRefundNote = (state: InternalSalesRefundNoteStates) => state.internalSalesRefundNote.selectedSalesRefundNote;
export const selectMode = (state: InternalSalesRefundNoteStates) => state.internalSalesRefundNote.selectedMode;
export const selectSettlement = (state: InternalSalesRefundNoteStates) => state.internalSalesRefundNote.selectedSettlement;
export const refreshGenDocListing = (state: InternalSalesRefundNoteStates) => state.internalSalesRefundNote.refreshGenDocListing;
export const selectContraDoc = (state: InternalSalesRefundNoteStates) => state.internalSalesRefundNote.selectedContraDoc;
export const selectContraLink = (state: InternalSalesRefundNoteStates) => state.internalSalesRefundNote.selectedContraLink;
export const selectPrintableFormatGuid = (state: InternalSalesRefundNoteStates) => state.internalSalesRefundNote.selectedPrintableFormatGuid;
export const selectAttachmentGuid = (state: InternalSalesRefundNoteStates) => state.internalSalesRefundNote.selectedAttachmentGuid;
export const selectPricingScheme = (state: InternalSalesRefundNoteStates) => state.internalSalesRefundNote.selectedPricingScheme;
export const selectKnockoffListingConfig = (state: InternalSalesRefundNoteStates) => state.internalSalesRefundNote.knockoffListingConfig;
export const selectEditMode = (state: InternalSalesRefundNoteStates) => state.internalSalesRefundNote.editMode;
export const selectCustomerForSalesRefundNote = (state: InternalSalesRefundNoteStates) => state.internalSalesRefundNote.selectedCustomerForSalesRefundNote;
export const selectInvoiceForSalesRefundNote = (state: InternalSalesRefundNoteStates) => state.internalSalesRefundNote.selectedInvoiceForSalesRefundNote;
export const updateAgGrid = (state: InternalSalesRefundNoteStates) => state.internalSalesRefundNote.updateContraAgGrid;
export const selectedTotalRevenue = (state: InternalSalesRefundNoteStates) => state.internalSalesRefundNote.totalRevenue;
export const selectedTotalExpense = (state: InternalSalesRefundNoteStates) => state.internalSalesRefundNote.totalExpense;
export const selectedTotalSettlement = (state: InternalSalesRefundNoteStates) => state.internalSalesRefundNote.totalSettlement;
export const selectedTotalContra = (state: InternalSalesRefundNoteStates) => state.internalSalesRefundNote.totalContra
export const selectedDocOpenAmount = (state: InternalSalesRefundNoteStates) => state.internalSalesRefundNote.docOpenAmount;
export const selectedDocArapBalance = (state: InternalSalesRefundNoteStates) => state.internalSalesRefundNote.docArapBalance;
export const selectRefreshArapValue = (state: InternalSalesRefundNoteStates) => state.internalSalesRefundNote.refreshArapListing;
export const selectArapListing = (state: InternalSalesRefundNoteStates) => state.internalSalesRefundNote.loadedArap;
export const selectGuid = (state: InternalSalesRefundNoteStates) => state.internalSalesRefundNote.selectedGuid;
export const selectInvItem = (state: InternalSalesRefundNoteStates) => state.internalSalesRefundNote.selectedInvItem;
export const selectIsEinvoiceSubmissionAnotherCustomer  = (state: InternalSalesRefundNoteStates) => state.internalSalesRefundNote.isEinvoiceSubmissionAnotherCustomer;
export const selectItemCategoryFilter = (state: InternalSalesRefundNoteStates) => state.internalSalesRefundNote.selectedItemCategoryFilter;
export const selectCategoryGroupList = (state: InternalSalesRefundNoteStates) => state.internalSalesRefundNote.categoryGroupList;
export const selectKoStatus = (state: InternalSalesRefundNoteStates) => state.internalSalesRefundNote.koStatus
export const selectSettlementAdjustment = (state: InternalSalesRefundNoteStates) => state.internalSalesRefundNote.selectedSettlementAdjustment;
export const selectCurrentlySelectedDocumentTypeLinked = (state: InternalSalesRefundNoteStates) => state.internalSalesRefundNote.linkedDocType;

export const selectInternalSalesRefundNotes = createSelector(
  selectInternalSalesRefundNoteFeature,
  (state) => state.selectedSalesRefundNote
);


export const getPricingSchemeLinks = createSelector(
  selectInternalSalesRefundNoteFeature,
  (state) => state.pricingSchemeLink
);

export const selectTempDoc = createSelector(
  selectInternalSalesRefundNoteFeature,
  (state) => state.createdTempDoc
);

export const getMyConversionActionState = createSelector(
  selectInternalSalesRefundNoteFeature,
  (state) => state.convertActionDispatched
);

export const selectAddedContraDoc = createSelector(
  selectInternalSalesRefundNoteFeature,
  (state) => state.addedContraDoc
);

export const selectEInvoiceEnabled = createSelector(
  selectInternalSalesRefundNoteFeature,
  (state) => state.eInvoiceEnabled
);

export const selectCOA = createSelector(
  selectInternalSalesRefundNoteFeature,
  (state) => state.selectedCOA
);

export const selectRoundingFiveCent = createSelector(
  selectInternalSalesRefundNoteFeature,
  (state) => state.roundingFiveCent
);

export const selectGroupDiscountItem = createSelector(
  selectInternalSalesRefundNoteFeature,
  (state) => state.groupDiscountItem
);

export const selectGroupDiscountPercentage = createSelector(
  selectInternalSalesRefundNoteFeature,
  (state) => state.groupDiscountPercentage
);

export const selectResetExpansionPanel = createSelector(
  selectInternalSalesRefundNoteFeature,
  (state) => state.resetExpansionPanel
);

export const selectGenDocLock = createSelector(
  selectInternalSalesRefundNoteFeature,
  (state) => state.docLock
);

export const selectRowData = createSelector(
  selectInternalSalesRefundNoteFeature,
  (state) => state.rowData
);
export const selectSearchItemRowData = createSelector(
  selectInternalSalesRefundNoteFeature,
  (state) => state.searchItemRowData
);
export const selectSearchItemTotalRecords = createSelector(
  selectInternalSalesRefundNoteFeature,
  (state) => state.searchItemTotalRecords
);
export const selectFirstLoadListing = createSelector(
  selectInternalSalesRefundNoteFeature,
  (state) => state.firstLoadListing
);

export const selectSettlementMethodAdjustment = createSelector(
  selectInternalSalesRefundNoteFeature,
  (state) => state.settlementMethodAdjustment
);

export const selectEditAdjustment = createSelector(
  selectInternalSalesRefundNoteFeature,
  (state) => state.isEditAdjustment
);

export const selectPricingSchemeGuid = createSelector(
  selectInternalSalesRefundNoteFeature,
  (state) => state.pricingSchemeGuid
);
export const selectSelectedChildAttributeLink = createSelector(
  selectInternalSalesRefundNoteFeature,
  (state) => state.selectedChildAttributeLink
);

export const selectChildItems = createSelector(
  selectInternalSalesRefundNoteFeature,
  (state) => state.childItems
);

export const selectPricingSchemeHdr = createSelector(
  selectInternalSalesRefundNoteFeature,
  (state) => state.selectedPricingSchemeHdr
);
