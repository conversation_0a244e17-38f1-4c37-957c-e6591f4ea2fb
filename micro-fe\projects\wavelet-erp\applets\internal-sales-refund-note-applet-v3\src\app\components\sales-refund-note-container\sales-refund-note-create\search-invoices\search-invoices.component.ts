import { Component, Input, OnInit, ViewChild } from "@angular/core";
import { MatTabGroup } from "@angular/material/tabs";
import { AppletSettings } from '../../../../models/applet-settings.model';
import { SessionStates } from 'projects/shared-utilities/modules/session/session-controller/states';
import { SessionSelectors } from 'projects/shared-utilities/modules/session/session-controller/selectors';
import { SubSink } from 'subsink2';
import { Store } from '@ngrx/store';
import { combineLatest } from 'rxjs';
import { map } from 'rxjs/operators';
import { InternalSalesRefundNoteSelectors } from "../../../../state-controllers/internal-sales-refund-note-controller/store/selectors";
import { InternalSalesRefundNoteStates } from "../../../../state-controllers/internal-sales-refund-note-controller/store/states";
@Component({
  selector: "app-search-invoices",
  templateUrl: "./search-invoices.component.html",
  styleUrls: ["./search-invoices.component.css"],
})
export class SearchInvoicesComponent implements OnInit {
  protected subs = new SubSink();

  @Input() selectedIndex;
  @Input() orientation: boolean = false;

  @ViewChild(MatTabGroup) matTab: MatTabGroup;

  postingStatus;
  currentDocTypeLinked = null;
  constructor(
    private readonly sessionStore: Store<SessionStates>,
    private readonly store: Store<InternalSalesRefundNoteStates>,
  ) {}

  panels = [
    { title: 'Search By Customer', content: 'search-by-customer' },
    { title: 'Search By Invoice', content: 'search-by-invoice' },
    { title: 'Search By Cashbill', content: 'search-by-cashbill' },
    //{ title: 'Search By Serial Number', content: 'search-by-serial-number' }
  ];
  expandedPanelIndex: number = 0;

  ngOnInit(): void {
    this.subs.sink = this.store.select(InternalSalesRefundNoteSelectors.selectCurrentlySelectedDocumentTypeLinked).subscribe(docType => {
      this.currentDocTypeLinked = docType;
      console.log("this.currentDocType", this.currentDocTypeLinked)
    })
  }

  onPanelOpened(index: number): void {
    this.expandedPanelIndex = index;
  }

  showPanels(): boolean {
    return this.orientation;
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }
}
