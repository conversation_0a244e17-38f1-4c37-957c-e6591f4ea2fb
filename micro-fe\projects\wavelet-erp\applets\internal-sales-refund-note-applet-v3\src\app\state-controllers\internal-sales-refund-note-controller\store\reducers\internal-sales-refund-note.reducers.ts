import { Action, createReducer, on } from '@ngrx/store';
import { InternalSalesRefundNoteActions } from '../actions';
import { initState, InternalSalesRefundNoteState } from '../states/internal-sales-refund-note.states';

export const InternalSalesRefundNoteFeatureKey = 'internalSalesRefundNote';

export const InternalSalesRefundNoteReducer = createReducer(
  initState,
  on(InternalSalesRefundNoteActions.updateKOStatus, (state, action) =>
    ({ ...state, koStatus: action.status })),
  on(InternalSalesRefundNoteActions.loadGroupDiscountItemSuccess, (state, action) =>
    ({ ...state, groupDiscountItem: action.item})),
  on(InternalSalesRefundNoteActions.loadRoundingItemSuccess, (state, action) =>
    ({ ...state, roundingItem: action.item, roundingFiveCent: action.isRounding})),
  on(InternalSalesRefundNoteActions.selectCOA, (state, action) => ({
    ...state, selectedCOA:  action.coa
  })),
  on(InternalSalesRefundNoteActions.loadRoundingItemSuccess, (state, action) =>
    ({ ...state, roundingItem: action.item, roundingFiveCent: action.isRounding})),
    on(InternalSalesRefundNoteActions.selectRoundingItem, (state, action) =>
    ({ ...state, roundingItem: action.item})),
  on(InternalSalesRefundNoteActions.selectEInvoiceEnabled, (state, action) => ({
    ...state, eInvoiceEnabled:  action.val
  })),
  on(InternalSalesRefundNoteActions.loadInternalSalesRefundNoteSuccess, (state, action) =>
    ({ ...state, loadedGenDocs: action.SalesRefundNotes })),

  on(InternalSalesRefundNoteActions.selectEntity, (state, action) =>
    ({ ...state, selectedEntity: action.entity.entity })),

  on(InternalSalesRefundNoteActions.selectEntityOnEdit, (state, action) =>
    ({ ...state, selectedEntity: action.entity.entity })),

  on(InternalSalesRefundNoteActions.selectMember, (state, action) =>
    ({ ...state, selectedMember: action.member })),

  on(InternalSalesRefundNoteActions.selectLineItem, (state, action) =>
    ({ ...state, selectedLineItem: action.lineItem })),

  on(InternalSalesRefundNoteActions.selectSalesRefundNoteForEdit, (state, action) =>
    ({ ...state, selectedSalesRefundNote: action.genDoc })),

  on(InternalSalesRefundNoteActions.selectMode, (state, action) =>
    ({ ...state, selectedMode: action.mode })),

  on(InternalSalesRefundNoteActions.selectSettlement, (state, action) =>
    ({ ...state, selectedSettlement: action.settlement })),

  on(InternalSalesRefundNoteActions.selectAttachmentGuid, (state, action) =>
    ({ ...state, selectedAttachmentGuid: action.guid })),

  on(InternalSalesRefundNoteActions.createInternalSalesRefundNoteSuccess, (state, action) => ({
    ...state, refreshGenDocListing: true,
  })),
  on(InternalSalesRefundNoteActions.editInternalSalesRefundNoteSuccess, (state, action) => ({
    ...state, refreshGenDocListing: true, convertActionDispatched: false
  })),
  on(InternalSalesRefundNoteActions.deleteInternalSalesRefundNoteSuccess, (state, action) => ({
    ...state, refreshGenDocListing: true
  })),
  on(InternalSalesRefundNoteActions.resetAgGrid, (state, action) => ({
    ...state, refreshGenDocListing: false
  })),
  on(InternalSalesRefundNoteActions.selectContraDoc, (state, action) => ({
    ...state,
    selectedContraDoc: action.entity
  })),
  on(InternalSalesRefundNoteActions.selectContraLink, (state, action) => ({
    ...state,
    selectedContraLink: action.link
  })),
  on(InternalSalesRefundNoteActions.printJasperPdfInit, (state, action) => ({
    ...state,
    selectedPrintableFormatGuid: action.guid
  })),
  on(InternalSalesRefundNoteActions.openJasperPdfInit, (state, action) => ({
    ...state,
    selectedPrintableFormatGuid: action.guid
  })),
  on(InternalSalesRefundNoteActions.selectPricingScheme, (state, action) => ({
    ...state,
    selectedPricingScheme: action.pricingScheme
  })),

  on(InternalSalesRefundNoteActions.selectPricingSchemeLinkSuccess, (state, action) =>
  ({
    ...state, pricingSchemeLink: action.pricing
  })),

  on(InternalSalesRefundNoteActions.updatePostingStatusSuccess, (state, action) => ({
    ...state, refreshGenDocListing: true, convertActionDispatched: false
  })),
  on(InternalSalesRefundNoteActions.updateKnockoffListingConfig, (state, action) => ({
    ...state,
    knockoffListingConfig: action.settings
  })),
  on(InternalSalesRefundNoteActions.setEditMode, (state, action) => ({
    ...state, editMode: action.editMode
  })),
  on(InternalSalesRefundNoteActions.selectCustomerForSalesRefundNote, (state, action) => ({
    ...state, selectedCustomerForSalesRefundNote: action.customerGuid
  })),
  on(InternalSalesRefundNoteActions.selectInvoiceForSalesRefundNote, (state, action) => ({
    ...state, selectedInvoiceForSalesRefundNote: action.invoiceGuid
  })),
  on(InternalSalesRefundNoteActions.updateAgGridDone, (state, action) => ({...state, updateContraAgGrid: action.done})),
  on(InternalSalesRefundNoteActions.selectTotalRevenue, (state, action) => ({...state, totalRevenue: action.totalRevenue})),
  on(InternalSalesRefundNoteActions.selectTotalExpense, (state, action) => ({...state, totalExpense: action.totalExpense})),
  on(InternalSalesRefundNoteActions.selectTotalSettlement, (state, action) => ({...state, totalSettlement: action.totalSettlement})),

  on(InternalSalesRefundNoteActions.selectTotalContra, (state, action) => ({...state, totalContra: action.totalContra})),
  on(InternalSalesRefundNoteActions.selectDocOpenAmount, (state, action) => ({...state, docOpenAmount: action.docOpenAmount})),
  on(InternalSalesRefundNoteActions.selectDocArapBalance, (state, action) => ({...state, docArapBalance: action.docArapBalance})),
  on(InternalSalesRefundNoteActions.refreshArapListing, (state, action) => ({...state, refreshArapListing: action.refreshArapListing})),
  on(
    InternalSalesRefundNoteActions.loadArapListingSuccess,
    (state, action) => ({ ...state, loadedArap: action.arapListing })
  ),
  on(InternalSalesRefundNoteActions.discardComplete, (state, action) => ({ ...state, refreshGenDocListing: action.successCount > 0 ? true : false })),
  on(InternalSalesRefundNoteActions.selectGUID, (state, action) => ({
    ...state,
    selectedGuid: action.guid
  })),
  on(InternalSalesRefundNoteActions.resetContra, (state, action) => ({
    ...state,
    totalContraRecords: 0,
    totalContra : 0,
    docOpenAmount : 0,
    docArapBalance : 0,
    loadedArap : null
  })),
  on(InternalSalesRefundNoteActions.voidSalesRefundNoteSuccess, (state, action) => ({
    ...state, refreshGenDocListing: true
  })),
  on(InternalSalesRefundNoteActions.getTotalRecordsSuccess, (state, action) => ({ ...state, totalRecords: action.totalRecords })),
  on(InternalSalesRefundNoteActions.createTempSalesRefundNoteSuccess, (state, action) =>
  ({ ...state, createdTempDoc: action.response, koAttachments:[] })),
  on(InternalSalesRefundNoteActions.convertToActiveSuccess, (state, action) => ({
    ...state, refreshGenDocListing: true, koAttachments: [], convertActionDispatched: true
  })),
  on(InternalSalesRefundNoteActions.resetconvertActionDispatchedState, (state, action) => ({
    ...state, convertActionDispatched: false
  })),
  on(InternalSalesRefundNoteActions.resetDraft, (state) => ({ ...state, convertActionDispatched:false, docLock: false, linkedDocType: null })),
  on(InternalSalesRefundNoteActions.addContra, (state, action) => ({
    ...state, addedContraDoc: action.contraDoc
  })),
  on(InternalSalesRefundNoteActions.setIsEinvoiceSubmissionAnotherCustomer, (state, action) => ({
    ...state, isEinvoiceSubmissionAnotherCustomer: action.isEinvoiceSubmissionAnotherCustomer
  })),
  on(InternalSalesRefundNoteActions.selectSettingItemFilterSuccess, (state, action) =>
    ({...state, selectedItemCategoryFilter:action.setting})),
  on(InternalSalesRefundNoteActions.resetSettingItemFilter, (state, action) =>
    ({...state, selectedItemCategoryFilter:[]})),
  on(InternalSalesRefundNoteActions.resetExpansionPanel, (state, action) => ({
    ...state, resetExpansionPanel: action.resetIndex
  })),
  on(InternalSalesRefundNoteActions.lockDocument, (state, action) => ({
    ...state, docLock: true
  })),
  on(InternalSalesRefundNoteActions.selectRowData, (state, action) =>
    ({...state, rowData: action.rowData})),
  on(InternalSalesRefundNoteActions.selectTotalRecords, (state, action) =>
    ({...state, totalRecords: action.totalRecords})),
  on(InternalSalesRefundNoteActions.selectSearchItemRowData, (state, action) =>
    ({...state, searchItemRowData: action.rowData})),
  on(InternalSalesRefundNoteActions.selectSearchItemTotalRecords, (state, action) =>
    ({...state, searchItemTotalRecords: action.totalRecords})),
  on(InternalSalesRefundNoteActions.selectFirstLoadListing, (state, action) =>
    ({...state, firstLoadListing: action.firstLoadListing})),
  on(InternalSalesRefundNoteActions.addGroupDiscountSuccess, (state, action) => ({
    ...state, groupDiscountPercentage: action.discPercentage
  })),
  on(InternalSalesRefundNoteActions.loadGroupDiscountItemSuccess, (state, action) => ({
    ...state, groupDiscountItem: action.item
  })),
  on(InternalSalesRefundNoteActions.selectSettlementAdjustment, (state, action) => ({
    ...state,
    selectedSettlementAdjustment: action.settlementAdjustment,
  })),
  on(InternalSalesRefundNoteActions.selectAdjustSettlementSuccess, (state, action) => ({...state, settlementMethodAdjustment: action.adjustment})),
  on(InternalSalesRefundNoteActions.selectEditAdjustment, (state, action) => ({...state, isEditAdjustment: action.value})),
  on(InternalSalesRefundNoteActions.selectPricingSchemeGuid, (state, action) => ({
    ...state, pricingSchemeGuid: action.guid
  })),
  on(InternalSalesRefundNoteActions.updateChildItem, (state, action) => {
    const updatedChildItems = state.childItems.map(obj =>
      obj.guid === action.child.guid ? { ...obj, ...action.child } : obj
    );
    return {
      ...state,
      childItems: updatedChildItems
    };
  }),
  on(InternalSalesRefundNoteActions.selectPricingSchemeHdr, (state, action) => ({
    ...state, selectedPricingSchemeHdr: action.pricingSchemeHdr
  })),
  on(InternalSalesRefundNoteActions.selectChildItem, (state, action) => ({
    ...state, childItems: action.child
  })),
  on(InternalSalesRefundNoteActions.setCurrentlySelectedDocumentTypeLinked, (state, action) => ({
    ...state, linkedDocType: action.linkedDocType
  })),
);

export function reducer(state: InternalSalesRefundNoteState | undefined, action: Action) {
  return InternalSalesRefundNoteReducer(state, action);
}
