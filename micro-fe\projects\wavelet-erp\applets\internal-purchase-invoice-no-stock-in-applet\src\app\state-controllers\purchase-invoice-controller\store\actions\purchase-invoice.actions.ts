import { createAction, props } from "@ngrx/store";
import {
  CompanyContainerModel,
  bl_fi_generic_doc_line_RowClass,
  bl_fi_mst_entity_line_RowClass,
  EntityContainerModel,
  GenericDocARAPContainerModel,
  GenericDocContainerModel,
  GenericDocSearchCriteriaDtoModel,
  JsonDatatypeInterface,
  LandedCostsDtoModel,
  Pagination,
  PricingSchemeLinkContainerModel,
  GenericDocumentCloneDTO,
  bl_fi_generic_doc_hdr_RowClass,
  bl_fi_my_einvoice_to_irb_hdr_RowInterface,
  AppletSettingFilterItemCategoryLinkContainerModel,
  BranchContainerModel,
  FinancialItemContainerModel,
} from "blg-akaun-ts-lib";

// export const loadPurchaseInvoiceInit = createAction('[Purchase Invoice] Load Init', props<{ request: any }>());
// export const loadPurchaseInvoiceSuccess = createAction('[Purchase Invoice] Load Success', props<{ totalRecords: number }>());
// export const loadPurchaseInvoiceFailed = createAction('[Purchase Invoice] Load Failed', props<{ error: string }>());

export const voidPurchaseInvoiceInit = createAction(
  "[Purchase Invoice] Void  Init",
  props<{ status: any; doc: GenericDocContainerModel }>()
);
export const voidPurchaseInvoiceSuccess = createAction(
  "[Purchase Invoice] Void  Success",
  props<{ doc: any }>()
);
export const voidPurchaseInvoiceFailed = createAction(
  "[Purchase Invoice] Void  Failed",
  props<{ error: string }>()
);

export const loadPurchaseInvoiceInit = createAction(
  "[Purchase Invoice] Load Init",
  props<{ pagination: Pagination }>()
);
export const loadPurchaseInvoiceSuccess = createAction(
  "[Purchase Invoice] Load Success",
  props<{
    purchaseInvoice: GenericDocContainerModel[];
    snapshotGuid?: string;
  }>()
);
export const loadPurchaseInvoiceFailed = createAction(
  "[Purchase Invoice] Load Failed",
  props<{ error: string }>()
);

export const createPurchaseInvoiceInit = createAction(
  "[Purchase Invoice] Create Init"
);
export const createPurchaseInvoiceSuccess = createAction(
  "[Purchase Invoice] Create Success",
  props<{ hdrGuid: string }>()
);
export const createPurchaseInvoiceFailed = createAction(
  "[Purchase Invoice] Create Failed",
  props<{ error: string }>()
);

export const editPurchaseInvoiceInit = createAction(
  "[Purchase Invoice] Edit Init"
);
export const editPurchaseInvoiceSuccess = createAction(
  "[Purchase Invoice] Edit Success",
  props<{ hdrGuid: string }>()
);
export const editPurchaseInvoiceFailed = createAction(
  "[Purchase Invoice] Edit Failed",
  props<{ error: string }>()
);

export const deletePurchaseInvoiceInit = createAction(
  "[Purchase Invoice] Delete Init"
);
export const deletePurchaseInvoiceSuccess = createAction(
  "[Purchase Invoice] Delete Success"
);
export const deletePurchaseInvoiceFailed = createAction(
  "[Purchase Invoice] Delete Failed",
  props<{ error: string }>()
);

export const selectPurchaseInvoice = createAction(
  "[Purchase Invoice] Select Purchase Invoice Init",
  props<{ genDoc: GenericDocContainerModel }>()
);

export const selectPurchaser = createAction(
  "[Purchase Invoice] Select Purchaser",
  props<{ guid: string; name: string }>()
);

export const selectEntity = createAction(
  "[Purchase Invoice] Select Entity",
  props<{
    entity: {
      entity: EntityContainerModel;
      contact: bl_fi_mst_entity_line_RowClass;
    };
  }>()
);
export const selectEntityBranch = createAction(
  "[Purchase Invoice] Select Entity Branch",
  props<{
    entityBranch: any, entity?: EntityContainerModel
  }>()
);
export const selectEntityOnEdit = createAction(
  "[Purchase Invoice] Select Entity On Edit",
  props<{
    entity: {
      entity: EntityContainerModel;
      contact: bl_fi_mst_entity_line_RowClass;
    };
  }>()
);
export const selectBillingAddress = createAction(
  "[Purchase Invoice] Select Billing Address",
  props<{ billing_address: JsonDatatypeInterface }>()
);
export const selectShippingAddress = createAction(
  "[Purchase Invoice] Select Shipping Address",
  props<{ shipping_address: JsonDatatypeInterface }>()
);

export const selectLineItem = createAction(
  "[Purchase Invoice] Select Line Item",
  props<{ lineItem: bl_fi_generic_doc_line_RowClass }>()
);

export const selectPricingScheme = createAction(
  "[Purchase Invoice] Select Pricing Scheme",
  props<{ pricingScheme: any }>()
);
export const addPricingSchemeLinkInit = createAction(
  "[Purchase Invoice] Add Pricing Scheme Link Init",
  props<{ link: PricingSchemeLinkContainerModel }>()
);
export const addPricingSchemeLinkSuccess = createAction(
  "[Purchase Invoice] Add Pricing Scheme Link Success"
);
export const addPricingSchemeLinkFailed = createAction(
  "[Purchase Invoice] Add Pricing Scheme Link Failed",
  props<{ error: string }>()
);
export const editPricingSchemeLinkInit = createAction(
  "[Purchase Invoice] Edit Pricing Scheme Link Init",
  props<{ link: PricingSchemeLinkContainerModel }>()
);
export const editPricingSchemeLinkSuccess = createAction(
  "[Purchase Invoice] Edit Pricing Scheme Link Success"
);
export const editPricingSchemeLinkFailed = createAction(
  "[Purchase Invoice] Edit Pricing Scheme Link Failed",
  props<{ error: string }>()
);

export const selectPricingSchemeLink = createAction(
  "[Purchase Invoice] Select Pricing Scheme Link",
  props<{ item: any }>()
);
export const selectPricingSchemeLinkSuccess = createAction(
  "[Purchase Invoice] Select Pricing Link Scheme Success",
  props<{ pricing: PricingSchemeLinkContainerModel[] }>()
);
export const selectPricingSchemeLinkFailed = createAction(
  "[Purchase Invoice] Select Pricing Link Scheme Failed",
  props<{ error: string }>()
);

export const selectPayment = createAction(
  "[Purchase Invoice] Select Payment",
  props<{ payment: bl_fi_generic_doc_line_RowClass }>()
);

export const selectContraDoc = createAction(
  "[Purchase Invoice] Select Contra Doc",
  props<{ entity: GenericDocContainerModel }>()
);
export const selectContraLink = createAction(
  "[Purchase Invoice] Select Contra Link",
  props<{ link: GenericDocARAPContainerModel }>()
);

export const selectAttachmentGuid = createAction(
  "[Purchase Invoice] Select Attachment",
  props<{ guid: string }>()
);

export const selectMode = createAction(
  "[Purchase Invoice] Select Purchase Invoice View Mode",
  props<{ mode: string }>()
);

export const editGenLineItemInit = createAction(
  "[Purchase Invoice] Edit Generic Doc Line Item Init"
);
export const editGenLineItemSuccess = createAction(
  "[Purchase Invoice] Edit Generic Doc Line Item Success"
);
export const editGenLineItemFailed = createAction(
  "[Purchase Invoice] Edit Generic Doc Line Item Failed",
  props<{ error: string }>()
);

export const printJasperPdfInit = createAction(
  "[Purchase Invoice] Print Jasper Pdf Init",
  props<{ guid: string }>()
);
export const printJasperPdfSuccess = createAction(
  "[Purchase Invoice] Print Jasper Pdf Success"
);
export const printJasperPdfFailed = createAction(
  "[Purchase Invoice] Print Jasper Pdf Failed"
);

export const printMultipleJasperPdfInit = createAction(
  "[Purchase Invoice] Print Multiple Jasper Pdf Init",
  props<{ guids: string[]; printable: string, preview: boolean, docNumbers: string[] }>()
);
export const printMultipleJasperPdfSuccess = createAction(
  "[Purchase Invoice] Print Multiple Jasper Pdf Success"
);
export const printMultipleJasperPdfFailed = createAction(
  "[Purchase Invoice] Print Multiple Jasper Pdf Failed"
);

export const printEInvoiceJasperPdfInit = createAction('[Purchase Invoice] Print E-Invoice Jasper Pdf Init', props<{ hdr: string }>());
export const printEInvoiceJasperPdfSuccess = createAction('[Purchase Invoice] Print E-Invoice Jasper Pdf Success');
export const printEInvoiceJasperPdfFailed = createAction('[Purchase Invoice] Print E-Invoice Jasper Pdf Failed');

export const resetAgGrid = createAction(
  "[Purchase Invoice] Reset Ag Grid Update"
);

export const resetPurchaseInvoice = createAction(
  "[Purchase Invoice] Reset Purchase Invoice"
);

export const addContra = createAction(
  "[Purchase Invoice] Add Contra",
  props<{ contraDoc: GenericDocARAPContainerModel }>()
);
export const addContraInit = createAction("[Purchase Invoice] Add Contra Init");
export const addContraSuccess = createAction(
  "[Purchase Invoice] Add Contra Success"
);
export const addContraFailed = createAction(
  "[Purchase Invoice] Add Contra Failed",
  props<{ error: string }>()
);

export const editInternalPurchaseInvoiceBeforeContraInit = createAction(
  "[Internal Purchase Invoice No Stock In] Edit Before Contra Init"
);
export const editInternalPurchaseInvoiceBeforeContraSuccess = createAction(
  "[Internal Purchase Invoice No Stock In] Edit Before Contra Success"
);
export const editInternalPurchaseInvoiceBeforeContraFailed = createAction(
  "[Internal Purchase Invoice No Stock In] Edit Before Contra Failed",
  props<{ error: string }>()
);

export const deleteContraInit = createAction(
  "[Purchase Invoice] Delete Contra Init"
);
export const deleteContraSuccess = createAction(
  "[Purchase Invoice] Delete Contra Success"
);
export const deleteContraFailed = createAction(
  "[Purchase Invoice] Delete Contra Failed",
  props<{ error: string }>()
);

export const updatePostingStatus = createAction(
  "[Purchase Invoice] Update Posting Status",
  props<{ status: any; doc: GenericDocContainerModel }>()
);
export const updatePostingStatusSuccess = createAction(
  "[Purchase Invoice] Update Posting Status Success",
  props<{ doc: GenericDocContainerModel }>()
);
export const updatePostingStatusFailed = createAction(
  "[Purchase Invoice] Update Posting Status Failed",
  props<{ error: string }>()
);

export const updateKnockoffListingConfig = createAction(
  "[Purchase Invoice] Update Knockoff Listing Config",
  props<{ settings: any }>()
);

export const selectCompanyGuid = createAction(
  "[Purchase Invoice] Select Company Guid",
  props<{ compGuid: string }>()
);

export const createPurchaseInvoiceGenDocLinkSuccess = createAction(
  "[Purchase Invoice] Create Gen Doc Link Success"
);
export const createPurchaseInvoiceGenDocLinkFailed = createAction(
  "[Purchase Invoice] Create Gen Doc Link Failed",
  props<{ error: string }>()
);
export const editPurchaseInvoiceGenDocLinkSuccess = createAction(
  "[Purchase Invoice] Edit Gen Doc Link Success"
);
export const editPurchaseInvoiceGenDocLinkFailed = createAction(
  "[Purchase Invoice] Edit Gen Doc Link Failed",
  props<{ error: string }>()
);
export const setEditMode = createAction(
  "[Purchase Invoice] Set Edit Mode",
  props<{ editMode: boolean }>()
);

export const editMode = createAction(
  "[Purchase Invoice] Edit Mode",
  props<{ editMode: boolean }>()
);

export const landedCostsAllocationInit = createAction(
  "[Purchase Invoice] Laneded Costs Allocation Init",
  props<{ landnedCosts: LandedCostsDtoModel[] }>()
);
export const landedCostsAllocationSuccess = createAction(
  "[Purchase Invoice] Laneded Costs Allocation Success"
);
export const landedCostsAllocationFailed = createAction(
  "[Purchase Invoice] Laneded Costs Allocation Failed",
  props<{ error: string }>()
);

export const searchPurchaseInvoiceInit = createAction(
  "[Internal Purchase Invoice No Stock In] Search Init",
  props<{ searchDto: GenericDocSearchCriteriaDtoModel }>()
);
export const selectGUID = createAction(
  "[Internal Purchase Invoice No Stock In] Select GUID",
  props<{ guid: string }>()
);

export const loadIntercompanySalesInvoiceInit = createAction(
  "[Purchase Invoice] Load Intercompany Purchase Invoice Init",
  props<{ pagination: Pagination }>()
);
export const loadIntercompanySalesInvoiceSuccess = createAction(
  "[Purchase Invoice] Load Intercompany Purchase Invoice Success",
  props<{ salesInvoices: GenericDocContainerModel[]; snapshotGuid?: string }>()
);
export const loadIntercompanySalesInvoiceFailed = createAction(
  "[Purchase Invoice] Load Intercompany Purchase Invoice Failed",
  props<{ error: string }>()
);

export const discardInit = createAction(
  "[Purchase Invoice] Discard Init",
  props<{ guids: string[]; fromEdit?: boolean }>()
);
export const discardComplete = createAction(
  "[Purchase Invoice] Discard Complete",
  props<{ total: number; successCount: number; failureCount: number }>()
);
export const discardFailure = createAction(
  "[Purchase Invoice] Discard Failure",
  props<{ error: any }>()
);

export const createIntercompanyTransactions = createAction(
  "[Purchase Invoice] Create Intercompany Transactions",
  props<{ payload: any }>()
);
export const createIntercompanyTransactionsSuccess = createAction(
  "[Purchase Invoice] Create Intercompany Transactions Success",
  props<{ response: any }>()
);
export const createIntercompanyTransactionsFailed = createAction(
  "[Purchase Invoice] Create Intercompany Transactions Failed",
  props<{ error: string }>()
);

export const selectInvItem = createAction(
  "[Purchase Invoice] Select Inventory Item",
  props<{ invItem }>()
);

export const refreshArapListing = createAction(
  "[Contra] Refresh Listing",
  props<{ refreshArapListing: boolean }>()
);
export const loadArapListingInit = createAction(
  "[Contra] Load Contra Init",
  props<{ pagination: Pagination }>()
);
export const loadArapListingSuccess = createAction(
  "[Contra] Load Contra Success",
  props<{ arapListing: GenericDocARAPContainerModel[] }>()
);
export const loadArapListingFailed = createAction(
  "[Contra] Load Contra Failed",
  props<{ error: string }>()
);

export const loadContraInit = createAction(
  "[Contra] Load Init",
  props<{ request: any }>()
);
export const loadContraSuccess = createAction(
  "[Contra] Load Success",
  props<{ totalRecords: number }>()
);
export const loadContraFailure = createAction(
  "[Contra] Load Failure",
  props<{ error: string }>()
);

export const updateAgGridDone = createAction(
  "[Contra] Update AG Grid Done",
  props<{ done: boolean }>()
);

export const selectTotalRevenue = createAction(
  "[Contra] Select Total Revenue",
  props<{ totalRevenue: any }>()
);
export const selectTotalExpense = createAction(
  "[Contra] Select Total Expense",
  props<{ totalExpense: any }>()
);
export const selectTotalSettlement = createAction(
  "[Contra] Select Total Settlement",
  props<{ totalSettlement: any }>()
);
export const selectTotalContra = createAction(
  "[Contra] Select Contra Amount",
  props<{ totalContra: any }>()
);
export const selectDocOpenAmount = createAction(
  "[Contra] Select Doc Open Amount",
  props<{ docOpenAmount: any }>()
);
export const selectDocArapBalance = createAction(
  "[Contra] Select Doc ARAP Balance",
  props<{ docArapBalance: any }>()
);

export const updateAfterContra = createAction(
  "[Contra] Update Gen Doc After Contra",
  props<{ genDoc: GenericDocContainerModel }>()
);
export const updateAfterContraFailed = createAction(
  "[Contra] Update Gen Doc After Contra Failed",
  props<{ error: string }>()
);

export const resetContra = createAction("[Contra] Reset Contra");

export const disableCreate = createAction(
  "[Purchase Invoice] Disable Create",
  props<{ disable: boolean }>()
);

export const editedInvoice = createAction(
  "[Purchase Invoice] Edited Purchase Invoice",
  props<{ edited: boolean }>()
);

export const setKOAttachments = createAction(
  "[Purchase Invoice] KO Attachments",
  props<{ attachments: any }>()
);

export const setDelimeter = createAction(
  "[Purchase Invoice] Set Delimeter ",
  props<{ delimeter: any }>()
);

export const getTotalRecords = createAction(
  "[Purchase Invoice] Get Total Records Init",
  props<{ searchDto: GenericDocSearchCriteriaDtoModel }>()
);
export const getTotalRecordsSuccess = createAction(
  "[Purchase Invoice] Get Total Records Success",
  props<{ totalRecords: number }>()
);
export const getTotalRecordsFailed = createAction(
  "[Purchase Invoice] Get Total Records Failed",
  props<{ error: string }>()
);

export const createTempPurchaseInvoiceInit = createAction(
  "[Internal Purchase Invoice No Stock In] Create Temp SR Init",
  props<{ doc: GenericDocContainerModel }>()
);
export const createTempPurchaseInvoiceSuccess = createAction(
  "[Internal Purchase Invoice No Stock In] Create Temp SR Success",
  props<{ response: GenericDocContainerModel }>()
);
export const createTempPurchaseInvoiceFailed = createAction(
  "[Internal Purchase Invoice No Stock In] Create Temp SR Failed",
  props<{ error: string }>()
);

export const convertToActiveInit = createAction(
  "[Internal Purchase Invoice No Stock In] Convert to Active Init"
);
export const convertToActiveSuccess = createAction(
  "[Internal Purchase Invoice No Stock In]  Convert to Active Success",
  props<{ hdrGuid: string }>()
);
export const convertToActiveFailed = createAction(
  "[Internal Purchase Invoice No Stock In] Convert to Active Failed",
  props<{ error: string }>()
);
export const resetconvertActionDispatchedState = createAction(
  "[Internal Purchase Invoice No Stock In] Reset Convert Action Dispatched State"
);

export const editPurchaseInvoiceFinalInit = createAction(
  "[Internal Purchase Invoice No Stock In] Edit and Final Init"
);
export const editPurchaseInvoiceFinalSuccess = createAction(
  "[Internal Purchase Invoice No Stock In] Edit and Final Success",
  props<{ status: any; doc: GenericDocContainerModel, hdrGuid?:string }>()
);
export const editPurchaseInvoiceFinalFailed = createAction(
  "[Internal Purchase Invoice No Stock In] Edit and Final Failed",
  props<{ error: string }>()
);
export const resetDraft = createAction(
  "[Internal Purchase Invoice No Stock In] Reset Internal Purchase Invoice No Stock In"
);

export const updateContraInit = createAction(
  "[Internal Purchase Invoice No Stock In] Save Contra Init",
  props<{ txn_date: string }>()
);
export const updateContraSuccess = createAction(
"[Internal Purchase Invoice No Stock In] Save Contra Success",
// props<{ txn_date: string }>()
);
export const updateContraFailed = createAction(
"[Internal Purchase Invoice No Stock In]  Save Contra Failed",
props<{ error: string }>()
);

export const initUpdateToSelfBilledInvoice = createAction(
  "[Internal Purchase Invoice No Stock In]  Update to Self Billed Invoice Init",
  props<{ selectedGenDocs: any[] }>()
);

export const updateToSelfBilledInvoiceSuccess = createAction(
  "[Internal Purchase Invoice No Stock In] Update to Self Billed Invoice Success"
  );
  export const updateToSelfBilledFailed = createAction(
  "[Internal Purchase Invoice No Stock In]  Update to Self Billed Invoice Failed",
  props<{ error: string }>()
);

export const cloneDocumentInit = createAction("[Internal Purchase Invoice No Stock In:Edit] Clone Document Init", props<{
  /** false by default */
  includeAttachments?: boolean
}>());
export const cloneDocumentSuccess = createAction("[Internal Purchase Invoice No Stock In:Edit] Clone Document Success", props<{ dto: GenericDocumentCloneDTO }>());
export const cloneDocumentFail = createAction("[Internal Purchase Invoice No Stock In:Edit] Clone Document Fail", props<{ error: any }>());

export const cloneDocumentFromSearchInit = createAction("[Purchase Invoice:Edit] Clone Document From Search Init", props<{
  /** false by default */
  guid:string,
  includeAttachments?: boolean
}>());
export const cloneDocumentFromSearchSuccess = createAction("[Purchase Invoice:Edit] Clone Document From Search Success", props<{ dto: GenericDocumentCloneDTO }>());
export const cloneDocumentFromSearchFail = createAction("[Purchase Invoice:Edit] Clone Document From Search Fail", props<{ error: any }>());

export const pollClonedDocumentInit = createAction("[Internal Purchase Invoice No Stock In:Edit] Poll Cloned Document Init", props<{ guid: string, currentRequestCount: number, maxRetries: number }>());
export const pollClonedDocumentSuccess = createAction("[Internal Purchase Invoice No Stock In:Edit] Poll Cloned Document Success", props<{ payload: GenericDocContainerModel, currentRequestCount: number, maxRetries: number }>());
export const pollClonedDocumentFail = createAction("[Internal Purchase Invoice No Stock In:Edit] Poll Cloned Document Fail", props<{ guid: string, currentRequestCount: number, maxRetries: number, error: any }>());

export const loadCloneSourceGenDocHdrInit = createAction("[Internal Purchase Invoice No Stock In:Edit] Load Clone Source Gen Doc Init", props<{ guid: string }>());
export const loadCloneSourceGenDocHdrSuccess = createAction("[Internal Purchase Invoice No Stock In:Edit] Load Clone Source Gen Doc Success", props<{ hdr: bl_fi_generic_doc_hdr_RowClass }>());
export const loadCloneSourceGenDocHdrFail = createAction("[Internal Purchase Invoice No Stock In:Edit] Load Clone Source Gen Doc Fail", props<{ error: any }>());

export const getCompanyEinvoiceDetails = createAction('[Internal Purchase Invoice No Stock In] Company Einvoice Details', props<{ compGuid: any}>());
export const getCompanyEinvoiceDetailsSuccess = createAction('[Internal Purchase Invoice No Stock In] Company Einvoice Details Success', props<{ company: any}>());
export const getCompanyEinvoiceDetailsFailed = createAction('[Internal Purchase Invoice No Stock In] Company Einvoice Details Failed', props<{ error: any}>());
export const resetSelectedCompany = createAction('[Internal Purchase Invoice No Stock In] Reset Selected Company');

export const selectEInvoiceEnabled = createAction('[Internal Purchase Invoice No Stock In] Select E-invoice enabled', props<{ val: boolean}>());
export const selectDocLink = createAction(
  '[Purchase Invoice] Select Doc Link', props<{ docLink: any }>()
);

export const selectPnsLevel2 = createAction('[Purchase Invoice] Select Pns Level 2', props<{ pnsLevel2: any[] }>()
);

export const updateEInvoiceDetails = createAction('Internal Purchase Invoice No Stock In] Update E-Invoice Details', props<{ form: any }>());
export const updateSingleGeneralDetails = createAction('[Internal Purchase Invoice No Stock In] Update Single General Details', props<{ form: any }>());

export const setIsEinvoiceSubmissionAnotherSupplier = createAction('[E-Invoice] Is E-Invoice Another customer', props<{ isEinvoiceSubmissionAnotherSupplier: boolean}>());
export const setEinvoiceSubmissionAnotherSupplierDetails = createAction('[E-Invoice] Set E-Invoice Another customer details', props<{ einvoiceSubmissionAnotherSupplierDetails: any}>());

export const selectSegment = createAction('[Internal Purchase Invoice No Stock In] Select Segment', props<{ segment: any}>());
export const selectProject = createAction('[Internal Purchase Invoice No Stock In] Select Project', props<{ project: any}>());
export const selectProfitCenter = createAction('[Internal Purchase Invoice No Stock In] Select Profit Center', props<{ profitCenter: any}>());
export const selectDimension = createAction('[Internal Purchase Invoice No Stock In] Select Dimension', props<{ dimension: any}>());
export const selectSegmentLine = createAction('[Internal Purchase Invoice No Stock In] Select Segment Line', props<{ segment: any}>());
export const selectProjectLine = createAction('[Internal Purchase Invoice No Stock In] Select Project Line', props<{ project: any}>());
export const selectProfitCenterLine = createAction('[Internal Purchase Invoice No Stock In] Select Profit Center Line', props<{ profitCenter: any}>());
export const selectDimensionLine = createAction('[Internal Purchase Invoice No Stock In] Select Dimension Line', props<{ dimension: any}>());

export const loadSegment = createAction('[Internal Purchase Invoice No Stock In] Load Segment', props<{ guid: any}>());
export const loadSegmentSuccess = createAction('[Internal Purchase Invoice No Stock In]  Load Segment Success', props<{segment: any}>());
export const loadSegmentFailed = createAction('[Internal Purchase Invoice No Stock In] Load Segment Failed', props<{error: string}>());
export const loadProject = createAction('[Internal Purchase Invoice No Stock In] Load Project', props<{ guid: any}>());
export const loadProjectSuccess = createAction('[Internal Purchase Invoice No Stock In]  Load Project Success', props<{project: any}>());
export const loadProjectFailed = createAction('[Internal Purchase Invoice No Stock In] Load Project Failed', props<{error: string}>());
export const loadProfitCenter = createAction('[Internal Purchase Invoice No Stock In] Load Profit Center', props<{ guid: any}>());
export const loadProfitCenterSuccess = createAction('[Internal Purchase Invoice No Stock In]  Load ProfitCenter Success', props<{profitCenter: any}>());
export const loadProfitCenterFailed = createAction('[Internal Purchase Invoice No Stock In] Load ProfitCenter Failed', props<{error: string}>());
export const loadDimension = createAction('[Internal Purchase Invoice No Stock In] Load Dimension', props<{ guid: any}>());
export const loadDimensionSuccess = createAction('[Internal Purchase Invoice No Stock In]  Load Dimension Success', props<{dimension: any}>());
export const loadDimensionFailed = createAction('[Internal Purchase Invoice No Stock In] Load Dimension Failed', props<{error: string}>());

export const selectCOA = createAction('[Internal Purchase Invoice No Stock In] Select COA', props<{ coa: any}>());
export const selectDepartmentMode = createAction('[Internal Purchase Invoice No Stock In] Select Department Mode', props<{ mode: any}>());
export const selectCopyDepartmentFromHdr = createAction('[Internal Purchase Invoice No Stock In] Select Copy Department From Hdr', props<{ value: any}>());

export const loadSegmentLine = createAction('[Internal Purchase Invoice No Stock In] Load Segment Line', props<{ guid: any}>());
export const loadSegmentLineSuccess = createAction('[Internal Purchase Invoice No Stock In]  Load Segment Success Line', props<{segment: any}>());
export const loadSegmentLineFailed = createAction('[Internal Purchase Invoice No Stock In] Load Segment Failed Line', props<{error: string}>());
export const loadProjectLine = createAction('[Internal Purchase Invoice No Stock In] Load Project Line', props<{ guid: any}>());
export const loadProjectLineSuccess = createAction('[Internal Purchase Invoice No Stock In]  Load Project Line Success', props<{project: any}>());
export const loadProjectLineFailed = createAction('[Internal Purchase Invoice No Stock In] Load Project Line Failed', props<{error: string}>());
export const loadProfitCenterLine = createAction('[Internal Purchase Invoice No Stock In] Load Profit Center Line', props<{ guid: any}>());
export const loadProfitCenterLineSuccess = createAction('[Internal Purchase Invoice No Stock In]  Load ProfitCenter Line Success', props<{profitCenter: any}>());
export const loadProfitCenterLineFailed = createAction('[Internal Purchase Invoice No Stock In] Load ProfitCenter Line Failed', props<{error: string}>());
export const loadDimensionLine = createAction('[Internal Purchase Invoice No Stock In] Load Dimension Line', props<{ guid: any}>());
export const loadDimensionLineSuccess = createAction('[Internal Purchase Invoice No Stock In]  Load Dimension Line Success', props<{dimension: any}>());
export const loadDimensionLineFailed = createAction('[Internal Purchase Invoice No Stock In] Load Dimension Line Failed', props<{error: string}>());

export const selectCopyDepartmentFromHdrFromEditLine = createAction('[Internal Purchase Invoice No Stock In] Get Copy Department From Hdr', props<{ item: bl_fi_generic_doc_line_RowClass}>());

export const resetExpansionPanel = createAction('[Internal Purchase Invoice No Stock In] Reset Expansion Panel Index', props<{ resetIndex: boolean }>());

export const selectChildItem = createAction('[Internal Purchase GRN] Select Child Item', props<{ child: any[] }>());
export const updateChildItem = createAction('[Internal Purchase GRN] Update Child Item', props<{ child: any }>());
export const selectChildItemPricingLink = createAction('[Internal Purchase GRN] Select Child Item Pricing Link', props<{ child: any }>());
export const selectChildItemPricingLinkSuccess = createAction('[Internal Purchase GRN] Select Child Item Pricing Link Success', props<{ price: any }>());
export const selectChildItemPricingLinkFailed = createAction('[Internal Purchase GRN] Select Child Item Pricing Link Failed', props<{ error: any }>());
export const selectPricingSchemeHdr = createAction('[Internal Purchase GRN] Select Pricing Scheme Hdr', props<{ pricingSchemeHdr: any }>());

export const selectSettingItemFilter = createAction('[Internal Purchase Invoice No Stock In] select Settings Item Filter', props<{branch: any}>());
export const selectSettingItemFilterSuccess = createAction('[Internal Purchase Invoice No Stock In] select Settings Item Filter Success', props<{setting: AppletSettingFilterItemCategoryLinkContainerModel[]}>());
export const selectSettingItemFilterFailed = createAction('[Internal Purchase Invoice No Stock In] select Settings Item Filter Failed', props<{error: string}>());

export const resetSettingItemFilter = createAction('[Internal Purchase Invoice No Stock In] Reset Selected Settings Item Filter');

export const saveSettingItemFilter = createAction('[Internal Purchase Invoice No Stock In] Save Settings Item Filter', props<{form: any, branch: any}>());
export const saveSettingItemFilterSuccess = createAction('[Internal Purchase Invoice No Stock In] Save Settings Item Filter Success');
export const saveSettingItemFilterFailed = createAction('[Internal Purchase Invoice No Stock In] Save Settings Item Filter Failed', props<{error: string}>());

export const loadSettingItemFilter = createAction('[Internal Purchase Invoice No Stock In] load Settings Item Filter');
export const loadSettingItemFilterSuccess = createAction('[Internal Purchase Invoice No Stock In] load Settings Item Filter Success', props<{setting: AppletSettingFilterItemCategoryLinkContainerModel[]}>());
export const loadSettingItemFilterFailed = createAction('[Internal Purchase Invoice No Stock In] load Settings Item Filter Failed', props<{error: string}>());

export const loadBranchCompany = createAction('[Internal Purchase Invoice No Stock In]  Load Branch Company', props<{ compGuid: any,branchGuid:any }>());
export const loadBranchCompanySuccess = createAction('[Internal Purchase Invoice No Stock In]  Load Branch Company Success', props<{ branch: any,company:any }>());
export const loadBranchCompanyFailed = createAction('[Internal Purchase Invoice No Stock In]  Load Branch Company Failed', props<{err: String}>());

export const lockDocument = createAction('[Purchase Invoice] Lock Gen Doc');
export const unlockDocument = createAction('[Purchase Invoice] Unlock Gen Doc', props<{hdrGuid:string}>());
export const unlockDocumentSuccess = createAction('[Purchase Invoice] Unlock Gen Doc Success');
export const unlockDocumentFailed = createAction('[Purchase Invoice] Unlock Gen Doc Failed', props<{error: string}>());

export const selectRowData = createAction('[Purchase Invoice] Select Row Data', props<{rowData: []}>());
export const selectTotalRecords = createAction('[Purchase Invoice] Select Total Records', props<{totalRecords: number }>());
export const selectSearchItemRowData = createAction('[Purchase Invoice] Select Search Item Row Data', props<{rowData: []}>());
export const selectSearchItemTotalRecords = createAction('[Purchase Invoice] Select Search Item Total Records', props<{totalRecords: number }>());
export const selectFirstLoadListing = createAction('[Purchase Invoice] Select First Load Listing', props<{firstLoadListing: boolean }>());

export const resetKOForVertical = createAction('[Purchase Invoice] Reset KO For Vertical UI', props<{reset: boolean}>());

export const einvoiceNtfQueueProcessInit = createAction('[Purchase Invoice] Notification Queue Process Init', props<{ dto: any}>());
export const einvoiceNtfQueueProcessSuccess = createAction('[Purchase Invoice] Notification Queue Process Success');
export const einvoiceNtfQueueProcessFailed = createAction('[Purchase Invoice] Notification Queue Process Failed', props<{error: string}>());

export const createPushBackToQueuesInit = createAction('[Purchase Invoice] Push Back To Matched History Init', props<{ dto: any}>());
export const createPushBackToQueuesSuccess = createAction('[Purchase Invoice] Push Back To Matched History Success');
export const createPushBackToQueuesFailed = createAction('[Purchase Invoice] Push Back To Matched History Failed', props<{ error: string }>());

export const loadMatchedHistoryInit = createAction('[Purchase Invoice] Matched History Load Init', props<{ request: any }>());
export const loadMatchedHistorySuccess = createAction('[Purchase Invoice] Matched History Load Success', props<{ totalRecords: number }>());
export const loadMatchedHistoryFailure = createAction('[Purchase Invoice] Matched History Load Failure', props<{error: string}>());

export const updateMatchedHistoryAgGridDone = createAction('[Purchase Invoice] Matched History Update AG Grid Done', props<{done: boolean}>() );

export const selectDocumentForKO = createAction(
  "[Purchase Invoice] Select Document For KO",
  props<{ docGuid: string }>()
);

export const selectSearchChildItemRowData = createAction('[Purchase Invoice] Select Search Child Item Row Data', props<{rowData: []}>());
export const selectSearchChildItemTotalRecords = createAction('[Purchase Invoice] Select Search Child Item Total Records', props<{totalRecords: number }>());

export const knockOffDocument = createAction('[Purchase Invoice] Knock Off Document', props<{link: any }>());
export const knockOffDocumentSuccess = createAction("[Purchase Invoice] Knock Off Document Success");
export const knockOffDocumentFail = createAction("[Purchase Invoice] Knock Off Document Fail", props<{ error: any }>());

export const selectPricingSchemeGuid = createAction('[Purchase Invoice] Select Pricing Scheme Guid', props<{ guid: any }>());
export const selectTradeInEnabled = createAction('[Purchase Invoice] Select Trade-In enabled', props<{ isTradeIn: boolean}>());
export const selectItemType = createAction('[Purchase Invoice] Select Item Type', props<{ itemTxnType: string}>());

export const autoPopUpPrintableSuccess = createAction('[Purchase Invoice] Auto Pop Up Printable Success');
export const autoPopUpPrintableFailed = createAction('[Purchase Invoice] Auto Pop Up Printable Failed', props<{ error: any }>());

export const editGenDocLinkFinalSuccess = createAction('[Purchase Invoice] Edit Gen Doc Link Final Success', props<{ status?: any; doc?: GenericDocContainerModel }>());
export const editGenDocLinkFinalFailed = createAction('[Purchase Invoice] Edit Gen Doc Link Final Failed', props<{ error: string }>());

export const calculateRounding = createAction('[Purchase Invoice]  Calculate Rounding');
export const calculateRoundingSuccess = createAction('[Purchase Invoice]  Calculate Rounding Success');
export const calculateRoundingFailed = createAction('[Purchase Invoice]  Calculate Rounding Failed');
export const selectRoundingItem = createAction('[Purchase Invoice]  Select Rounding Item', props<{ item: FinancialItemContainerModel }>());

export const loadRoundingItemSuccess = createAction('[Purchase Invoice]  Load Rounding Item Success', props<{ item: FinancialItemContainerModel,isRounding: boolean  }>());
export const loadRoundingItemFailed = createAction('[Purchase Invoice]  Load Rounding Item Failed', props<{err: String}>());
export const resetRoundingGroupDiscountItem = createAction('[Purchase Invoice]  Reset Rounding Group Discount Item');
export const loadGroupDiscountItemSuccess = createAction('[Purchase Invoice]  Load Group Discount Item Success', props<{ item: FinancialItemContainerModel }>());
export const loadGroupDiscountItemFailed = createAction('[Purchase Invoice]  Load Group Discount Item Failed', props<{err: String}>());
export const addGroupDiscount = createAction('[Purchase Invoice]  Add Group Discount', props<{discAmount: number, discPercentage: number}>());
export const addGroupDiscountSuccess = createAction('[Purchase Invoice]  Add Group Discount Success', props<{discPercentage: number}>());
export const addGroupDiscountFailed = createAction('[Purchase Invoice]  Add Group Discount Failed');
export const recalculateGroupDiscount = createAction('[Purchase Invoice]  Recalculate Group Discount');
export const recalculateGroupDiscountSuccess = createAction('[Purchase Invoice]  Recalculate Group Discount Success');
export const recalculateGroupDiscountFailed = createAction('[Purchase Invoice]  Recalculate Group Discount Failed');