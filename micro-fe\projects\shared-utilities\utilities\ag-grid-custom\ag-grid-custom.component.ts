// Angular Core
import { Component, EventEmitter, Input, OnInit, Output, ViewChild } from '@angular/core';

// NgRx Store
import { Store } from "@ngrx/store";

// AG Grid
import { AgGridAngular } from 'ag-grid-angular';
import { GridOptions } from 'ag-grid-enterprise';

// Shared Utilities - Session
import { SessionActions } from "projects/shared-utilities/modules/session/session-controller/actions";
import { SessionSelectors } from "projects/shared-utilities/modules/session/session-controller/selectors";
import { SessionStates } from "projects/shared-utilities/modules/session/session-controller/states";

// Shared Utilities - Permissions
import { ClientSidePermissionsSelectors } from 'projects/shared-utilities/modules/permission/client-side-permissions-controller/selectors';
import { ClientSidePermissionStates } from 'projects/shared-utilities/modules/permission/client-side-permissions-controller/states';

// Shared Utilities - Status Bar Components
import { ExportStatusBarComponent } from 'projects/shared-utilities/utilities/status-bar/export-status-bar.component';
import { PaginationStatusBarComponent } from 'projects/shared-utilities/utilities/status-bar/pagination-status-bar.component';
import { StateDropDownStatusBarComponent } from 'projects/shared-utilities/utilities/status-bar/state-dropdown-status-bar.component';

// Shared Utilities - Module
import { UtilitiesModule } from 'projects/shared-utilities/utilities/utilities.module';


@Component({
  selector: 'app-ag-grid-custom',
  templateUrl: './ag-grid-custom.component.html',
  styleUrls: ['./ag-grid-custom.component.css']
})

export class AgGridCustomComponent implements OnInit {
  @ViewChild('agGrid') agGrid!: AgGridAngular;

  @Input() id = '';
  @Input() title = '';
  @Input() gridOptions: Partial<GridOptions> = {};
  @Input() columnDefs: [];
  @Input() getRowStyle?: (params: any) => any;
  @Input() showColumns: any[] = [];

  @Output() gridReady = new EventEmitter<any>();
  @Output() onMorePageClick = new EventEmitter<any>();
  @Output() onAllPageClick = new EventEmitter<any>();

  masterSettings$ = this.sessionStore.select(SessionSelectors.selectMasterSettings);
  clientSidePermissions$ = this.permissionStore.select(ClientSidePermissionsSelectors.selectAll);

  columnEvents = [
    'columnMoved',
    'columnVisible',
    'columnPinned',
    'columnRowGroupChanged',
    'columnValueChanged',
    'columnPivotChanged',
    'columnResized',
    'columnEverythingChanged'
  ];

  gridStateId;
  gridOptionsDefault: GridOptions;

  settings: any[] = [];
  permissions: any[] = [];

  constructor(
    protected readonly sessionStore: Store<SessionStates>,
    protected readonly permissionStore: Store<ClientSidePermissionStates>,
  ) { }

  ngOnInit() {
    this.gridStateId = this.id + 'State';

    this.gridOptionsDefault = {
      defaultColDef: {
        filter: 'agTextColumnFilter',
        floatingFilter: true,
        width: 100,
        sortable: true,
        resizable: true,
        enableRowGroup: true,
        enablePivot: true
      },
      columnTypes: UtilitiesModule.columnTypes,
      defaultExcelExportParams: UtilitiesModule.getDefaultExportParams(),
      defaultCsvExportParams: UtilitiesModule.getDefaultExportParams(),
      suppressAggFuncInHeader: true,
      pagination: true,
      animateRows: true,
      sideBar: true,
      rowSelection: 'multiple',
      rowGroupPanelShow: 'always',
      groupSelectsChildren: true,
      suppressRowClickSelection: false,
      multiSortKey: 'ctrl',
      onSortChanged: (params) => this.onSortChanged(params),
      onFilterChanged: (params) => this.onFilterChanged(params),
      onPaginationChanged: (params) => {
        if (params.newPage) {
          let currentPage = params.api.paginationGetCurrentPage();
          localStorage.setItem(this.id + 'Page', JSON.stringify(currentPage));
        }
      },
    };

    this.gridOptionsDefault = { ...this.gridOptionsDefault, ...this.gridOptions };
    const paginationEnabled = this.gridOptionsDefault.pagination === true;
    this.gridOptionsDefault.statusBar = this.getStatusBarConfig(paginationEnabled);

    this.getAppletSettings();
    this.getPermissions();
  }

  get gridInstance(): AgGridAngular {
    return this.agGrid;
  }

  async getAppletSettings() {
    await this.masterSettings$.subscribe(
      resolve => {
        this.settings.push(resolve);
      }
    );
  }

  async getPermissions() {
    await this.clientSidePermissions$.subscribe(
      resolve => {
        this.permissions = resolve
      }
    );
  }

  loadGridState(params) {
    if (!params) return;
    this.sessionStore.select(SessionSelectors.selectGridState).subscribe(resolved => {
      let gridState = resolved ? resolved[this.gridStateId] ? resolved[this.gridStateId] : null : null;
      if (!gridState) {
        this.sessionStore.select(SessionSelectors.selectPersonalSettings).subscribe((data) => {
          if (data[this.gridStateId]) {
            params.columnApi.applyColumnState({
              state: JSON.parse(data[this.gridStateId]),
              applyOrder: true, // Set this to true to forcefully apply the state
            });
          }
        });
      }
      else {
        params.columnApi.applyColumnState({
          state: JSON.parse(gridState),
          applyOrder: true, // Set this to true to forcefully apply the state
        });
      }
    })
  }

  onGridReady(params) {
    this.loadGridState(params);

    params.api.closeToolPanel();

    const statusBarComponent = params.api.getStatusPanel('statusBarExportKey');
    statusBarComponent.setTitle(this.title);
    statusBarComponent.setFilename(this.title);
    statusBarComponent.setPageOritentation('landscape');

    this.columnEvents.forEach(event => {
      params.api!.addEventListener(event, () => {
        setTimeout(() => {
          this.saveColumnStateToLocal(params.columnApi!);
          this.saveColumnStateToBackend(params.columnApi!);
        }, 300);
      });
    });

    this.setColumnVisibilityFromSettings(params);

    this.gridReady.emit(params);
  }

  setColumnVisibilityFromSettings(params) {
    const columnApi = params.columnApi;
    const api = params.api;

    if (this.showColumns?.length) {
      this.showColumns.forEach(showColumn => {
        const column = columnApi.getColumn(showColumn.name);
        if (column) {
          const isShowColumn = this.isShowColumn(showColumn.setting, showColumn.permission);
          this.setColumnVisible(api, columnApi, column, isShowColumn);
        }
      });
    }
  }

  isShowColumn(settingName, permissionName) {
    if (settingName.includes('SHOW')) {
      return this.settings.some(setting => setting[settingName]) || this.permissions.some(permission => permission.perm_code === permissionName);
    }
    return !this.settings.some(setting => setting[settingName]) || this.permissions.some(permission => permission.perm_code === permissionName);
  }

  setColumnVisible(gridApi, gridColumnApi, column: any, visible: boolean) {
    var colDef = column.getColDef();
    colDef.suppressColumnsToolPanel = !visible;
    gridColumnApi.setColumnVisible(column, visible)
    gridApi.refreshToolPanel();
  }

  onSortChanged(event) {
    event.api.refreshCells();
  }

  onFilterChanged(event) {
    event.api.refreshCells();
  }

  defaultRowStyle = params => {
    if (params.node.footer) {
      return { fontWeight: 'bold', background: '#e6f7ff' };
    }
    if (params.node.group) {
      return { fontWeight: 'bold' };
    }
  }

  saveColumnStateToLocal(columnApi) {
    const columnState = columnApi.getColumnState();
    const serializedColumnState = JSON.stringify(columnState);
    this.sessionStore.dispatch(SessionActions.selectGridState({ key: [this.gridStateId], gridState: serializedColumnState }));
  }

  saveColumnStateToBackend(columnApi) {
    const columnState = columnApi.getColumnState();
    const serializedColumnState = JSON.stringify(columnState);
    this.sessionStore.dispatch(SessionActions.saveColumnStateInit({
      settings: {
        [this.gridStateId]: serializedColumnState
      }
    }));
  }

  getStatusBarConfig(paginationEnabled: boolean): any {
    const panels: any[] = [
      { statusPanel: ExportStatusBarComponent, key: 'statusBarExportKey', align: 'left' },
      { statusPanel: StateDropDownStatusBarComponent, statusPanelParams: { id: this.gridStateId }, align: 'left' },
      { statusPanel: 'agTotalAndFilteredRowCountComponent', align: paginationEnabled ? 'center' : 'right' },
      { statusPanel: 'agSelectedRowCountComponent', align: paginationEnabled ? 'center' : 'right' },
      { statusPanel: 'agAggregationComponent', align: paginationEnabled ? 'center' : 'right' },
    ];

    if (paginationEnabled) {
      panels.push({
          statusPanel: PaginationStatusBarComponent,
          key: 'statusBarPagination',
          statusPanelParams: {
            onAllPageClick: () => this.handleAllPageClick(),
            onMorePageClick: () => this.handleMorePageClick(),
          },
          align: 'right'
      });
    }

    return { statusPanels: panels };
  }

  handleAllPageClick() {
    this.onAllPageClick.emit();
  }

  handleMorePageClick() {
    this.onMorePageClick.emit();
  }
}
