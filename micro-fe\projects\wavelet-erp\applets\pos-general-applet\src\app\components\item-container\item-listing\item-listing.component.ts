import { Component, ChangeDetectionStrategy, Input, ViewChild, ElementRef, ChangeDetectorRef, Renderer2 } from '@angular/core';
import { ComponentStore } from '@ngrx/component-store';
import { ApiResponseModel, BasicApiResponseModel, bl_fi_generic_doc_hdr_RowClass, bl_fi_generic_doc_line_RowClass, CompanyContainerModel, CompanyService, FinancialItemContainerModel, FinancialItemService, InventoryItemContainerModel, InventoryItemService,  Pagination,  PagingResponseModel,  PricingSchemeContainerModel,  PricingSchemeService,  RecurringSalesInvoiceService,  SubQueryService } from 'blg-akaun-ts-lib';
import { combineLatest, interval, Observable, Subject } from 'rxjs';
import { ViewColumnComponent } from 'projects/shared-utilities/view-column.component';
import { AppConfig } from 'projects/shared-utilities/visa';
import { SubSink } from 'subsink2';
import { FormControl, FormGroup } from '@angular/forms';
import { Store } from '@ngrx/store';
import { ViewColumnFacade } from '../../../facades/view-column.facade';
import { ViewColumnFacadeItem } from '../../../facades/view-column-item.facade';
import { PosActions } from '../../../state-controllers/pos-controller/store/actions';
import { PosStates } from '../../../state-controllers/pos-controller/store/states';
import { PosEditActions } from '../../../state-controllers/pos-edit-controller/store/actions';
import { PosEditStates } from '../../../state-controllers/pos-edit-controller/store/states';
import { AppletSettings } from '../../../models/applet-settings.model';
import { PosSelectors } from '../../../state-controllers/pos-controller/store/selectors';
import { PosEditSelectors } from '../../../state-controllers/pos-edit-controller/store/selectors';
import { ColumnVisibleEvent, GridOptions } from 'ag-grid-community';
import { ToastrService } from 'ngx-toastr';
import { ButtonDeleteRendererComponent } from '../../button-del-renderer/button-del-renderer.component';
import { LineItemModel, MainDetails } from '../../../models/pos.model';
import { SessionStates } from 'projects/shared-utilities/modules/session/session-controller/states';
import { SessionSelectors } from 'projects/shared-utilities/modules/session/session-controller/selectors';
import { PaginationComponent } from 'projects/shared-utilities/utilities/pagination/pagination.component';
import { SessionActions } from 'projects/shared-utilities/modules/session/session-controller/actions';
import { PaginationClientSideComponent } from 'projects/shared-utilities/utilities/pagination-client-side/pagination-client-side.component';
import { debounceTime, filter, map, withLatestFrom, tap } from 'rxjs/operators';
import { POSDexieService } from '../../../services/pos-dexie.service';
import { ConnectionService } from 'ng-connection-service';
import { UtilitiesModule } from 'projects/shared-utilities/utilities/utilities.module';
import { Column2ViewModelActions, Column4ViewModelActions } from '../../../state-controllers/view-model-controller/actions';
import { ColumnViewModelStates } from '../../../state-controllers/view-model-controller/states';
import { PNSActions, PNSEditActions } from '../../../state-controllers/draft-controller/store/actions';
import { DraftStates } from '../../../state-controllers/draft-controller/store/states';
import { PNSSelectors } from '../../../state-controllers/draft-controller/store/selectors';
import { Column1ViewSelectors, Column2ViewSelectors } from '../../../state-controllers/view-model-controller/selectors';
import { PermissionStates } from 'projects/shared-utilities/modules/permission/permission-controller';
import { ClientSidePermissionsSelectors } from 'projects/shared-utilities/modules/permission/client-side-permissions-controller/selectors';
import { AkaunPinNumberDialogComponent } from "projects/shared-utilities/dialogues/akaun-pin-number-dialog/akaun-pin-number-dialog.component";
import { MatDialog, MatDialogRef } from "@angular/material/dialog";

interface LocalState {
  deactivateAdd: boolean;
  deactivateList: boolean;
}

export class ItemPriceJsonDetails {
  uom_guid: string;
  label_guid: string;
  price_name: string;
  price_amount : string;
}

@Component({
  selector: 'app-item-listing',
  templateUrl: './item-listing.component.html',
  styleUrls: ['./item-listing.component.scss'],
  //changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [ComponentStore]
})

export class ItemListingComponent extends ViewColumnComponent {
  @Input() main$: Observable<MainDetails>;
  @Input() pnsItem$: Observable<bl_fi_generic_doc_line_RowClass[]>;
  @Input() pns2: bl_fi_generic_doc_line_RowClass[] = [];
  @Input() pnsReturn: bl_fi_generic_doc_line_RowClass[] = [];
  @Input() isView: boolean;
  @Input() postingStatus: string;
  roundingOpt = 2;
  protected compName = 'Item Listing';
  protected readonly index = 1;
  private localState: LocalState;

  readonly localState$ = this.viewColFacade.selectLocalState(this.index);
  readonly deactivateAdd$ = this.componentStore.select(state => state.deactivateAdd);
  readonly deactivateList$ = this.componentStore.select(state => state.deactivateList);
  timeout: any = null;
  toggleColumn$: Observable<boolean>;
  searchModel;
  delimiter;
  pricingGuid;
  locationGuid;
  form: FormGroup;
  private columnMoveSubject: Subject<void> = new Subject<void>();
  private debounceTimeMs = 500;
  /* @ViewChild(PaginationComponent, { static: true }) private paginationComponent: PaginationComponent; */
  @ViewChild(PaginationClientSideComponent) paginationComponent: PaginationClientSideComponent;
  @ViewChild('basic', { static: false }) codeInput: ElementRef;
  defaultColDef = {
    flex: 1,
    autoHeight:true,
    wrapText: true,
    filter: 'agTextColumnFilter',
    floatingFilterComponentParams: {suppressFilterButton: true},
    sortable: true,
    resizable: true,
    suppressCsvExport: true,

  };
  subtotal="0";
  gridApi;

rowData =[];

  isRowMaster = dataItem => {
    let arr = this.getDoclines(dataItem)
    //console.log('isRowMaster',arr)
    return (dataItem ?arr.length> 0 : false)
  }

  gridOptions: GridOptions = {
    components: {
      buttonRenderer: ButtonDeleteRendererComponent
    },
    columnTypes: UtilitiesModule.columnTypes,
   detailRowAutoHeight: true,
   groupDefaultExpanded: -1,
   //rememberGroupStateWhenNewData: true,
  }
  detailCellRendererParams ;
  private apiVisa = AppConfig.apiVisa;
  isEnter = true;
  timeoutItemAdd: any = null;
  private subs = new SubSink();
  filteredUnitPrice: Observable<string[]>;
  isFuzzy;
  showItem;
  scanBarcode;
  frameworkComponents;
  cashbill$;
  pagination = new Pagination();
  private subSink = new SubSink;
  isReturn: boolean = false;
  isTradeIn: boolean = false;
  checkQuota= false;
  hideReturn = false;
  showTradeIn = false;
  offlineMode = false;
  showItemStockBal = false;
  showAutoMTO = false;
  SHOW_ITEM_DETAIL_AFTER_SCAN = false;
  matchedMTO$;
  myImage;
  mode;
  posTheme ="LEFT_RIGHT";
  mode$;
  button$;
  columnsDefs;
  txnMode;
  menu;
  actions;
  store;
  selectors;
  SHOW_SCAN_CODE = false;
  showSwapSerial = false;
  salesReturnAmountError = false;
  akaunPinNumberDialogComponent: MatDialogRef<AkaunPinNumberDialogComponent>;
  pinNumber;
  gridStateId;
  columnEvents = [
    'columnMoved',
    'columnVisible',
    'columnPinned',
    'columnRowGroupChanged',
    'columnValueChanged',
    'columnPivotChanged',
    'columnResized',
    'columnEverythingChanged'
  ];
  settings: any[] = [];
  permissions: any[] = [];
  showColumns: any[] = [];
  constructor(
    private dialogRef: MatDialog,
    private readonly permissionStore: Store<PermissionStates>,
    private viewColFacadeMain: ViewColumnFacade,
    private readonly draftStore: Store<DraftStates>,
    private viewModelStore: Store<ColumnViewModelStates>,
    private connectionService: ConnectionService,
    protected posDexieService: POSDexieService,
    private toastr: ToastrService,
    private readonly sessionStore: Store<SessionStates>,
    private readonly posCreateStore: Store<PosStates>,
    private readonly posEditStore: Store<PosEditStates>,
    private viewColFacade: ViewColumnFacadeItem,
    private readonly componentStore: ComponentStore<LocalState>,
    private renderer: Renderer2, private cdr: ChangeDetectorRef) {
    super();
    this.posCreateStore.select(PosSelectors.selectMenu).subscribe(menu=>{
      if(menu==="edit"){
        this.actions = PosEditActions;
        this.store = this.posEditStore;
        this.selectors = PosEditSelectors;
      }else{
        this.actions = PosActions;
        this.store = this.posCreateStore;
        this.selectors = PosSelectors;
      }
    })
    this.detailCellRendererParams = {
      detailGridOptions: {
        columnDefs: [
          { headerName: "Item Code", field: "item_code" , cellStyle: () => ({ 'text-align': 'left' }) },
          { headerName: "Item Name", field: "item_name" , cellStyle: () => ({ 'text-align': 'left' })},
          { headerName: "Qty", field: "quantity_base"},
          {
              headerName: "Amt Std",
              field: "amount_std",
              cellStyle: { 'text-align': "right" },
              type: 'numericColumn',
              valueFormatter: params => {
                const value = params.value;
                if (typeof value === 'number') {
                  return value.toFixed(2);
                } else if (value == null) {
                  return "0.00";
                } else {
                  return value;
                }
              }
            },
            {
              headerName: "Amt Discount",
              field: "amount_discount",
              cellStyle: { 'text-align': "right" },
              type: 'numericColumn',
              valueFormatter: params => {
                const value = params.value;
                if (typeof value === 'number') {
                  return value.toFixed(2);
                } else if (value == null) {
                  return "0.00";
                } else {
                  return value;
                }
              }
            },
            {
              headerName: "Amt Txn",
              field: "amount_txn",
              cellStyle: { 'text-align': "right" },
              type: 'numericColumn',
              valueFormatter: params => {
                const value = params.value;
                if (typeof value === 'number') {
                  return value.toFixed(2);
                } else if (value == null) {
                  return "0.00";
                } else {
                  return value;
                }
              }
            }
        ],
        defaultColDef: { flex: 1, resizable: true },
      },
      getDetailRowData: (params) => {
        setTimeout(() => {
          let arr = this.getDoclines(params.data)
          params.successCallback(arr);
        }, 100);
      }
    };
  }

  getDoclines(data:any){
    let arr = [];
    let json = data.item_child_json;
    if(json){
      if(data.item_txn_type==='MADE_TO_ORDER'){
        let arrSection = json['mto'];
        if(arrSection){
          arrSection.forEach(s=>{
            //console.log('s',s)
          let arrComponent = s['component'];
          arrComponent.forEach(c=>{
            // console.log('c',c);

            arr = [...arr,...c.doclines.filter(x=>x.status==='ACTIVE')]
          })
            })

        }
        if(json.childItems){
          arr = json.childItems;
        }
      }else if(data.item_txn_type==='BUNDLE'){
        //if (data.posting_bundle!=='DONE' && json?.childItems) {
          arr = json.childItems;
        //}
      }

    }

    return arr;
  }
  ngOnInit() {
    this.gridStateId = "posLineItemListingState";
    this.cashbill$ = this.store.select(this.selectors.selectEntity);
    this.matchedMTO$ = this.store.select(this.selectors.selectMatchedMTOlist);
    this.mode$ = this.store.select(this.selectors.selectMode);
    this.button$ = this.store.select(this.selectors.selectAddButton);
    this.cashbill$ = this.store.select(this.selectors.selectEntity);
    this.subs.sink = combineLatest([
      this.sessionStore.select(SessionSelectors.selectMasterSettings),
      this.main$,
      this.store.select(this.selectors.selectReturn),
      this.store.select(this.selectors.selectTradeIn),
      this.permissionStore.select(ClientSidePermissionsSelectors.selectAll),
      this.draftStore.select(PNSSelectors.selectTotalSalesAmount),
      this.draftStore.select(PNSSelectors.selectTotalSalesReturnAmount),
      this.draftStore.select(PNSSelectors.selectAll),
    ]).pipe().subscribe(
      ([a, main, isReturn, isTradeIn, clientSidePerm, totalSalesAmount, totalSalesReturnAmount, pns]:any)=>{
        this.settings.push(a);
        this.pinNumber = main.branch?.bl_fi_mst_branch?.pin_number_json?.pin_number;
        const containsPositive = pns.some(
          item => item.amount_signum === 1 && item.item_txn_type !== 'DOC_HEADER_ADJUSTMENT'
        );
        const containsNegative = pns.some(item => (item.amount_signum === -1 && item.server_doc_type==='INTERNAL_SALES_RETURN') || (item.amount_signum === -1 && item.server_doc_type==='INTERNAL_PURCHASE_TRADE_IN'));
        const isExchange = containsPositive && containsNegative;

        if(!a?.POS_ALLOW_SALES_RETURN_HIGHER_THAN_SALES
         // && totalSalesAmount>0
          && Math.abs(totalSalesReturnAmount)>totalSalesAmount
        ){
          this.salesReturnAmountError = true;
        }else{
          this.salesReturnAmountError = false;
        }
        let POS_SWAP_SERIAL_NUMBER_FINAL = clientSidePerm.find(perm => perm.perm_code.toUpperCase() === 'POS_SWAP_SERIAL_NUMBER_FINAL')?true: false;
        if(POS_SWAP_SERIAL_NUMBER_FINAL && a?.POS_SWAP_SERIAL_FINAL){
          this.showSwapSerial = true;
        }else{
          this.showSwapSerial = false;
        }
        this.SHOW_SCAN_CODE =  a?.SHOW_SCAN_CODE?true:false;
        if(isReturn){
          this.txnMode = "RETURN";
        }
        if(!isReturn){
          this.txnMode = "";
        }
        if(isReturn || isTradeIn){
          if(this.codeInput){
            setTimeout(() => {
              this.renderer.selectRootElement(this.codeInput.nativeElement).focus();
              this.cdr.detectChanges();
            },500);
          }

        }
        this.posTheme = a?.POS_THEME?a.POS_THEME:"LEFT_RIGHT";
        this.delimiter = a.QTY_DELIMITER;
        this.pricingGuid = a.PRICING_RETAIL_GUID;
        this.showItem = a.SHOW_ITEM_DETAILS;
        if( a.REGEX &&  a.ITEMCODE_LENGTH &&  a.ITEMPRICE_LENGTH){
          this.scanBarcode = true;
        }
        this.roundingOpt = a.ROUNDING_OPTION??2;
        this.hideReturn =a?.HIDE_RETURN?a?.HIDE_RETURN:false;
        this.showTradeIn =a?.SHOW_TRADE_IN?a?.SHOW_TRADE_IN:false;

        this.showItemStockBal =a?.SHOW_ITEM_STOCK_BALANCE?a?.SHOW_ITEM_STOCK_BALANCE:false;
        const value = `AUTO_APPLY_MTO_BRANCH_${main.branchGuid }`;
        //console.log("value",value)
        //console.log("settings",settings)
        const resolvedValue = a ? a[value] : null;
        //console.log("show mto",resolvedValue)
        this.showAutoMTO = resolvedValue;
        this.locationGuid = main.locationGuid;
        this.SHOW_ITEM_DETAIL_AFTER_SCAN = a?.SHOW_ITEM_DETAIL_AFTER_SCAN;
      }
    );
    this.columnsDefs = [
      { headerName: "#", field: "position_id" ,hide: true, sort: 'asc',cellStyle: () => ({ 'text-align': 'left' }) },
      // {headerName: 'No', minWidth: 40,valueGetter: 'node.rowIndex+1', cellRenderer: 'agGroupCellRenderer'},
      {headerName: 'Item Code', field: 'item_code',     minWidth: this.posTheme==='TOP_BOTTOM'?300:150 ,cellStyle: () => ({ 'text-align': 'left' })
      ,cellRendererSelector: (params) => {
        if (params.data && params.data.item_property_json?.eanCode) {
          return {
            component: 'agGroupCellRenderer',
            params: {
              innerRenderer: (param) => {
                let ean = param.data.item_property_json?.eanCode ? param.data.item_property_json.eanCode : "";
                let code = param.data.item_code;
                if (ean && this.SHOW_SCAN_CODE) {
                  code = code + "<br>" + ean;
                }
                return code;
              }
            }
          };
        } else {
          return {
            component: 'agGroupCellRenderer',
            params: {
              innerRenderer: (param) => param.data.item_code
            }
          };
        }
      }
      },
      {headerName: 'Item Name', field: 'item_name', autoHeight: true, minWidth: this.posTheme==='TOP_BOTTOM'?650:250 ,cellStyle: () => ({ 'text-align': 'left' })
      ,cellRenderer: function(param){
        let detail = param.data.item_name;
        if(param.data.item_remarks!=null ){
          detail += '<br/>Rmk: ' + param.data.item_remarks;
        }
        if(param.data.item_property_json?.mtoStatus){
          if(param.data.item_property_json?.mtoStatus==="FULFILLED")
            detail += '<br/><span style="color:green">' + param.data.item_property_json?.mtoStatus + '</span>' ;
          else
            detail += '<br/><span style="color:red">'+ param.data.item_property_json?.mtoStatus +'</span>' ;
        }
        if(param.data.item_property_json?.salesPrompt){
          detail += '<br/><span style="color:green">' + param.data.item_property_json?.salesPrompt + '</span>' ;
        }else if(param.data.discount_description){
          detail += '<br/><span style="color:green">' + param.data.discount_description + '</span>' ;
        }
        if(param.data.item_sub_type==='SERIAL_NUMBER' || (param.data.item_txn_type==='NSTI')|| (param.data.item_txn_type==='WARRANTY')|| (param.data.item_txn_type==='COUPON')){
          const hasInvalidSerial = UtilitiesModule.checkSerialValid(<any> param.data.serial_no);
          if(hasInvalidSerial){
            detail += '<br/><span style="color:red">Invalid serial number</span>' ;
          }else{
            //console.log('param.data.serial_no',param.data.serial_no);
            let serialArr = [];
            if(Array.isArray(param.data?.serial_no)){
              serialArr = param.data.serial_no.map(sn => sn['sn_id']);
            }else if(param.data?.serial_no?.serialNumbers){
              serialArr = param.data?.serial_no?.serialNumbers;
            }

            const commaSeparatedString = serialArr.join(", ");

            detail += '<br/>SN#: '+commaSeparatedString;
          }
        }
        if(param.data.batch_no!=null ){
          let batch ='';
          let count = 0;
          param.data.batch_no?.batches.forEach(b=>{
            if( count>0){
              batch +=", ";
            }
            batch += b?.batch_no;
            count++;
          })

          if(batch.length>0){
            detail += '<br/>Batch#:' + batch;
          }else{
            detail += '<br/><span style="color:red">Please add batch number</span>' ;
          }
        }else{
          if(param.data.item_property_json?.sub_item_type==='BATCH_NUMBER'){
            detail += '<br/><span style="color:red">Please add batch number</span>' ;
          }
        }
        if(param.data.bin_no!=null ){
          let bin ='';
          let count = 0;
          param.data.bin_no?.bins.forEach(b=>{
            if( count>0){
              bin +=", ";
            }
            bin += b?.bin_hdr_code;
            count++;
          })

          if(bin.length>0){
            detail += '<br/>Bin#:' + bin;
          }else{
            detail += '<br/><span style="color:red">Please add bin number</span>' ;
          }
        }else{
          if(param.data.item_property_json?.sub_item_type==='BIN_NUMBER'){
            detail += '<br/><span style="color:red">Please add bin number</span>' ;
          }
        }
        if(param.data.point_amount!=null ){
            detail += '<br/>Points: ' + param.data.point_amount;
        }
        if(param.data.point_currency!=null ){
          detail += '<br/>Points Ccy: ' + param.data.point_currency;
      }
        return detail;
      },
      },
      {headerName: 'Qty', field: 'quantity_base', minWidth: this.posTheme==='TOP_BOTTOM'?10:40  ,aggFunc: 'sum'
      ,cellRenderer: function(param){
        let qty = param.data.quantity_base;
        if(param.data.uom_to_base_ratio){
          qty = param.data.quantity_base /parseFloat(param.data.uom_to_base_ratio)
        }
        return qty*param.data.amount_signum;
      }},
      {headerName: 'Category 1', field: 'category_1', minWidth: 100, cellStyle: () => ({ 'text-align': 'left' }), hide: true,
        cellRenderer: function(param) {
          return param.data.item_property_json?.category_1 || '';
        }
      },
      {headerName: 'Category 2', field: 'category_2', minWidth: 100, cellStyle: () => ({ 'text-align': 'left' }), hide: true,
        cellRenderer: function(param) {
          return param.data.item_property_json?.category_2 || '';
        }
      },
      {headerName: 'Category 3', field: 'category_3', minWidth: 100, cellStyle: () => ({ 'text-align': 'left' }), hide: true,
        cellRenderer: function(param) {
          return param.data.item_property_json?.category_3 || '';
        }
      },
      {headerName: 'Category 4', field: 'category_4', minWidth: 100, cellStyle: () => ({ 'text-align': 'left' }), hide: true,
        cellRenderer: function(param) {
          return param.data.item_property_json?.category_4 || '';
        }
      },
      {headerName: 'Category 5', field: 'category_5', minWidth: 100, cellStyle: () => ({ 'text-align': 'left' }), hide: true,
        cellRenderer: function(param) {
          return param.data.item_property_json?.category_5 || '';
        }
      },
      {headerName: 'Category 6', field: 'category_6', minWidth: 100, cellStyle: () => ({ 'text-align': 'left' }), hide: true,
        cellRenderer: function(param) {
          return param.data.item_property_json?.category_6 || '';
        }
      },
      {headerName: 'Category 7', field: 'category_7', minWidth: 100, cellStyle: () => ({ 'text-align': 'left' }), hide: true,
        cellRenderer: function(param) {
          return param.data.item_property_json?.category_7 || '';
        }
      },
      {headerName: 'Category 8', field: 'category_8', minWidth: 100, cellStyle: () => ({ 'text-align': 'left' }), hide: true,
        cellRenderer: function(param) {
          return param.data.item_property_json?.category_8 || '';
        }
      },
      {headerName: 'Category 9', field: 'category_9', minWidth: 100, cellStyle: () => ({ 'text-align': 'left' }), hide: true,
        cellRenderer: function(param) {
          return param.data.item_property_json?.category_9 || '';
        }
      },
      {headerName: 'Category 10', field: 'category_10', minWidth: 100, cellStyle: () => ({ 'text-align': 'left' }), hide: true,
        cellRenderer: function(param) {
          return param.data.item_property_json?.category_10 || '';
        }
      },
      {headerName: 'UOM', field: 'uom', minWidth: 70 ,cellStyle: () => ({ 'text-align': 'left' })
     ,cellRenderer: function(param){
          return (param.data.uom?param.data.uom:"");
      }, hide: true },
      {headerName: 'Unit Price',  field: 'amount_std', minWidth: 80 , cellStyle: () => ({ 'text-align': 'right' })
      ,cellRenderer: function(param){
        let unitPrice = param.data.amount_std;
        if(param.data.quantity_base!=0){
          unitPrice = (param.data.amount_std/ param.data.quantity_base).toFixed(2);
        }
        return (parseFloat(unitPrice)*parseFloat(param.data.amount_signum)).toFixed(2);
      }},
      {headerName: 'Unit Discount',  field: 'amount_discount', minWidth: 80, cellStyle: () => ({ 'text-align': 'right' })
      ,cellRenderer: function(param){
        let discount = param.data.amount_discount;
        if(param.data.quantity_base!=0){
          discount = (param.data.amount_discount/ param.data.quantity_base).toFixed(2);
        }
        return (parseFloat(discount)*parseFloat(param.data.amount_signum)).toFixed(2);
      }
      },
      {headerName: 'Amount Net',  field: 'amount_net', hide:true, minWidth: 80 , cellStyle: () => ({ 'text-align': 'right' })
      ,cellRenderer: function(param){
      return (parseFloat(param.data.amount_net)*parseFloat(param.data.amount_signum)).toFixed(2);
      }},
      {headerName: 'Amount Txn',  field: 'amount_txn', minWidth: 80, cellStyle: () => ({ 'text-align': 'right' })
        ,cellRenderer: function(param){
          return (parseFloat(param.data.amount_txn)*parseFloat(param.data.amount_signum)).toFixed(2);
        }
      },
      {headerName: 'Sales Agent', field: 'sales_entity_hdr_name', hide:true, minWidth: 100 ,cellStyle: () => ({ 'text-align': 'left' })},
      { headerName: 'Action', field: 'action',  minWidth: 80,
              valueGetter: params => params.data.guid,
              hide: this.postingStatus==='FINAL'?true:false,
              cellRenderer: 'buttonRenderer',
              cellRendererParams:
              {
                onClick: this.onButtonClick.bind(this)
              }
      }
    ];
    this.subs.sink = this.button$.subscribe({ next: (resolve: string) => this.myImage = resolve });
    this.subs.sink = this.mode$.subscribe({ next: (resolve: string) => this.mode = resolve });
    this.subs.sink = this.store.select(this.selectors.selectReturn).subscribe({ next: resolve => this.isReturn = resolve });
    this.subs.sink = this.store.select(this.selectors.selectTradeIn).subscribe({ next: resolve => this.isTradeIn = resolve });
    //console.log('init item list',this.postingStatus)
    this.onToggle(true);
    this.toggleColumn$ = this.viewColFacade.toggleColumn$;
    this.subs.sink = this.localState$.subscribe(a => {
      this.localState = a;
      this.componentStore.setState(a);
    });
    this.form = new FormGroup({
      scanned_code: new FormControl('')
    });
    if(this.postingStatus!=='FINAL' && this.codeInput){
      setTimeout(() => {
        this.renderer.selectRootElement(this.codeInput.nativeElement).focus();
        this.cdr.detectChanges();
      },500);
    }

    //this.subs.sink = this.main$.subscribe(m=>{
     // this.locationGuid = m.locationGuid;
      //this.maxQtyExt = m.customer.bl_fi_mst_entity_ext.filter(element =>  element.param_code === 'MAX_QTY');
    //})
    this.subs.sink = this.store.select(this.selectors.selectSpendingLimit).subscribe(a=> {
      //console.log('sp',a)
      this.checkQuota = a?.bl_fi_mst_entity_spending_limit_line?.is_enabled;
    });
    if(!this.delimiter){
      this.delimiter = "*";
    }

    this.subs.sink = this.sessionStore.select(SessionSelectors.selectPersonalSettings).subscribe(
      (a:any)=>{
        this.offlineMode = a?.OFFLINE_MODE
      }
    );
    this.store.dispatch(this.actions.selectReturn({value:this.isReturn}));
    this.store.dispatch(this.actions.selectTradeIn({value:this.isTradeIn}));
  }

  onColumnVisible    (e: ColumnVisibleEvent){
    let cs  = e.columnApi.getColumnState();
    //this.store.dispatch(SessionActions.savePersonalSettingsInitNoPopup({settings: {ITEM_GRID_COLUMNS:cs}}));
   }


  onButtonClick(e) {
    if(e.rowData.item_txn_type==='DOC_HEADER_ADJUSTMENT'){
      return;
    }else{
      this.onDelete(e.rowData);
    }
  }

  applyMTO(){
    //console.log('applyMTO')
    this.store.dispatch(this.actions.transformDoclineToMTO());
  }

  onEnter(e) {
    if(!this.locationGuid){
      this.form.controls['scanned_code'].patchValue("");
      this.isReturn = false;
      this.isTradeIn = false;
      return;
    }
    const input = e.target.value.trim();
    if (input === '') {
      return;
    }
    if(this.postingStatus==='FINAL' || this.postingStatus==='VOID'){
      return;
    }
    this.viewModelStore.dispatch(Column4ViewModelActions.processSerialNumberListing_Reset());
    let arr = input.split(this.delimiter,"2");
    let lineItemModel = new LineItemModel();
    lineItemModel.pricing_guid = this.pricingGuid;
    lineItemModel.location_guid = this.locationGuid;
    lineItemModel.scanBarcode = this.scanBarcode;
    lineItemModel.isReturn = this.isReturn;
    lineItemModel.isTradeIn = this.isTradeIn;
    if(arr.length>1){
      lineItemModel.item_qty = arr[0];
      lineItemModel.scancode = arr[1];
      lineItemModel.scanSerial = false;
    }else{
      lineItemModel.item_qty = 1;
      lineItemModel.scancode = arr[0];
      lineItemModel.scanSerial = true;
    }
    if(this.offlineMode){
      let itemAdded = false;
      this.store.dispatch(this.actions.scanItemOfflineInit({lineItemModel:lineItemModel}));
      this.form.controls['scanned_code'].patchValue("");
      this.isReturn = false;
      this.isTradeIn = false;
      itemAdded = true;
    }else{
      this.store.dispatch(this.actions.scanItemInitV2({lineItemModel:lineItemModel,showDetail:this.SHOW_ITEM_DETAIL_AFTER_SCAN }));
      this.form.controls['scanned_code'].patchValue("");
      this.isReturn = false;
      this.isTradeIn = false;
    }
  }

  checkSerialExistPNS(serial: string){
    let serialAdded = false;
    this.pns2.forEach((l:any)=>{
      let sn = l.serial_no?.serialNumbers;

      if(sn && sn.includes(serial)){
        serialAdded = true;

      }
    })
    this.pnsReturn.forEach((l:any)=>{
      let sn = l.serial_no?.serialNumbers;

      if(sn && sn.includes(serial)){
        serialAdded = true;

      }
    })
    if(serialAdded){
      this.toastr.error(
        'Serial Number has already been added!',
        'Success',
        {
          tapToDismiss: true,
          progressBar: true,
          timeOut: 1300
        }
      );
      return true;
    }else{
      return false;
    }

  }

  checkItemExistPNS(code: string){
    let itemAdded = false;
    this.pns2.forEach((l:any)=>{
      let itemCode = l.item_code;

      if(itemCode && itemCode.includes(code)){
        itemAdded = true;

      }
    })

    if(itemAdded){
      this.toastr.error(
        'Customer account has reached the maximum quantity quota for this item',
        'Error',
        {
          tapToDismiss: true,
          progressBar: true,
          timeOut: 1300
        }
      );
      return true;
    }else{
      return false;
    }

  }

  onNext() {
    this.store.dispatch(this.actions.selectModeItem({ mode: 'create' }));
    this.viewModelStore.dispatch(Column4ViewModelActions.processSerialNumberListing_Reset());
    const index = this.showItemStockBal?11:3;
    if(this.posTheme==='TOP_BOTTOM'){
      this.viewColFacadeMain.updateInstance(30,
        {
          deactivateAdd: false,
          deactivateList: false,
          keyword : null,
          isReturn: this.isReturn,
          isTradeIn: this.isTradeIn
        });
      this.viewColFacadeMain.onNextAndReset(this.mode, 30);
    }else{
      this.viewColFacade.updateInstance(index,
        {
          deactivateAdd: false,
          deactivateList: false,
          keyword : null,
          isReturn: this.isReturn,
          isTradeIn: this.isTradeIn
        });
      this.viewColFacade.onNextAndReset(0, index);
    }
  }

  onGridReady(params) {
    this.loadGridState(params);
    this.gridApi = params.api;
    this.gridApi.closeToolPanel();
    this.gridApi.forEachNode(node => {
      node.setExpanded(true);
    })
    this.columnEvents.forEach(event => {
      params.api!.addEventListener(event, () => {
        setTimeout(() => {
          this.saveColumnStateToLocal(params.columnApi!);
          this.saveColumnStateToBackend(params.columnApi!);
        }, 300);
      });
    });

    this.setColumnVisibilityFromSettings(params);

    //this.gridReady.emit(params);
  }

  setColumnVisibilityFromSettings(params) {
    const columnApi = params.columnApi;
    const api = params.api;

    if (this.showColumns?.length) {
      this.showColumns.forEach(showColumn => {
        const column = columnApi.getColumn(showColumn.name);
        if (column) {
          const isShowColumn = this.isShowColumn(showColumn.setting, showColumn.permission);
          this.setColumnVisible(api, columnApi, column, isShowColumn);
        }
      });
    }
  }

  isShowColumn(settingName, permissionName) {
    if (settingName.includes('SHOW')) {
      return this.settings.some(setting => setting[settingName]) || this.permissions.some(permission => permission.perm_code === permissionName);
    }
    return !this.settings.some(setting => setting[settingName]) || this.permissions.some(permission => permission.perm_code === permissionName);
  }

  setColumnVisible(gridApi, gridColumnApi, column: any, visible: boolean) {
    var colDef = column.getColDef();
    colDef.suppressColumnsToolPanel = !visible;
    gridColumnApi.setColumnVisible(column, visible)
    gridApi.refreshToolPanel();
  }

  saveColumnStateToLocal(columnApi) {
    const columnState = columnApi.getColumnState();
    const serializedColumnState = JSON.stringify(columnState);
    this.sessionStore.dispatch(SessionActions.selectGridState({ key: [this.gridStateId], gridState: serializedColumnState }));
  }

  saveColumnStateToBackend(columnApi) {
    const columnState = columnApi.getColumnState();
    const serializedColumnState = JSON.stringify(columnState);
    this.sessionStore.dispatch(SessionActions.saveColumnStateInit({
      settings: {
        [this.gridStateId]: serializedColumnState
      }
    }));
  }

  loadGridState(params) {
    if (!params) return;
    this.sessionStore.select(SessionSelectors.selectGridState).subscribe(resolved => {
      let gridState = resolved ? resolved[this.gridStateId] ? resolved[this.gridStateId] : null : null;
      if (!gridState) {
        this.sessionStore.select(SessionSelectors.selectPersonalSettings).subscribe((data) => {
          if (data[this.gridStateId]) {
            params.columnApi.applyColumnState({
              state: JSON.parse(data[this.gridStateId]),
              applyOrder: true, // Set this to true to forcefully apply the state
            });
          }
        });
      }
      else {
        params.columnApi.applyColumnState({
          state: JSON.parse(gridState),
          applyOrder: true, // Set this to true to forcefully apply the state
        });
      }
    })
  }

  getDatasource(){
    const source = interval(5000);
    this.subSink.sink = this.pnsItem$.pipe(
      withLatestFrom(this.pnsItem$),
      map(([first, a]) => {

        let mod = <any>a.length % this.paginationComponent.rowPerPage;
        //console.log('mod',mod)

        return mod;
      })
    ).subscribe(a=>{
     // console.log('subs',a)
      if(a===1){
        //this.paginationComponent.lastPage();
      }
    });

  }

  onToggle(e: boolean) {
    //this.viewColFacade.toggleColumn(e);
  }

  onRowClicked(line: any) {
    if(line.item_txn_type==='DOC_HEADER_ADJUSTMENT'){
      return;
    }
    if(this.postingStatus==='FINAL'){
      if(!this.showSwapSerial && line.item_sub_type!=='SERIAL_NUMBER'){
        return;
      }
    }
    this.store.dispatch(this.actions.resetSelectedItem());
    this.store.dispatch(this.actions.selectModeItem({ mode: 'edit' }));
   //console.log('onRowClicked',line);
   this.store.dispatch(this.actions.resetInvItem());
   this.viewModelStore.dispatch(Column4ViewModelActions.processSerialNumberListing_Reset());
   if(line.item_sub_type==='SERIAL_NUMBER'){
    if(line.serial_no &&  Array.isArray(line.serial_no)){
      const hasInvalidSerial = line.serial_no.filter(s=>s.status==="INVALID");

      if(hasInvalidSerial && hasInvalidSerial.length>0){
        this.viewModelStore.dispatch(Column4ViewModelActions.setSerialNumberTabFieldColor({color: "warn"}));
      }
      this.viewModelStore.dispatch(Column4ViewModelActions.setSerialNumberTab_ScanTab_SerialNumbersListing({serialNumberListing: line.serial_no}));
    }else if(this.postingStatus!=='FINAL' && line.serial_no &&  line.serial_no?.serialNumbers){
      this.draftStore.dispatch(
        PNSEditActions.validatePNSSerialNo({ line })
      );
    }else if(this.postingStatus==='FINAL' && line.serial_no &&  line.serial_no?.serialNumbers){
      this.draftStore.dispatch(
        PNSEditActions.mapToSerialNumberObject({
          line: line,
          postingStatus: "FINAL",
        })
      );
    }
   }

    this.store.dispatch(this.actions.resetStockBalance());
    if(this.showItemStockBal && line.item_txn_type==='BASIC_ITEM'){
      this.store.dispatch(this.actions.selectStockBalanceInit({fiGuid:line.item_guid, location : this.locationGuid}));
    }
    if(!this.offlineMode){
      this.store.dispatch(this.actions.selectInvItem({fiGuid:line.item_guid.toString()}));
    }

    this.store.dispatch(this.actions.selectLineItemInit({line}));
    this.store.dispatch(this.actions.resetBatch());
    this.store.dispatch(this.actions.resetBin());
    this.store.dispatch(this.actions.resetMembershipPointsForm());

    this.store.dispatch(this.actions.selectPricingLink({fiGuid:line.item_guid.toString()}));
    if(line.item_txn_type==='MADE_TO_ORDER'){
      this.store.dispatch(this.actions.loadMTOMasterInit({json:line.item_child_json}));
    }
    this.store.dispatch(PosActions.selectSegmentLine({segment:null}));
    this.store.dispatch(PosActions.selectProjectLine({project:null}));
    this.store.dispatch(PosActions.selectProfitCenterLine({profitCenter:null}));
    this.store.dispatch(PosActions.selectDimensionLine({dimension:null}));
    if(line.guid_segment){
      this.store.dispatch(PosActions.loadSegmentLine({ guid: line.guid_segment }));
    }
    if(line.guid_project){
      this.store.dispatch(PosActions.loadProjectLine({ guid: line.guid_project }));
    }
    if(line.guid_profit_center){
      this.store.dispatch(PosActions.loadProfitCenterLine({ guid: line.guid_profit_center }));
    }
    if(line.guid_dimension){
      this.store.dispatch(PosActions.loadDimensionLine({ guid: line.guid_dimension }));
    }
    this.store.dispatch(PosActions.selectCopyDepartmentFromHdrFromEditLine({ item: line }));
    this.viewColFacade.updateInstance(2,
      {
        subItemType: line.item_sub_type,
        taxApplicable: line.item_property_json?.taxApplicable,
        taxType: line.item_property_json?.taxType,
        hideUOM: line.item_property_json?.hideUOM,
        isReturn: line.server_doc_type==='INTERNAL_SALES_RETURN',
        eanCode: line.item_property_json?.eanCode,
        txnType: line.item_txn_type,
        selectedIndex: (line.item_txn_type==='MADE_TO_ORDER'?1:0),
        mtoPricingLogic: line.item_property_json?.pricingLogic
      });


    if(this.posTheme==='TOP_BOTTOM'){
      this.viewColFacadeMain.onNextAndReset(this.mode, 32);
    }else{
      this.viewColFacade.onNextAndReset(0,2);
    }
  }

  onCellClicked(e) {
    if (e.column.colId === 'action' || e.column.colId === 'item_name') {
      // Handle specific cell
    } else {
      this.onRowClicked(e.data);
    }
  }

  onDelete(lineItem) {
    if(this.pinNumber){
      this.onConfirmDelete(lineItem);
    }else{
      this.deleteLineItem(lineItem);
    }
  }

  deleteLineItem(lineItem){
    let index;
    this.subs.sink = this.cashbill$.subscribe({ next: resolve => {
      if (resolve)
        index = resolve.bl_fi_generic_doc_line.findIndex(x => x.guid === lineItem.guid)
    }});

    if (index >= 0) {
      // Change status of existing line and link to DELETED
      const line = {...lineItem, status: 'DELETED'};
      const diffLine = new bl_fi_generic_doc_line_RowClass();
      diffLine.amount_discount = <any>(0 - parseFloat(<any>line.amount_discount));
      diffLine.amount_net = <any>(0 - parseFloat(<any>line.amountisReturnonnext_net));
      diffLine.amount_std = <any>(0 - parseFloat(<any>line.amount_std));
      diffLine.amount_tax_gst = <any>(0 - parseFloat(<any>line.amount_tax_gst));
      diffLine.amount_tax_wht = <any>(0 - parseFloat(<any>line.amount_tax_wht));
      diffLine.amount_txn = <any>(0 - parseFloat(<any>line.amount_txn));
      diffLine.amount_signum = line.amount_signum;

      this.viewColFacade.deleteExistingLine(line, diffLine, this.roundingOpt);
    } else {
      // Remove the line and link entirely from the draft
      const diffLine = new bl_fi_generic_doc_line_RowClass();
      diffLine.amount_discount = <any>(0 - parseFloat(<any>lineItem.amount_discount));
      diffLine.amount_net = <any>(0 - parseFloat(<any>lineItem.amount_net));
      diffLine.amount_std = <any>(0 - parseFloat(<any>lineItem.amount_std));
      diffLine.amount_tax_gst = <any>(0 - parseFloat(<any>lineItem.amount_tax_gst));
      diffLine.amount_tax_wht = <any>(0 - parseFloat(<any>lineItem.amount_tax_wht));
      diffLine.amount_txn = <any>(0 - parseFloat(<any>lineItem.amount_txn));
      diffLine.amount_signum = lineItem.amount_signum;
      diffLine.item_txn_type = lineItem.item_txn_type;
      diffLine.item_guid = lineItem.item_guid;
      //console.log('diffLine',diffLine)
     // console.log('lineItem',lineItem)
      this.viewColFacade.deleteLine(lineItem.guid.toString(), diffLine, this.roundingOpt);
      this.posDexieService.deleteRow(lineItem);
    }
  }

  onConfirmDelete(lineItem){
    this.akaunPinNumberDialogComponent = this.dialogRef.open(AkaunPinNumberDialogComponent, { width: '400px' });
    this.akaunPinNumberDialogComponent.componentInstance.confirmMessage = 'Are you sure you want to delete this item ';
    this.akaunPinNumberDialogComponent.componentInstance.pinNo = this.pinNumber;

    this.akaunPinNumberDialogComponent.afterClosed().subscribe((result) => {
      if(result === true) {
        this.deleteLineItem(lineItem)
      }else{

      }
    });
  }

  onSearch(e: string) {
  }

  onReturn(){
    if(this.isReturn){
      this.isReturn = false;
    }else{
      this.isReturn = true;
    }
    this.store.dispatch(this.actions.selectReturn({value:this.isReturn}));
     setTimeout(()=>{
      this.codeInput?.nativeElement?.focus();
      },100)
  }

  onTradeIn(){
    if(this.isTradeIn){
      this.isTradeIn = false;
    }else{
      this.isTradeIn = true;
    }
    this.store.dispatch(this.actions.selectTradeIn({value:this.isTradeIn}));
     setTimeout(()=>{
      this.codeInput?.nativeElement?.focus();
      },100)
  }

  disableReturn(){
    return this.isReturn;
  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }
}
