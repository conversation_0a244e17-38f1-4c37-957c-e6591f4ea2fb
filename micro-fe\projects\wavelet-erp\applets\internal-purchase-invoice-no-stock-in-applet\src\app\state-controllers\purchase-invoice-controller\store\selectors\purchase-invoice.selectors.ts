import { createFeatureSelector, createSelector } from '@ngrx/store';
import { PurchaseInvoiceFeatureKey } from '../reducers/purchase-invoice.reducers';
import { PurchaseInvoiceStates } from '../states';
import { PurchaseInvoiceState } from '../states/purchase-invoice.states';

export const selectPurchaseInvoiceFeature = createFeatureSelector<PurchaseInvoiceState>(PurchaseInvoiceFeatureKey);
export const PurchaseInvoiceSelector = createFeatureSelector<PurchaseInvoiceStates>(PurchaseInvoiceFeatureKey);

export const selectTotalRecords = (state: PurchaseInvoiceStates) => state.purchaseInvoice.totalRecords;
export const selectInvoice = (state:PurchaseInvoiceStates) => state.purchaseInvoice.selectedInvoice;
export const selectEntity = (state: PurchaseInvoiceStates) => state.purchaseInvoice.selectedEntity;
export const selectLineItem = (state: PurchaseInvoiceStates) => state.purchaseInvoice.selectedLineItem;
export const selectPricingScheme = (state: PurchaseInvoiceStates) => state.purchaseInvoice.selectedPricingScheme;
export const selectPayment = (state: PurchaseInvoiceStates) => state.purchaseInvoice.selectedPayment;
export const selectContraDoc = (state: PurchaseInvoiceStates) => state.purchaseInvoice.selectedContraDoc;
export const selectContraLink = (state: PurchaseInvoiceStates) => state.purchaseInvoice.selectedContraLink;
export const selectAttachmentGuid = (state: PurchaseInvoiceStates) => state.purchaseInvoice.selectedAttachmentGuid;
export const selectPrintableFormatGuid = (state: PurchaseInvoiceStates) => state.purchaseInvoice.selectedPrintableFormatGuid;
export const selectPrintableFormatGuids = (state: PurchaseInvoiceStates) => state.purchaseInvoice.selectedPrintableFormatGuids;
export const selectMode = (state: PurchaseInvoiceStates) => state.purchaseInvoice.selectedMode;
export const selectAgGrid = (state: PurchaseInvoiceStates) => state.purchaseInvoice.updateAgGrid;
export const selectKnockoffListingConfig = (state: PurchaseInvoiceStates) => state.purchaseInvoice.knockoffListingConfig;
export const selectCompanyGuid = (state: PurchaseInvoiceStates) => state.purchaseInvoice.selectedCompGuid;
export const selectInternalPurchaseInvoice = (state: PurchaseInvoiceStates) => state.purchaseInvoice.loadedGenDocs;
export const selectEditMode = (state: PurchaseInvoiceStates) => state.purchaseInvoice.editMode;
export const selectInvItem = (state: PurchaseInvoiceStates) => state.purchaseInvoice.selectedInvItem;
export const selectDisableCreate = (state: PurchaseInvoiceStates) => state.purchaseInvoice.disableCreate;
export const selectEditedInvoice = (state: PurchaseInvoiceStates) => state.purchaseInvoice.editedInvoice;
export const selectIsEinvoiceSubmissionAnotherSupplier = (state: PurchaseInvoiceStates) => state.purchaseInvoice.isEinvoiceSubmissionAnotherSupplier;
export const selectItemCategoryFilter = (state: PurchaseInvoiceStates) => state.purchaseInvoice.selectedItemCategoryFilter;
export const selectCategoryGroupList = (state: PurchaseInvoiceStates) => state.purchaseInvoice.categoryGroupList;
// export const selectRowData = (state: PurchaseInvoiceStates) => state.purchaseInvoice.rowData;
// export const selectSearchItemRowData = (state: PurchaseInvoiceStates) => state.purchaseInvoice.searchItemRowData;
// export const selectSearchItemTotalRecords = (state: PurchaseInvoiceStates) => state.purchaseInvoice.searchItemTotalRecords;
// export const selectFirstLoadListing = (state: PurchaseInvoiceStates) => state.purchaseInvoice.firstLoadListing;


export const getPricingSchemeLinks = createSelector(
  selectPurchaseInvoiceFeature,
  (state) => state.pricingSchemeLink
);

export const refreshGenDocListing = createSelector(
  selectPurchaseInvoiceFeature,
  (state) => state.refreshGenDocListing
);

export const selectGuid = createSelector(
  selectPurchaseInvoiceFeature,
  (state) => state.selectedGuid
);

export const selectIntercompanySalesInvoices = createSelector(
  selectPurchaseInvoiceFeature,
  (state) => state.intercompanySalesInvoices
);

export const selectRefreshArapValue = (state: PurchaseInvoiceStates) => state.purchaseInvoice.refreshArapListing;
export const selectArapListing = (state: PurchaseInvoiceStates) => state.purchaseInvoice.loadedArap;
export const updateAgGrid = (state: PurchaseInvoiceStates) => state.purchaseInvoice.updateContraAgGrid;
export const selectedTotalRevenue = (state: PurchaseInvoiceStates) => state.purchaseInvoice.totalRevenue;
export const selectedTotalExpense = (state: PurchaseInvoiceStates) => state.purchaseInvoice.totalExpense;
export const selectedTotalSettlement = (state: PurchaseInvoiceStates) => state.purchaseInvoice.totalSettlement;
export const selectedTotalContra = (state: PurchaseInvoiceStates) => state.purchaseInvoice.totalContra
export const selectedDocOpenAmount = (state: PurchaseInvoiceStates) => state.purchaseInvoice.docOpenAmount;
export const selectedDocArapBalance = (state: PurchaseInvoiceStates) => state.purchaseInvoice.docArapBalance;


export const getKOAttachments = (state: PurchaseInvoiceStates) => state.purchaseInvoice.koAttachments;

export const selectDelimeter = (state: PurchaseInvoiceStates) => state.purchaseInvoice.delimeter;

export const selectDocLink = (state: PurchaseInvoiceStates) => state.purchaseInvoice.docLink;


export const selectTempDoc = createSelector(
  selectPurchaseInvoiceFeature,
  (state) => state.createdTempDoc
);

export const getMyConversionActionState = createSelector(
  selectPurchaseInvoiceFeature,
  (state) => state.convertActionDispatched
);

export const selectAddedContraDoc = createSelector(
  selectPurchaseInvoiceFeature,
  (state) => state.addedContraDoc
);

export const selectDisableCloneBtn = createSelector(
  selectPurchaseInvoiceFeature,
  state => state.disableCloneBtn
);

export const selectCloneSourceGenDocHdr = createSelector(
  selectPurchaseInvoiceFeature,
  state => state.cloneSourceGenDocHdr
);

export const selectEInvoiceEnabled = (state: PurchaseInvoiceStates) => state.purchaseInvoice.eInvoiceEnabled;
export const selectPnsLevel2 = createSelector(
  selectPurchaseInvoiceFeature,
  (state) => state.pnsLevel2
);

export const selectSegment = createSelector(
  selectPurchaseInvoiceFeature,
  (state) => state.selectedSegment
);
export const selectProject = createSelector(
  selectPurchaseInvoiceFeature,
  (state) => state.selectedProject
);
export const selectProfitCenter = createSelector(
  selectPurchaseInvoiceFeature,
  (state) => state.selectedProfitCenter
);
export const selectDimension = createSelector(
  selectPurchaseInvoiceFeature,
  (state) => state.selectedDimension
);
export const selectCOA = createSelector(
  selectPurchaseInvoiceFeature,
  (state) => state.selectedCOA
);
export const selectDepartmentMode= createSelector(
  selectPurchaseInvoiceFeature,
  (state) => state.departmentMode
);
export const selectSegmentLine = createSelector(
  selectPurchaseInvoiceFeature,
  (state) => state.selectedSegmentLine
);
export const selectProjectLine = createSelector(
  selectPurchaseInvoiceFeature,
  (state) => state.selectedProjectLine
);
export const selectProfitCenterLine = createSelector(
  selectPurchaseInvoiceFeature,
  (state) => state.selectedProfitCenterLine
);
export const selectDimensionLine = createSelector(
  selectPurchaseInvoiceFeature,
  (state) => state.selectedDimensionLine
);
export const selectCopyDepartmentFromHdr = createSelector(
  selectPurchaseInvoiceFeature,
  (state) => state.copyDepartmentFromHdr
);
export const selectResetExpansionPanel = createSelector(
  selectPurchaseInvoiceFeature,
  (state) => state.resetExpansionPanel
);
export const selectChildItems = createSelector(
  selectPurchaseInvoiceFeature,
  (state) => state.childItems
);

export const selectPricingSchemeHdr = createSelector(
  selectPurchaseInvoiceFeature,
  (state) => state.selectedPricingSchemeHdr
);

export const selectSelectedCompany = createSelector(
  selectPurchaseInvoiceFeature,
  (state) => state.selectedCompany
);

export const selectSelectedBranch = createSelector(
  selectPurchaseInvoiceFeature,
  (state) => state.selectedBranch
);
export const selectGenDocLock = createSelector(
  selectPurchaseInvoiceFeature,
  (state) => state.docLock
);
export const selectRowData = createSelector(
  selectPurchaseInvoiceFeature,
  (state) => state.rowData
);
export const selectSearchItemRowData = createSelector(
  selectPurchaseInvoiceFeature,
  (state) => state.searchItemRowData
);

export const selectSearchItemTotalRecords = createSelector(
  selectPurchaseInvoiceFeature,
  (state) => state.searchItemTotalRecords
);

export const selectFirstLoadListing = createSelector(
  selectPurchaseInvoiceFeature,
  (state) => state.firstLoadListing
);

export const selectResetKOVertical = createSelector(
  selectPurchaseInvoiceFeature,
  (state) => state.resetKOVertical
)

export const updateAgGridMatchedHistory = createSelector(
  selectPurchaseInvoiceFeature,
  (state) => state.updateMatchedHistoryAgGrid
);
export const selectDocumentForKO = createSelector(
  selectPurchaseInvoiceFeature,
  (state) => state.selectedDocForKO
);
export const selectSearchChildItemRowData = createSelector(
  selectPurchaseInvoiceFeature,
  (state) => state.searchChildItemRowData
);
export const selectSearchChildItemTotalRecords = createSelector(
  selectPurchaseInvoiceFeature,
  (state) => state.searchChildItemTotalRecords
);

export const selectPricingSchemeGuid = createSelector(
  selectPurchaseInvoiceFeature,
  (state) => state.pricingSchemeGuid
);

export const selectTradeInEnabled = createSelector(
  selectPurchaseInvoiceFeature,
  (state) => state.isTradeIn
);

export const selectRoundingItem = createSelector(
  selectPurchaseInvoiceFeature,
  (state) => state.roundingItem
);

export const selectRoundingFiveCent = createSelector(
  selectPurchaseInvoiceFeature,
  (state) => state.roundingFiveCent
);

export const selectGroupDiscountItem = createSelector(
  selectPurchaseInvoiceFeature,
  (state) => state.groupDiscountItem
);

export const selectGroupDiscountPercentage = createSelector(
  selectPurchaseInvoiceFeature,
  (state) => state.groupDiscountPercentage
);
