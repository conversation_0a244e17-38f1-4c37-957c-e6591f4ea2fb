import { Component, OnInit, Input, On<PERSON><PERSON>roy } from "@angular/core";
import { FormControl, FormGroup } from "@angular/forms";
import { Store } from "@ngrx/store";
import {
  AppletContainerModel,
  BranchContainerModel,
  GuidDataFieldInterface,
} from "blg-akaun-ts-lib";
import { PermissionStates } from "projects/shared-utilities/modules/permission/permission-controller";
import { UserPermInquirySelectors } from "projects/shared-utilities/modules/permission/user-permissions-inquiry-controller/selectors";
import { SessionActions } from "projects/shared-utilities/modules/session/session-controller/actions";
import { SessionSelectors } from "projects/shared-utilities/modules/session/session-controller/selectors";
import { SessionStates } from "projects/shared-utilities/modules/session/session-controller/states";
import { AppConfig } from "projects/shared-utilities/visa";
import { Observable } from "rxjs";
import { SubSink } from "subsink2";
import { AppletSettings } from "../../../models/applet-settings.model";
import { debounceTime, distinctUntilChanged } from "rxjs/operators";

interface Option {
  value: string;
  viewValue: string;
}
@Component({
  selector: "app-personal-default-settings",
  templateUrl: "./personal-default-settings.component.html",
  styleUrls: ["./personal-default-settings.component.css"],
})
export class PersonalDefaultSettingsComponent implements OnInit, OnDestroy {
  @Input() appletSettings$: Observable<AppletContainerModel>;

  private subs = new SubSink();

  form: FormGroup;

  apiVisa = AppConfig.apiVisa;
  selectedBranch: GuidDataFieldInterface;
  branchGuids: any[];
  locationGuids: any[];
  itemTypeOptions: Option[] = [
    { value: "BASIC_ITEM", viewValue: "Basic Item" },
    { value: "GROUPED_ITEM", viewValue: "Grouped Item" },
    { value: "BUNDLE", viewValue: "Bundle" },
    { value: "COUPON", viewValue: "Coupon" },
    { value: "SERVICE", viewValue: "Service" },
    { value: "WARRANTY", viewValue: "Warranty" },
    { value: "GL_CODE", viewValue: "Account Code" },
    { value: "DOC_HEADER_ADJUSTMENT", viewValue: "Doc Header Adjustment" },
    { value: "MEMBERSHIP", viewValue: "Membership" },
    { value: "MADE_TO_ORDER", viewValue: "Made to Order (MTO)" },
    { value: "DIGITAL_GOODS", viewValue: "Digital Goods" },
    { value: "FIXED_ASSET_REGISTER", viewValue: "Fixed Asset Register" },
    { value: "SALES_CONTRACT", viewValue: "Sales Contract" },
    { value: "DELIVERY_CHARGES", viewValue: "Delivery Charges" },
  ];
  selectedOptions: string[] = [];

  userPermissionTarget$ = this.permissionStore.select(
    UserPermInquirySelectors.selectUserPermInquiry
  );

  constructor(
    private readonly store: Store<SessionStates>,
    protected readonly permissionStore: Store<PermissionStates>
  ) {}

  ngOnInit() {
    this.form = new FormGroup({
      DEFAULT_BRANCH: new FormControl(),
      DEFAULT_LOCATION: new FormControl(),
      DEFAULT_ITEM_SEARCH_ITEM_TYPE: new FormControl(''),
      DEFAULT_TOGGLE_COLUMN: new FormControl(),
      DEFAULT_ORIENTATION: new FormControl(),
    });
    this.subs.sink = this.store
      .select(SessionSelectors.selectPersonalSettings)
      .subscribe({
        next: (resolve: AppletSettings) => {
          this.selectedBranch = resolve?.DEFAULT_BRANCH;
          this.form.patchValue({
            DEFAULT_BRANCH: resolve?.DEFAULT_BRANCH,
            DEFAULT_LOCATION: resolve?.DEFAULT_LOCATION,
            DEFAULT_ITEM_SEARCH_ITEM_TYPE: resolve?.DEFAULT_ITEM_SEARCH_ITEM_TYPE,
            DEFAULT_TOGGLE_COLUMN: resolve?.DEFAULT_TOGGLE_COLUMN,
            DEFAULT_ORIENTATION: resolve?.DEFAULT_ORIENTATION,
          });
        },
      });

    this.subs.sink = this.userPermissionTarget$.subscribe((targets) => {
      console.log("targets", targets);
      let target = targets.filter(
        (target) =>
          target.permDfn ===
          "TNT_API_DOC_INTERNAL_PURCHASE_INVOICE_NO_STOCK_IN_READ_TGT_GUID"
      );
      // this.branchGuids =
      //   target[0]?.target !== null &&
      //   Object.keys(target[0]?.target || {}).length !== 0
      //     ? target[0]?.target["bl_fi_mst_branch"]
      //     : [];
      let adminCreatePermissionTarget = targets.filter(
        (target) => target.permDfn === "TNT_TENANT_ADMIN"
      );
      let ownerCreatePermissionTarget = targets.filter(
        (target) => target.permDfn === "TNT_TENANT_OWNER"
      );
      if(adminCreatePermissionTarget[0]?.hasPermission || ownerCreatePermissionTarget[0]?.hasPermission){
        this.branchGuids = [];
      }else{
        this.branchGuids = (target[0]?.target!==null && Object.keys(target[0]?.target || {}).length !== 0) ? target[0]?.target["bl_fi_mst_branch"] : [];
      }
    });

    this.subs.sink = this.form.valueChanges
      .pipe(debounceTime(100), distinctUntilChanged())
      .subscribe({
        next: (form) => {
          console.log(form);
          this.selectedBranch = form.DEFAULT_BRANCH;
        },
      });
  }

  updateSelectedOptions(): void {
    const selectedOptions = this.form.get('DEFAULT_ITEM_SEARCH_ITEM_TYPE')?.value;
    console.log(selectedOptions);
    // this.multiSelectForm.patchValue({ selectedOptions });
  }

  onBranchSelected(e: BranchContainerModel) {
    this.selectedBranch = e.bl_fi_mst_branch.guid;
    this.form.patchValue({ DEFAULT_COMPANY: e.bl_fi_mst_branch.comp_guid });
  }

  onSave() {
    console.log(this.form.value);
    this.store.dispatch(
      SessionActions.savePersonalSettingsInit({ settings: this.form.value })
    );
  }

  onReset() {
    this.store.dispatch(
      SessionActions.savePersonalSettingsInit({
        settings: {
          DEFAULT_BRANCH: null,
          DEFAULT_LOCATION: null,
          DEFAULT_COMPANY: null,
          DEFAULT_ITEM_SEARCH_ITEM_TYPE: null,
          DEFAULT_TOGGLE_COLUMN: null,
          DEFAULT_ORIENTATION: null,
        },
      })
    );
  }
  ngOnDestroy() {
    this.subs.unsubscribe();
  }
}
