import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { MatSnackBar } from '@angular/material/snack-bar';
import { Store } from '@ngrx/store';
import { ToastrService } from 'ngx-toastr';
import { 
  bl_fi_generic_doc_line_RowClass, 
  bl_fi_mst_entity_line_RowClass, 
  EntityContainerModel, 
  bl_fi_generic_doc_link_RowClass,
  GenericDocContainerModel,
  PricingSchemeLinkContainerModel,
  JsonDatatypeInterface
} from 'blg-akaun-ts-lib';
import { AppStates } from 'projects/shared-utilities/application-controller/store/states';
import { ViewColActions } from 'projects/shared-utilities/application-controller/store/actions';
import { ViewColSelectors } from 'projects/shared-utilities/application-controller/store/selectors';
import { ViewColumnState } from 'projects/shared-utilities/application-controller/store/states/view-col.states';
import { ViewCacheActions } from '../state-controllers/view-cache-controller/store/actions';
import { ViewCacheSelectors } from '../state-controllers/view-cache-controller/store/selectors';
import { ViewCacheStates } from '../state-controllers/view-cache-controller/store/states';
import { DraftStates } from '../state-controllers/draft-controller/store/states';
import { HDRActions, LinkActions, PaymentActions, PNSActions } from '../state-controllers/draft-controller/store/actions';
import { PurchaseInvoiceStates } from '../state-controllers/purchase-invoice-controller/store/states';
import { PurchaseInvoiceActions } from '../state-controllers/purchase-invoice-controller/store/actions';
import { LineItemActions } from '../state-controllers/line-item-controller/store/actions';
import { LineItemStates } from '../state-controllers/line-item-controller/store/states';
import { SupplierStates } from '../state-controllers/supplier-controller/states';
import { SupplierActions } from '../state-controllers/supplier-controller/actions';
import { Observable } from 'rxjs';
import { PurchaseInvoiceSelectors } from '../state-controllers/purchase-invoice-controller/store/selectors';
@Injectable()
export class ViewColumnFacade {

  viewColState: ViewColumnState;
  purchaseInvoiceCache$ = this.viewCacheStore.select(ViewCacheSelectors.selectPurchaseInvoiceCache);
  lineItemsCache$ = this.viewCacheStore.select(ViewCacheSelectors.selectLineItemsCache);
  printableFormatSettingsCache$ = this.viewCacheStore.select(ViewCacheSelectors.selectPrintableFormatSettingsCache);
  fileImportCache$ = this.viewCacheStore.select(ViewCacheSelectors.selectFileImportCache);
  purchaseInvoiceFileExportCache$ = this.viewCacheStore.select(ViewCacheSelectors.selectFileExportCache);

  docLink$ = this.piStore.select(PurchaseInvoiceSelectors.selectDocLink);

  firstCol$ = this.appStore.select(ViewColSelectors.selectFirstColComp);
  secondCol$ = this.appStore.select(ViewColSelectors.selectSecondColComp);
  breadCrumbs$ = this.appStore.select(ViewColSelectors.selectBreadCrumbs);
  leftDrawer$ = this.appStore.select(ViewColSelectors.selectLeftDrawer);
  rightDrawer$ = this.appStore.select(ViewColSelectors.selectRightDrawer);
  toggleColumn$ = this.appStore.select(ViewColSelectors.selectSingleColumn);
  prevIndex$ = this.appStore.select(ViewColSelectors.selectPrevIndex);
  prevLocalState$ = () => {
    ViewColSelectors.selectPrevLocalState.release();
    return this.appStore.select(ViewColSelectors.selectPrevLocalState);
  }

  errorLog = [];
  draft$: Observable<any>;

  constructor(
    private readonly appStore: Store<AppStates>,
    private readonly viewCacheStore: Store<ViewCacheStates>,
    private readonly draftStore: Store<DraftStates>,
    private readonly piStore: Store<PurchaseInvoiceStates>,
    private readonly lineStore: Store<LineItemStates>,
    private router: Router,
    private snackBar: MatSnackBar,
    private readonly supplierExtStore: Store<SupplierStates>,
    private toastr: ToastrService) {
    this.appStore.select(ViewColSelectors.selectViewColState).subscribe( resolve => this.viewColState = resolve);
  }

  setViewColState(state: ViewColumnState) {
    this.appStore.dispatch(ViewColActions.setViewColState({ state }));
  }

  onNext(index: number) {
    this.appStore.dispatch(ViewColActions.viewColNext({ index }));
  }

  onNextAndReset(curIndex: number, nextIndex: number) {
    this.appStore.dispatch(ViewColActions.viewColNextAndReset({ curIndex, nextIndex }));
  }

  onPrev(index: number) {
    this.appStore.dispatch(ViewColActions.viewColPrev({ index }));
  }

  updateInstance<T>(index: number, localState: T) {
    this.appStore.dispatch(ViewColActions.viewColUpdateInstance({ index, localState }));
  }

  goToIndex(index: number) {
    this.appStore.dispatch(ViewColActions.goToIndex({ index }));
  }

  goBackIndex(index: number) {
    this.appStore.dispatch(ViewColActions.viewColRvIndex({ index }));
  }

  goForwardIndex(index: number) {
    this.appStore.dispatch(ViewColActions.viewColFwIndex({ index }));
  }

  resetIndex(index: number) {
    this.appStore.dispatch(ViewColActions.resetIndex({ index }));
  }

  toggleColumn(toggle: boolean) {
    this.appStore.dispatch(ViewColActions.toggleColumn({ toggle }));
  }

  selectLocalState(index: number) {
    return this.appStore.select(ViewColSelectors.selectLocalState, index);
  }

  gotoFourOhFour() {
    this.router.navigate(['404']);
  }

  savePurchaseInvoiceState() {
    this.viewCacheStore.dispatch(ViewCacheActions.cachePurchaseInvoice({ cache: this.viewColState }));
  }

  saveLineItemsState() {
    this.viewCacheStore.dispatch(ViewCacheActions.cacheLineItems({ cache: this.viewColState }));
  }

  savePrintableFormatSettingsState() {
    this.viewCacheStore.dispatch(ViewCacheActions.cachePrintableFormatSettings({ cache: this.viewColState }));
  }

  saveFileImportState() {
    this.viewCacheStore.dispatch(ViewCacheActions.cacheFileImport({ cache: this.viewColState }));
  }

  saveFileExportState() {
    this.viewCacheStore.dispatch(ViewCacheActions.cacheFileExport({ cache: this.viewColState }));
  }

  showSuccessToast(message: string) {
    this.toastr.success(
      message,
      'Success',
      {
        tapToDismiss: true,
        progressBar: true,
        timeOut: 2000
      }
    );
  }

  showWarningToast(message: string) {
    this.toastr.warning(
      message,
      'Warning',
      {
        tapToDismiss: true,
        progressBar: true,
        timeOut: 2000
      }
    );
  }

  showFailedToast(err) {
    this.toastr.error(
      err.message,
      'Error',
      {
        tapToDismiss: true,
        progressBar: true,
        timeOut: 2000
      }
    );
  }

  showSnackBar(message: string) {
    this.snackBar.open(message, 'Close');
  }

  selectEntity(entity: { entity: EntityContainerModel, contact: bl_fi_mst_entity_line_RowClass }) {
    this.piStore.dispatch(PurchaseInvoiceActions.selectEntity({ entity }));
    console.log(entity);
      // Default Billing
      let billingAdress = entity.entity.bl_fi_mst_entity_hdr.addresses_json ? entity.entity.bl_fi_mst_entity_hdr.addresses_json.billing_address : null;
      if(billingAdress){
        let billingAddresses = entity.entity.bl_fi_mst_entity_hdr.addresses_json.billing_address;
        billingAddresses.forEach(eachBilling => {
          if(eachBilling.default_address_status || (entity.entity.bl_fi_mst_entity_hdr.addresses_json.billing_address.length ==1)){
            let defaultBillingAddress = <JsonDatatypeInterface>eachBilling;
            this.piStore.dispatch(PurchaseInvoiceActions.selectBillingAddress({ billing_address:defaultBillingAddress }));
          }
        })
      } 
      // Default Shipping
      let shippingAdress = entity.entity.bl_fi_mst_entity_hdr.addresses_json ? entity.entity.bl_fi_mst_entity_hdr.addresses_json.shipping_address : null;
      if(shippingAdress){
        let shippingAddreses = entity.entity.bl_fi_mst_entity_hdr.addresses_json.shipping_address;
        shippingAddreses.forEach(eachShipping => {
          if(eachShipping.default_address_status || entity.entity.bl_fi_mst_entity_hdr.addresses_json.shipping_address.length ==1){
            let defaultShippingAddress = <JsonDatatypeInterface>eachShipping;
            this.piStore.dispatch(PurchaseInvoiceActions.selectShippingAddress({ shipping_address:defaultShippingAddress }));
          }
        })
      } 
  }

  addLineItem(line: bl_fi_generic_doc_line_RowClass, mode: string) {    
    this.draftStore.dispatch(PNSActions.addPNS({ pns: line }));
    this.piStore.dispatch(PurchaseInvoiceActions.recalculateGroupDiscount());
    if (mode === 'create')
      this.resetIndex(1);
    else if (mode === 'edit')
      this.resetIndex(2);
    this.snackBar.open(`Line Item Added`, 'Close');
  }

  editLineItem(line: bl_fi_generic_doc_line_RowClass, diffLine: bl_fi_generic_doc_line_RowClass, pageIndex: number) {
    this.draftStore.dispatch(PNSActions.editPNS({ pns: line }));
    this.draftStore.dispatch(HDRActions.updateBalance({ pns: diffLine }));
    if (pageIndex === 1)
      this.resetIndex(1);
    else if (pageIndex === 2)
      this.resetIndex(2);
    this.snackBar.open(`Line Item Edited`, 'Close');
  }

  addLink(link: bl_fi_generic_doc_link_RowClass) {    
    this.draftStore.dispatch(LinkActions.addLink({ link }));
  }

  editLink(link: bl_fi_generic_doc_link_RowClass) {    
    this.draftStore.dispatch(LinkActions.editLink({ link }));
  }

  deleteLink(guid: string) {
    this.draftStore.dispatch(LinkActions.deleteLink({ guid }));
  }

  deleteExistingLine(line: bl_fi_generic_doc_line_RowClass, diffLine: bl_fi_generic_doc_line_RowClass) {
    this.draftStore.dispatch(PNSActions.editPNS({ pns: line })); // Update line with status DELETED
    this.draftStore.dispatch(HDRActions.updateBalance({ pns: diffLine })); // update HDR pricing
    this.resetIndex(2);
    this.snackBar.open(`Line Item Deleted`, 'Close');
  }

  deleteLine(guid: string, diffLine: bl_fi_generic_doc_line_RowClass, pageIndex: number) {
    this.draftStore.dispatch(PNSActions.deletePNS({ guid })); // Remove from line array entirely
    this.draftStore.dispatch(HDRActions.updateBalance({ pns: diffLine })); // update HDR pricing
    if (pageIndex === 1)
      this.resetIndex(1);
    else if (pageIndex === 2)
      this.resetIndex(2);
    this.snackBar.open(`Line Item Deleted`, 'Close');
  }
  startDraft() {
    this.supplierExtStore.dispatch(SupplierActions.startDraft());
  }
  updateDraftHdr(entity: EntityContainerModel) {
    this.supplierExtStore.dispatch(SupplierActions.updateDraft({ entity, line: null }));
  }

  updateMainOnKOImport(genDocHdr:GenericDocContainerModel){
    this.draftStore.dispatch(HDRActions.updateMainOnKOImport({genDocHdr}));
  }
  
  createSupplier(supplierExt: EntityContainerModel) {
    this.supplierExtStore.dispatch(SupplierActions.createSupplier({ supplierExt }));
    this.supplierExtStore.dispatch(SupplierActions.resetDraft());
    this.updateInstance(0, {
      deactivateAdd: false,
      deactivateList: false
    });
    this.resetIndex(3);
  }

  editGenDocLine(genDoc: GenericDocContainerModel) {
    this.lineStore.dispatch(LineItemActions.editGenLineItemInit({ genDoc }));
  }

  addPaymentMethod(line: bl_fi_generic_doc_line_RowClass, pageIndex: number) {
    this.draftStore.dispatch(PaymentActions.addPaymentInit({ payment: line, pageIndex: pageIndex }));
  }

  editPaymentMethod(line: bl_fi_generic_doc_line_RowClass, diffAmt: any, pageIndex: number) {
    this.draftStore.dispatch(PaymentActions.editPaymentInit({ payment: line, diffAmt: diffAmt, pageIndex: pageIndex }));
  }

  deleteExistingPaymentMethod(line: bl_fi_generic_doc_line_RowClass, diffAmt: any) {
    this.draftStore.dispatch(PaymentActions.deleteExistingPaymentInit({ payment: line, diffAmt: diffAmt }));
  }

  deletePaymentMethod(guid: string, diffAmt: any, pageIndex: number) {
    this.draftStore.dispatch(PaymentActions.deletePaymentInit({ guid: guid, diffAmt: diffAmt, pageIndex: pageIndex }));
  }

  resetDraft() {
    this.piStore.dispatch(PurchaseInvoiceActions.resetPurchaseInvoice());
    this.snackBar.open(`Purchase Invoice Reset`, 'Close');
  }

  resetDepartment(){
    this.piStore.dispatch(PurchaseInvoiceActions.selectSegment({segment:null}));
    this.piStore.dispatch(PurchaseInvoiceActions.selectProject({project:null}));
    this.piStore.dispatch(PurchaseInvoiceActions.selectProfitCenter({profitCenter:null}));
    this.piStore.dispatch(PurchaseInvoiceActions.selectDimension({dimension:null}));
    this.piStore.dispatch(PurchaseInvoiceActions.selectSegmentLine({segment:null}));
    this.piStore.dispatch(PurchaseInvoiceActions.selectProjectLine({project:null}));
    this.piStore.dispatch(PurchaseInvoiceActions.selectProfitCenterLine({profitCenter:null}));
    this.piStore.dispatch(PurchaseInvoiceActions.selectDimensionLine({dimension:null}));
  }

  loadDepartment(){
  }

}