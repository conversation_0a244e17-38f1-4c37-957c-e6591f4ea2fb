import { ChangeDetectionStrategy, Component, <PERSON><PERSON>hild, <PERSON>Children, ElementRef, AfterViewChecked } from '@angular/core';
import { MatTabGroup } from '@angular/material/tabs';
import { ComponentStore } from '@ngrx/component-store';
import { Store } from '@ngrx/store';
import { take } from 'rxjs/operators';
import { SubSink } from 'subsink2';
import { SessionStates } from 'projects/shared-utilities/modules/session/session-controller/states';
import { ViewColumnComponent } from 'projects/shared-utilities/view-column.component';
import { ViewColumnFacade } from '../../../facades/view-column.facade';
import { CashTransferStates } from '../../../state-controllers/cash-transfer-controller/store/states';
import { CashTransferMainDetailsComponent } from '../cash-transfer-create/cash-transfer-main-details/cash-transfer-main-details.component';
import { CashTransferActions } from '../../../state-controllers/cash-transfer-controller/store/actions';
import { CashTransferSelectors } from '../../../state-controllers/cash-transfer-controller/store/selectors';
import { CashTransferLineListingComponent } from './cash-transfer-line-listing/cash-transfer-line-listing.component';
import { SessionSelectors } from 'projects/shared-utilities/modules/session/session-controller/selectors';

interface LocalState {
  deactivateReturn: boolean;
  deactivateAdd: boolean;
  deactivateList: boolean;
  selectedIndex: number;
  expandedPanel: number;
}

@Component({
  selector: 'app-cash-transfer-edit',
  templateUrl: './cash-transfer-edit.component.html',
  styleUrls: ['./cash-transfer-edit.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [ComponentStore]
})

export class CashTrasnferEditComponent extends ViewColumnComponent implements AfterViewChecked {

  protected readonly index = 2;
  protected compName = 'Cash Transfer Edit';
  protected localState: LocalState;
  protected prevLocalState: any;
  protected subs = new SubSink();
  createPayload:any;

  prevIndex: number;
  readonly localState$ = this.viewColFacade.selectLocalState(this.index);
  readonly deactivateReturn$ = this.componentStore.select(state => state.deactivateReturn);
  readonly selectedIndex$ = this.componentStore.select(state => state.selectedIndex);
  readonly expandedPanel$ = this.componentStore.select(state => state.expandedPanel);
  selectedCashTransfer$ = this.store.select(CashTransferSelectors.selectCashTransfer);
  selectedCashTransferLines$ = this.store.select(CashTransferSelectors.selectCashTransferLines);
  disableCloneBtn$ = this.store.select(CashTransferSelectors.selectDisableCloneBtn);
  selectedCashTransferLines:any[] = [];
  selectedCashTransferGuid:any;

  @ViewChild(MatTabGroup) matTab: MatTabGroup;
  @ViewChild(CashTransferMainDetailsComponent) main: CashTransferMainDetailsComponent;
  @ViewChild(CashTransferLineListingComponent) lineListing: CashTransferLineListingComponent;
  appletSettings;
  postingStatus;

  panels = [
    { title: 'Main Details', content: 'main-details', expandSetting: 'EXPAND_MAIN_DETAILS'},
    { title: 'Transfer Line', content: 'transfer-line', expandSetting: 'EXPAND_TRANSFER_LINE'},
    { title: 'Journal Transaction', content: 'journal-txn', expandSetting: 'EXPAND_JOURNAL_TXN'},
    { title: 'Cashbook Transaction Line', content: 'cashbook-txn', expandSetting: 'EXPAND_CASHBOOK_TXN'},
    { title: 'Export', content: 'export', hide: 'HIDE_EXPORT_TAB', expandSetting: 'EXPAND_EXPORT_TAB'},
  ];
  expandedPanelIndex: number = 0;
  orientation: boolean;
  resetExpansion: boolean = false;
  @ViewChildren('panel', { read: ElementRef }) panelScroll;
  isPanelExpanded: boolean = false;
  viewCheckedSub: boolean = false;
  HIDE_CLONE_BUTTON: boolean;

  constructor(
      private viewColFacade: ViewColumnFacade,
      private readonly store: Store<CashTransferStates>,
      protected readonly sessionStore: Store<SessionStates>,
      private readonly componentStore: ComponentStore<LocalState>,
  ) {
    super();
   }


   ngOnInit(){
    this.subs.sink = this.sessionStore.select(SessionSelectors.selectMasterSettings).subscribe(
      (resolve) => {
        this.appletSettings = resolve;
        this.HIDE_CLONE_BUTTON = resolve?.HIDE_CLONE_BUTTON;
      });
    this.subs.sink = this.localState$.subscribe(a => {
      this.localState = a;
      this.componentStore.setState(a);
    });
    this.subs.sink = this.viewColFacade.prevIndex$.subscribe(resolve => this.prevIndex = resolve);
    this.subs.sink = this.viewColFacade.prevLocalState$().subscribe(resolve => this.prevLocalState = resolve);
    this.subs.sink = this.selectedCashTransfer$.subscribe((data:any) => {
      if(data){
        this.selectedCashTransferGuid = data.bl_fi_cash_document_hdr.guid;
        this.postingStatus = data.bl_fi_cash_document_hdr.posting_status;
      }
    })
    this.subs.sink = this.selectedCashTransferLines$.subscribe((data:any) => {
      if(data.length > 0) {
        this.selectedCashTransferLines = data
      } else if (this.appletSettings.SAVE_CASH_TRANSFER_DETAILS) {
        this.selectedCashTransferLines = JSON.parse(localStorage.getItem('cashTransferLineListing'));
      }
    })

    this.store.select(CashTransferSelectors.selectResetExpansionPanel).subscribe(x => {
      this.resetExpansion = x;
      if (!this.resetExpansion){
        this.subs.sink = this.expandedPanel$.subscribe(expandedPanel =>{
          if(expandedPanel !== undefined) {
            this.expandedPanelIndex = expandedPanel;
        }});
      } else {
        this.expandedPanelIndex = 0;
        this.store.dispatch(CashTransferActions.resetExpansionPanel({ resetIndex: null }));
      }
    });

    this.initializeExpandedPanels();
  }

  ngAfterViewChecked() {
    if(this.orientation){
      if (this.isPanelExpanded) {
        if(this.resetExpansion){
          this.expandedPanelIndex = 0;
          this.store.dispatch(CashTransferActions.resetExpansionPanel({ resetIndex: null }));
        } else {
          if(!this.viewCheckedSub) {
            this.expandedPanel$.subscribe(expandedPanel => {
              if (expandedPanel !== undefined) { this.expandedPanelIndex = expandedPanel }
            });
            this.viewCheckedSub = true;
          }
        }
        setTimeout(() => this.panelScroll.toArray()[this.expandedPanelIndex].nativeElement.scrollIntoView({ behavior: 'smooth' }),500);;
        this.isPanelExpanded = false;
      }
    }
  }

  initializeExpandedPanels(): void {
    const expandedIndex = this.panels.findIndex(panel =>
      panel.expandSetting && this.appletSettings[panel.expandSetting]
    );
    if (expandedIndex !== -1) {
      this.expandedPanelIndex = expandedIndex;
    }
  }

  onPanelOpened(index: number): void {
    if (this.orientation) {
      this.expandedPanelIndex = index;
      this.isPanelExpanded = true;
    }
  }

  savePanelState(): void {
    this.viewColFacade.updateInstance<LocalState>(this.index, {
      ...this.localState,
      expandedPanel: this.expandedPanelIndex
    });
  }

  getFilteredPanels() {
    return this.panels.filter(panel => {
      const hidePanels = panel.hide && this.appletSettings[panel.hide];
      return !hidePanels;
    });
  }

  showPanels(): boolean {
    if(this.appletSettings?.VERTICAL_ORIENTATION){
      if(this.appletSettings?.DEFAULT_ORIENTATION === 'HORIZONTAL'){
        this.orientation = false;
      } else {
        this.orientation = true;
      }
    } else {
      if(this.appletSettings?.DEFAULT_ORIENTATION === 'VERTICAL'){
        this.orientation = true;
      } else {
        this.orientation = false;
      }
    }
    return this.orientation;
  }

  showFinal(){
    if(!this.selectedCashTransferLines?.filter(c=>c.status==='ACTIVE').length){
      return false;
    }
    if(this.postingStatus==='FINAL'){
      return false;
    }else{
      return true;
    }
  }

  preparePayload(data?: any, lines?: any) {
    let payload: any = {
      hdrGuid: this.selectedCashTransferGuid
    };

    if (this.main?.form?.value) {
      payload.hdrForm = this.main.form.value;
    }

    if (this.selectedCashTransferLines?.length) {
      payload.lines = this.selectedCashTransferLines;
    }

    if (data) {
      payload.hdrForm = {
        companyGuid: data.bl_fi_cash_document_hdr.comp_guid,
        companyCode: data.bl_fi_cash_document_hdr.comp_code,
        branchGuid: data.bl_fi_cash_document_hdr.branch_guid,
        branchCode: data.bl_fi_cash_document_hdr.branch_code,
        trasnferType: data.bl_fi_cash_document_hdr.transfer_type,
        description: data.bl_fi_cash_document_hdr.remarks,
        refNo: data.bl_fi_cash_document_hdr.ref_no,
        status: data.bl_fi_cash_document_hdr.status,
        txnDate: data.bl_fi_cash_document_hdr.date_txn,
        currency: data.bl_fi_cash_document_hdr.ccy_code,
      }

      payload.lines = lines
      payload.lines = payload.lines.map(line => {
        return {
          ...line,
          date_txn: data.bl_fi_cash_document_hdr.date_txn,
        }
      })
    }

    return payload;
  }

  calculateOpenAmount(lines) {
    const sum = lines?.reduce((total, line) => total + (line.amount || 0), 0) || 0;
    return Math.round(sum * 100) / 100;
  }

  validateOpenAmount(payload) {
    const openAmount = this.calculateOpenAmount(payload.lines);
    if (openAmount === 0) return true;
    this.viewColFacade.showErrorWithCustomMsg("Total doc open amount need to be zero");
    return false;
  }

  showClone() {
    return !this.HIDE_CLONE_BUTTON;
  }

  clone() {
    this.store.dispatch(CashTransferActions.cloneDocumentInit({}));
  }

  onFinal() {
    const payload = this.preparePayload();

    if (payload.lines && this.validateOpenAmount(payload)) {
      this.store.dispatch(CashTransferActions.editCashTransferAndFinalInit({ payload }));
      this.onReturn();
    } else if (!payload.lines) {
      this.store.dispatch(CashTransferActions.editCashTransferAndFinalInit({ payload }));
      this.onReturn();
    }
  }

  onSave() {
    let payload = this.preparePayload();
    if(this.appletSettings.SAVE_CASH_TRANSFER_DETAILS && this.postingStatus === 'DRAFT') {
      let hdr;
      let lines;
      this.store.select(CashTransferSelectors.selectCashTransfer).pipe(take(1)).subscribe((data:any) => {
        localStorage.setItem('cashTransferMainDetails', JSON.stringify(data.bl_fi_cash_document_hdr));
        hdr = data;
      });
      this.store.select(CashTransferSelectors.selectCashTransferLines).pipe(take(1)).subscribe((data:any) => {
        localStorage.setItem('cashTransferLineListing', JSON.stringify(data));
        lines = data;
      });
      payload = this.preparePayload(hdr, lines)
    }
    if (payload.lines && this.validateOpenAmount(payload)) {
      this.store.dispatch(CashTransferActions.editCashTransferInit({ payload }));
      this.onReturn();
    } else if (!payload.lines) {
      this.store.dispatch(CashTransferActions.editCashTransferInit({ payload }));
      this.onReturn();
    }
  }

   onReturn() {
    this.viewColFacade.updateInstance<LocalState>(this.prevIndex, {
      ...this.prevLocalState,
      deactivateAdd: false,
      deactivateList: false
    });
    if (this.orientation) {
      this.savePanelState();
      this.store.dispatch(CashTransferActions.resetExpansionPanel({ resetIndex: null }));
    }
    this.viewColFacade.onPrev(this.prevIndex);
  }

  goToAddTransferLine() {
    if (this.orientation) {
      this.expandedPanelIndex = 1;
      this.savePanelState();
    }
    // this.store.dispatch(ReceivingDocActions.selectMode({ mode: 'edit' }))
    this.viewColFacade.updateInstance(this.index, {
      ...this.localState,
      deactivateAdd: true,
      deactivateReturn: true,
      deactivateList: true
    });
    this.viewColFacade.onNextAndReset(this.index, 3);
  }

  disableSave() {
    // return this.main?.form.invalid ;
  }

   ngOnDestroy() {
    if (this.matTab) {
      this.viewColFacade.updateInstance<LocalState>(this.index, {
        ...this.localState,
        selectedIndex: this.matTab.selectedIndex,
      });
    }
    if (this.orientation) {
      this.savePanelState();
      this.store.dispatch(CashTransferActions.resetExpansionPanel({ resetIndex: null }));
    }
    this.subs.unsubscribe();
  }

}
