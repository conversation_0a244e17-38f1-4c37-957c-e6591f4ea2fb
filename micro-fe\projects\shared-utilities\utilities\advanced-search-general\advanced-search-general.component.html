<mat-form-field #trigger="cdkOverlayOrigin" cdkOverlayOrigin appearance="outline" class="search-box" (click)="searchToggle()">
  <mat-label>Search...</mat-label>
  <mat-icon matPrefix>search</mat-icon>
  <input #basic matInput type="text" (keyup.enter)="basicSearch(basic.value)"/>
  <div role="group" fxLayout="row wrap" matSuffix *ngIf="showAdv">
    <div matSuffix>
      <button *ngIf="showAdv" class="blg-small-button-icon" mat-icon-button (click)="searchToggle()">
        <mat-icon>filter_list</mat-icon>
      </button>
    </div>
  </div>
</mat-form-field>

<ng-template cdkConnectedOverlay [cdkConnectedOverlayOrigin]="trigger" [cdkConnectedOverlayOpen]="isAdvanced">
  <div #container [id]="id" class="advanced-search-container">
    <form [formGroup]="advForm" (ngSubmit)="advancedSearch()">
      <div class="advanced-search-form" fxLayout="column" fxFlexAlign="space-between center">
        <ng-container *ngFor="let x of inputArray">
          <div [ngSwitch]="x[1]">
            <div fxLayout="row" fxLayoutAlign="start center" fxLayoutGap="10px">
              <mat-checkbox *ngIf="isCheckbox(x)" formControlName="{{x[0]}}Checkbox"></mat-checkbox>
              <label *ngSwitchCase="'label'">{{advSearchModel.label[x[0]]}}</label>
              <mat-form-field *ngSwitchCase="'string'" appearance="outline" type="text">
                <mat-label>{{advSearchModel.label[x[0]]}}</mat-label>
                <input matInput [formControl]="advForm.controls[x[0]]" [required]="isRequired(x)"/>
              </mat-form-field>

              <mat-form-field *ngSwitchCase="'number'" appearance="outline">
                <mat-label>{{advSearchModel.label[x[0]]}}</mat-label>
                <input matInput [formControl]="advForm.controls[x[0]]" type="number" [required]="isRequired(x)"/>
              </mat-form-field>

              <mat-form-field *ngSwitchCase="'select'" appearance="outline">
                <mat-label>{{advSearchModel.label[x[0]]}}</mat-label>
                <mat-select [placeholder]="advSearchModel.label[x[0]]" [formControl]="advForm.controls[x[0]]" [required]="isRequired(x)" [multiple]="isMultiple(x)">
                  <mat-option>
                    <ngx-mat-select-search [id]="x[0]" (keyup)="optionSearchFilter($event)"
                      [placeholderLabel]="advSearchModel.label[x[0]]" [noEntriesFoundLabel]="'No matching records found'"
                      [showToggleAllCheckbox]="true"
                      [toggleAllCheckboxIndeterminate]="isIndeterminate"
                      [toggleAllCheckboxChecked]="isChecked"
                      (toggleAll)="toggleSelectAll($event, x[0])"
                      [(ngModel)]='filter' [ngModelOptions]="{standalone: true}">
                    </ngx-mat-select-search>
                  </mat-option>
                  <mat-option *ngFor="let item of optionArray[x[0]]" [value]="item">
                    {{ item }}
                  </mat-option>
                </mat-select>
              </mat-form-field>

              <mat-form-field *ngSwitchCase="'selectDateOption'" appearance="outline">
                <mat-label>{{advSearchModel.label[x[0]]}}</mat-label>
                <mat-select [placeholder]="advSearchModel.label[x[0]]" [formControl]="advForm.controls[x[0]]" [required]="isRequired(x)" (selectionChange)="onDateOptionChange($event)">
                  <mat-option value="DAY">DAY</mat-option>
                  <mat-option value="MONTH">MONTH</mat-option>
                  <!-- <mat-option value="QUARTER">QUARTER</mat-option> -->
                </mat-select>
              </mat-form-field>

              <mat-form-field *ngSwitchCase="'selectSortBy'" appearance="outline">
                <mat-label>{{advSearchModel.label[x[0]]}}</mat-label>
                <mat-select [placeholder]="advSearchModel.label[x[0]]" [formControl]="advForm.controls[x[0]]" [required]="isRequired(x)" (selectionChange)="onDateOptionChange($event)">
                  <mat-option value="DATE_TXN">TRANSACTION DATE</mat-option>
                  <mat-option value="CREATED_DATE">CREATED DATE</mat-option>
                </mat-select>
              </mat-form-field>

              <!-- Period Selector -->
              <mat-form-field appearance="outline" *ngSwitchCase="'date'" fxFlex="20">
                <mat-label>Select Period</mat-label>
                <mat-select [(value)]="selectedPeriodMap[x[0]]" (selectionChange)="onPeriodChange(x[0])">
                  <mat-option value="day">Day</mat-option>
                  <mat-option value="week">Week</mat-option>
                  <mat-option value="month">Month</mat-option>
                </mat-select>
              </mat-form-field>

              <!-- Day Selection -->
              <div *ngSwitchCase="'date'" fxFlex="80" fxLayout="row wrap">
                <div *ngIf="!selectedPeriodMap[x[0]] || selectedPeriodMap[x[0]] === 'day'" fxFlex="100%" fxLayout="row wrap" fxLayoutGap="4%">
                  <mat-form-field appearance="outline" fxFlex="48%">
                    <mat-label>{{advSearchModel.label[x[0]]}} From</mat-label>
                    <input matInput [matDatepicker]="start_modified_range" [required]="isRequired(x)"
                      [formControl]="advForm.controls[x[0]].controls.from" readonly (click)="start_modified_range.open()" (dateChange)="onFromDateChange(x[0])" />
                    <mat-datepicker-toggle matSuffix [for]="start_modified_range"></mat-datepicker-toggle>
                    <mat-datepicker touchUi="true" #start_modified_range></mat-datepicker>
                  </mat-form-field>
                  <mat-form-field appearance="outline" fxFlex="48%">
                    <mat-label>{{advSearchModel.label[x[0]]}} To</mat-label>
                    <input matInput [min]="minToDateMap[x[0]]" [matDatepicker]="end_modified_range" [required]="isRequired(x)"
                      [formControl]="advForm.controls[x[0]].controls.to" readonly (click)="end_modified_range.open()" />
                    <mat-datepicker-toggle matSuffix [for]="end_modified_range"></mat-datepicker-toggle>
                    <mat-datepicker touchUi="true" #end_modified_range></mat-datepicker>
                  </mat-form-field>
                </div>

                <!-- Week Selection -->
                <div *ngIf="selectedPeriodMap[x[0]] === 'week'" fxFlex="100%" fxLayout="row wrap" fxLayoutGap="4%">
                  <app-week-picker
                    [id]="'week-picker-date-from'"
                    [label]="advSearchModel.label[x[0]] + ' From (Week)'"
                    [date]="advForm.controls[x[0]].controls.from"
                    [options]="advSearchModel.options[x[0]]"
                    (dateChanged)="onWeekPickerChange(x[0])"
                    fxFlex="48%">
                  </app-week-picker>
                  <app-week-picker
                    [id]="getInputId(advSearchModel.label[x[0]],  'to')"
                    [label]="advSearchModel.label[x[0]] + ' To (Week)'"
                    [date]="advForm.controls[x[0]].controls.to"
                    [options]="advSearchModel.options[x[0]]"
                    [min]="minToDateMap[x[0]]"
                    (dateChanged)="onWeekPickerChange(x[0])"
                    fxFlex="48%">
                  </app-week-picker>
                </div>

                <!-- Month Selection -->
                <div *ngIf="selectedPeriodMap[x[0]] === 'month'" fxFlex="100%" fxLayout="row wrap" fxLayoutGap="4%">
                  <app-month-year-picker [label]="advSearchModel.label[x[0]] + ' From (Month)'"
                    [date]="advForm.controls[x[0]].controls.from" [options]="advSearchModel.options[x[0]]"
                    (dateChanged)="onMonthPickerChange(x[0])" fxFlex="48%">
                  </app-month-year-picker>
                  <app-month-year-picker [label]="advSearchModel.label[x[0]] + ' To (Month)'"
                    [date]="advForm.controls[x[0]].controls.to" [options]="advSearchModel.options[x[0]]" [min]="minToDateMap[x[0]]"
                    (dateChanged)="onMonthPickerChange(x[0])" fxFlex="48%">
                  </app-month-year-picker>
                </div>
              </div>

              <mat-form-field appearance="outline" *ngSwitchCase="'item-range'" fxFlex="50">
                <mat-label>{{advSearchModel.label[x[0]]}} From</mat-label>
                <input matInput [required]="isRequired(x)" [formControl]="advForm.controls[x[0]].controls.from" />
              </mat-form-field>
              <mat-form-field appearance="outline" *ngSwitchCase="'item-range'" fxFlex="50">
                <mat-label>{{advSearchModel.label[x[0]]}} To</mat-label>
                <input matInput [required]="isRequired(x)" [formControl]="advForm.controls[x[0]].controls.to" />
              </mat-form-field>

              <mat-form-field *ngSwitchCase="'select-date'" appearance="outline">
                <mat-label>{{advSearchModel.label[x[0]]}}</mat-label>
                <input matInput [matDatepicker]="select_date_range" [required]="isRequired(x)"
                  [formControl]="advForm.controls[x[0]]" readonly (click)="select_date_range.open()" />
                <mat-datepicker-toggle matSuffix [for]="select_date_range"></mat-datepicker-toggle>
                <mat-datepicker touchUi="true" #select_date_range></mat-datepicker>
              </mat-form-field>

              <app-month-year-picker *ngSwitchCase="'month'" [label]="advSearchModel.label[x[0]] + ' From'" [date]="advForm.controls[x[0]].controls.from" [options]="advSearchModel.options[x[0]]" (dateChanged)="onMonthPickerChange(x[0], 'from')" fxFlex="50"></app-month-year-picker>
              <app-month-year-picker *ngSwitchCase="'month'" [label]="advSearchModel.label[x[0]] + ' To'" [date]="advForm.controls[x[0]].controls.to" [options]="advSearchModel.options[x[0]]" [min]="minToDateMap[x[0]]" (dateChanged)="onMonthPickerChange(x[0], 'to')" fxFlex="50"></app-month-year-picker>

              <app-month-year-picker *ngSwitchCase="'select-month'" [label]="advSearchModel.label[x[0]]" [date]="advForm.controls[x[0]]" [options]="advSearchModel.options[x[0]]" fxFlex="100%"></app-month-year-picker>

              <div *ngSwitchCase="'numberRange'" fxFlex="100%" fxLayout="row wrap" fxLayoutGap="4%">
                <mat-form-field fxFlex="48%" appearance="outline">
                  <mat-label>{{advSearchModel.label[x[0]]}} Start Range</mat-label>
                  <input matInput [formControl]="advForm.controls[x[0]].controls.from" [required]="isRequired(x)"/>
                </mat-form-field>
                <mat-form-field fxFlex="48%" appearance="outline">
                  <mat-label>{{advSearchModel.label[x[0]]}} End Range</mat-label>
                  <input matInput [formControl]="advForm.controls[x[0]].controls.to" [required]="isRequired(x)"/>
                </mat-form-field>
              </div>

                <app-select-multi-company-drop-down
                  *ngSwitchCase="'select-multi-company'"
                  [readPermissionDefintion]="readPermissionDefintion?.company"
                  [apiVisa]="apiVisa"
                  [company]="advForm.controls[x[0]]"
                  (companySelected)="onCompanySelected($event)"
                  [label]="advSearchModel.label[x[0]]"
                  [options]="advSearchModel.options[x[0]]"
                  fxFlex="100">
                </app-select-multi-company-drop-down>

                  <app-select-multi-branch-drop-down
                    *ngSwitchCase="'select-multi-branch'"
                    [readPermissionDefintion]="readPermissionDefintion?.branch"
                    [apiVisa]="apiVisa"
                    [branch]="advForm.controls[x[0]]"
                    [companyGuidList]="companyGuidList"
                    [label]="advSearchModel.label[x[0]]"
                    [options]="advSearchModel.options[x[0]]"
                    fxFlex="100">
                  </app-select-multi-branch-drop-down>

              <app-select-multi-location-drop-down *ngSwitchCase="'select-multi-location'" [readPermissionDefintion]="readPermissionDefintion?.location" [apiVisa]="apiVisa" [location]="advForm.controls[x[0]]" [companyGuidList]='companyGuidList'
                [label]="advSearchModel.label[x[0]]" [options]="advSearchModel.options[x[0]]" fxFlex="100"></app-select-multi-location-drop-down>

              <app-select-multi-location-drop-down *ngSwitchCase="'select-multi-location-to'" [readPermissionDefintion]="readPermissionDefintion?.locationTo" [apiVisa]="apiVisa" [location]="advForm.controls[x[0]]" [companyGuidList]='companyGuidList'
                [label]="advSearchModel.label[x[0]]" [options]="advSearchModel.options[x[0]]" fxFlex="100"></app-select-multi-location-drop-down>
 
              <app-select-multi-entity-drop-down *ngSwitchCase="'select-multi-entity'" [apiVisa]="apiVisa" [entity]="advForm.controls[x[0]]"
                [label]="advSearchModel.label[x[0]]" [options]="advSearchModel.options[x[0]]" fxFlex="100"></app-select-multi-entity-drop-down>

              <app-select-multi-customer-drop-down *ngSwitchCase="'select-multi-customer'" [apiVisa]="apiVisa" [customer]="advForm.controls[x[0]]"
                [label]="advSearchModel.label[x[0]]" [options]="advSearchModel.options[x[0]]" fxFlex="100"></app-select-multi-customer-drop-down>

              <app-select-multi-vehicle-drop-down *ngSwitchCase="'select-multi-vehicle'" [apiVisa]="apiVisa" [vehicle]="advForm.controls[x[0]]"
                [label]="advSearchModel.label[x[0]]" [options]="advSearchModel.options[x[0]]" fxFlex="100"></app-select-multi-vehicle-drop-down>

              <app-select-multi-item-category-level-drop-down *ngSwitchCase="'select-multi-item-category-level'" [apiVisa]="apiVisa" [itemCategoryLevel]="advForm.controls[x[0]]"
                [label]="advSearchModel.label[x[0]]" [options]="advSearchModel.options[x[0]]" fxFlex="100"></app-select-multi-item-category-level-drop-down>

              <app-select-multi-glcode-drop-down *ngSwitchCase="'select-multi-glcode'" [apiVisa]="apiVisa" [glCode]="advForm.controls[x[0]]"
                [label]="advSearchModel.label[x[0]]" [options]="advSearchModel.options[x[0]]" fxFlex="100"></app-select-multi-glcode-drop-down>

              <app-select-multi-device-drop-down *ngSwitchCase="'select-multi-device'" [apiVisa]="apiVisa" [device]="advForm.controls[x[0]]"
                [label]="advSearchModel.label[x[0]]" [options]="advSearchModel.options[x[0]]" fxFlex="100"></app-select-multi-device-drop-down>

              <app-select-multi-glcategory-drop-down *ngSwitchCase="'select-multi-glcategory'" [apiVisa]="apiVisa" [glCategory]="advForm.controls[x[0]]"
                [label]="advSearchModel.label[x[0]]" [options]="advSearchModel.options[x[0]]" fxFlex="100"></app-select-multi-glcategory-drop-down>

              <app-select-multi-glsection-drop-down *ngSwitchCase="'select-multi-glsection'" [apiVisa]="apiVisa" [glSection]="advForm.controls[x[0]]"
                [label]="advSearchModel.label[x[0]]" [options]="advSearchModel.options[x[0]]" fxFlex="100"></app-select-multi-glsection-drop-down>

              <app-select-multi-customer-category-drop-down *ngSwitchCase="'select-multi-customer-category'" [apiVisa]="apiVisa" [customerCategories]="advForm.controls[x[0]]"
                [label]="advSearchModel.label[x[0]]" [options]="advSearchModel.options[x[0]]" fxFlex="100"></app-select-multi-customer-category-drop-down>

              <app-select-multi-employee-drop-down *ngSwitchCase="'select-multi-employee'" [apiVisa]="apiVisa" [employee]="advForm.controls[x[0]]"
                [label]="advSearchModel.label[x[0]]" [options]="advSearchModel.options[x[0]]" fxFlex="100"></app-select-multi-employee-drop-down>

              <app-select-multi-location-label-drop-down *ngSwitchCase="'select-multi-location-label'" [apiVisa]="apiVisa" [locationLabel]="advForm.controls[x[0]]"
                [label]="advSearchModel.label[x[0]]" [options]="advSearchModel.options[x[0]]" fxFlex="100"></app-select-multi-location-label-drop-down>

              <app-select-multi-cashbook-drop-down *ngSwitchCase="'select-multi-cashbook'" [readPermissionDefintion]="readPermissionDefintion" [apiVisa]="apiVisa" [cashbook]="advForm.controls[x[0]]"
                [label]="advSearchModel.label[x[0]]" [options]="advSearchModel.options[x[0]]" fxFlex="100"></app-select-multi-cashbook-drop-down>

              <app-select-multi-tax-code-drop-down *ngSwitchCase="'select-multi-tax-code'" [readPermissionDefintion]="readPermissionDefintion" [apiVisa]="apiVisa" [taxCode]="advForm.controls[x[0]]" [taxType]="'SST-SVC-OUTPUT'"
                [label]="advSearchModel.label[x[0]]" [options]="advSearchModel.options[x[0]]" fxFlex="100"></app-select-multi-tax-code-drop-down>

              <app-select-multi-drop-down *ngSwitchCase="'select-multi-gl-dimension'" [key]= "'gl-dimension'" [apiVisa]="apiVisa" [form]="advForm.controls[x[0]]"
                [label]="advSearchModel.label[x[0]]" [options]="advSearchModel.options[x[0]]" fxFlex="100"></app-select-multi-drop-down>

              <app-select-multi-drop-down *ngSwitchCase="'select-multi-profit-center'" [key]= "'profit-center'" [apiVisa]="apiVisa" [form]="advForm.controls[x[0]]"
                [label]="advSearchModel.label[x[0]]" [options]="advSearchModel.options[x[0]]" fxFlex="100"></app-select-multi-drop-down>

              <app-select-multi-drop-down *ngSwitchCase="'select-multi-segment'" [key]= "'segment'" [apiVisa]="apiVisa" [form]="advForm.controls[x[0]]"
                [label]="advSearchModel.label[x[0]]" [options]="advSearchModel.options[x[0]]" fxFlex="100"></app-select-multi-drop-down>

              <app-select-multi-drop-down *ngSwitchCase="'select-multi-project'" [key]= "'project'" [apiVisa]="apiVisa" [form]="advForm.controls[x[0]]"
                [label]="advSearchModel.label[x[0]]" [options]="advSearchModel.options[x[0]]" fxFlex="100"></app-select-multi-drop-down>

              <app-select-multi-tax-filing-drop-down *ngSwitchCase="'select-multi-tax-filing'" [readPermissionDefintion]="readPermissionDefintion" [apiVisa]="apiVisa" [taxFiling]="advForm.controls[x[0]]"
              [label]="advSearchModel.label[x[0]]" [options]="advSearchModel.options[x[0]]" fxFlex="100"></app-select-multi-tax-filing-drop-down>

              <app-select-multi-inv-item-drop-down *ngSwitchCase="'select-multi-inv-item'" [apiVisa]="apiVisa" fxFlex="100" 
              [invItem]="advForm.controls[x[0]]" [label]="advSearchModel.label[x[0]]" [options]="advSearchModel.options[x[0]]">
              </app-select-multi-inv-item-drop-down>

              <app-select-multi-fi-item-drop-down *ngSwitchCase="'select-multi-fi-item'" [apiVisa]="apiVisa" fxFlex="100"
              [fiItem]="advForm.controls[x[0]]" [label]="advSearchModel.label[x[0]]" [options]="advSearchModel.options[x[0]]">
              </app-select-multi-fi-item-drop-down>

            </div>
        </div>
        </ng-container>
        <div [ngSwitch]="advForm.controls.dateOption.value" *ngIf="advForm.controls.dateOption">

          <!-- Day selection -->
          <div *ngSwitchCase="'DAY'">
            <!-- Add the day picker controls -->
            <mat-form-field appearance="outline" fxFlex="50">
              <mat-label>{{advSearchModel.label.dateFrom}}</mat-label>
              <input matInput [matDatepicker]="start_day_picker" [formControl]="advForm.controls.date.controls.from" readonly (click)="start_day_picker.open()" />
              <mat-datepicker-toggle matSuffix [for]="start_day_picker"></mat-datepicker-toggle>
              <mat-datepicker touchUi="true" #start_day_picker></mat-datepicker>
            </mat-form-field>
            <mat-form-field appearance="outline" fxFlex="50">
              <mat-label>{{advSearchModel.label.dateTo}}</mat-label>
              <input matInput [matDatepicker]="end_day_picker" [formControl]="advForm.controls.date.controls.to" readonly (click)="end_day_picker.open()" />
              <mat-datepicker-toggle matSuffix [for]="end_day_picker"></mat-datepicker-toggle>
              <mat-datepicker touchUi="true" #end_day_picker></mat-datepicker>
            </mat-form-field>
          </div>

          <!-- Month selection -->
          <div *ngSwitchCase="'MONTH'">
            <app-month-year-picker [label]="advSearchModel.label.monthFrom" [date]="advForm.controls.date.controls.from" fxFlex="50"></app-month-year-picker>
            <app-month-year-picker [label]="advSearchModel.label.monthTo" [date]="advForm.controls.date.controls.to" fxFlex="50"></app-month-year-picker>
          </div>

          <!-- Quarter selection -->
          <mat-form-field appearance="outline" *ngSwitchCase="'quarter'" fxFlex="50">
            <mat-label>{{advSearchModel.label.quarterFrom}}</mat-label>
            <mat-select [formControl]="advForm.controls['quarterFrom']" placeholder="Select Quarter From">
              <mat-option *ngFor="let quarter of quarterOptions" [value]="quarter">{{ quarter }}</mat-option>
            </mat-select>
          </mat-form-field>

          <mat-form-field appearance="outline" *ngSwitchCase="'quarter'" fxFlex="50">
            <mat-label>{{advSearchModel.label.quarterTo}}</mat-label>
            <mat-select [formControl]="advForm.controls['quarterTo']" placeholder="Select Quarter To">
              <mat-option *ngFor="let quarter of quarterOptions" [value]="quarter">{{ quarter }}</mat-option>
            </mat-select>
          </mat-form-field>
        </div>
      
        <div fxLayout="row" fxLayoutAlign="space-between center" fxLayoutGap="10px">
          <div fxLayout="row" fxLayoutAlign="start center" fxLayoutGap="5px">
            <mat-form-field appearance="standard" class="custom-standard-select" >
              <mat-select [(value)]="selectedLabel" (selectionChange)="onLabelChange($event.value)">
                <mat-option *ngFor="let label of labels" [value]="label">{{ label }}</mat-option>
              </mat-select>
            </mat-form-field>
            <button color="primary" class="blg-small-button-icon" mat-icon-button type="button" (click)="openAddDialog()">
              <mat-icon>add</mat-icon>
            </button>
            <button color="warn" class="blg-small-button-icon" mat-icon-button type="button" (click)="openDeleteDialog()" [disabled]="!selectedLabel">
              <mat-icon>remove</mat-icon>
            </button>
          </div>
          <div fxLayout="row" fxLayoutAlign="end center" fxLayoutGap="10px">
            <button color="basic" mat-raised-button type="button" (click)="reset()">
              RESET
            </button>
            <button color="primary" mat-raised-button type="submit">
              SEARCH
            </button>
          </div>
        </div>
      </div>
    </form>
  </div>

  <!-- Add Dialog -->
  <ng-template #addDialog>
    <h1 mat-dialog-title>Add New State</h1>
    <div mat-dialog-content>
      <mat-form-field appearance="fill">
        <mat-label>State</mat-label>
        <input matInput [(ngModel)]="newLabel" />
      </mat-form-field>
    </div>
    <div mat-dialog-actions align="end">
      <button mat-raised-button (click)="closeAddDialog()">Cancel</button>
      <button mat-raised-button color="primary" [disabled]="!newLabel.trim()" (click)="confirmAdd()">Add</button>
    </div>
  </ng-template>

  <!-- Delete Dialog -->
  <ng-template #deleteDialog>
    <h1 mat-dialog-title>Remove State</h1>
    <div mat-dialog-content>
      Are you sure you want to delete "<strong>{{ selectedLabel }}</strong>"?
    </div>
    <div mat-dialog-actions align="end">
      <button mat-button (click)="closeDeleteDialog(false)">No</button>
      <button mat-button color="warn" (click)="closeDeleteDialog(true)">Yes</button>
    </div>
  </ng-template>
</ng-template>
