import { D, E } from "@angular/cdk/keycodes";
import { ChangeDetectionStrategy, Component, ViewChild } from "@angular/core";
import { ComponentStore } from '@ngrx/component-store';
import { Store } from '@ngrx/store';
import { ApiResponseModel, FinancialItemContainerModel, FinancialItemPricingStockBalanceService, FinancialItemService, Pagination, PricingSchemeLinkService, SubQueryService } from "blg-akaun-ts-lib";
import moment from "moment";
import { ToastrService } from "ngx-toastr";
import { ViewColSelectors } from "projects/shared-utilities/application-controller/store/selectors";
import { AppStates } from 'projects/shared-utilities/application-controller/store/states';
import { SessionSelectors } from "projects/shared-utilities/modules/session/session-controller/selectors";
import { SessionStates } from "projects/shared-utilities/modules/session/session-controller/states";
import { UtilitiesModule } from "projects/shared-utilities/utilities/utilities.module";
import { ViewColumnComponent } from 'projects/shared-utilities/view-column.component';
import { fork<PERSON>oin, from, iif, Observable, of, zip } from 'rxjs';
import { catchError, concatMap, filter, map, mergeMap, switchMap, tap, toArray, withLatestFrom } from 'rxjs/operators';
import { SubSink } from "subsink2";
import { SearchQueryModel } from "../../../../../../../../shared-utilities/models/query.model";
import { PaginationComponent } from "../../../../../../../../shared-utilities/utilities/pagination/pagination.component";
import { AppConfig } from "../../../../../../../../shared-utilities/visa";
import { ViewColumnFacadeItem } from "../../../facades/view-column-item.facade";
import { ViewColumnFacade } from '../../../facades/view-column.facade';
import { FISearchModel } from "../../../models/advanced-search-models";
import { ItemSearchQueryModel } from "../../../models/pos.model";
import { DefaultSelectors, MainSelectors } from "../../../state-controllers/draft-controller/store/selectors";
import { DraftStates } from "../../../state-controllers/draft-controller/store/states";
import { PosActions } from "../../../state-controllers/pos-controller/store/actions";
import { PosSelectors } from "../../../state-controllers/pos-controller/store/selectors";
import { PosStates } from "../../../state-controllers/pos-controller/store/states";
import { PaginationV2Component } from "projects/shared-utilities/utilities/pagination-v2/pagination-v2.component";
import { ApiService } from "../../../services/api-service";
import { FIStockSearchModel } from "../../../models/advanced-search-models/fi-stock.model";
import { Column4ViewModelActions } from "../../../state-controllers/view-model-controller/actions";
import { ColumnViewModelStates } from "../../../state-controllers/view-model-controller/states";
import { UtilitiesModulePos } from "../../utilities/utilities.module";
import { POSDexieService } from "../../../services/pos-dexie.service";
import { ClientSidePermissionsSelectors } from "projects/shared-utilities/modules/permission/client-side-permissions-controller/selectors";
import { ClientSidePermissionStates } from "projects/shared-utilities/modules/permission/client-side-permissions-controller/states";
import { PosCustomTooltipComponent } from "../../utilities/tooltip/custom-tooltip.component";
import { FormControl, FormGroup } from '@angular/forms';
export interface LocalState {
  deactivateAdd: boolean;
  deactivateList: boolean;
  deactivateReturn: boolean;
  selectedIndex: number;
}
export const FIStockSearchModelV2: ItemSearchQueryModel = {

  label: {
      keyword: 'Has the words',
      itemType: 'Item Type',
  },

  dataType: {
      keyword: 'string',
      itemType: ['select', ['BASIC_ITEM', 'MADE_TO_ORDER', 'BUNDLE','NSTI','VOUCHER','ACCOUNT_CODE','SERVICE', 'WARRANTY','MEMBERSHIP']],
  },

  form: new FormGroup({
      keyword: new FormControl(),
      itemType: new FormControl(),
  }),


  creationDate:'',
  itemType:'',
  stockBal:'',
  query: '',
  isBasic: '',
  keyword: ''

};
@Component({
  selector: 'app-line-search-item-stock',
  templateUrl: './line-search-item-stock.component.html',
  styleUrls: ['./line-search-item-stock.component.css'],
  providers: [ComponentStore]
})

export class LineSearchItemStockComponent extends ViewColumnComponent {
  itemTypeOptions: string[] = FIStockSearchModelV2.dataType.itemType[1];
  filteredItemTypeOptions: string[] = [...this.itemTypeOptions];
  protected readonly index = 11;
  localState$;
  gridApi;
  gridColumnApi;
  gridOptions = {

    defaultColDef: {
        filter: 'agTextColumnFilter',
        floatingFilter: false,
        sortable: true,
        resizable: true,
        enableRowGroup: true,
        enablePivot: true,
        //wrapText: true,
        //autoHeight: true,
        //suppressCsvExport: true,
        //suppressSizeToFit: false,
    },
    pagination: true,
    animateRows: true,
    sideBar: true,
    rowSelection: 'single',
    //rowGroupPanelShow: 'always',
    suppressRowClickSelection: false,
    //autoGroupColumnDef: this.groupColumn,
    onPaginationChanged: (params) => {
      if (params.newPage) {
        let currentPage = params.api.paginationGetCurrentPage();

        localStorage.setItem('currentPageGLCode', JSON.stringify(currentPage));
      }
    },
  };
  frameworkComponents = {
    PosCustomTooltipComponent: PosCustomTooltipComponent
};


  toggleColumn$: Observable<boolean>;
  rowData = [];
  totalRecords = 0;
  sqlGuids: string[] = null;
  limit = 50;
  coaMap: {guid: string, name: string}[] = [];


  private localState: LocalState;
  readonly deactivateAdd$ = this.componentStore.select(
    (state) => state.deactivateAdd
  );
  readonly deactivateList$ = this.componentStore.select(
    (state) => state.deactivateList
  );

  keyword;
  main$ = this.draftStore.select(MainSelectors.selectMain);
  compGuid;
  branchGuid;
  locationGuid;
  pricingHdrGuid;
  taxApplicable = false;
  roundingItemCode;
  employeeDiscount = 0;
  isReturn = false;
  isTradeIn = false;
  searchModel = FIStockSearchModelV2;
  filterKeyword;
  isMember;
  txnTypeFilter='';
  private subs = new SubSink();
  @ViewChild(PaginationV2Component, { static: false })
  private paginationComponent: PaginationV2Component;
  columnsDefs;
  totalRecords$ = this.store.select(PosSelectors.selectTotalRecordsItem);
  filterCategory0;
  filterCategory1;
  filterCategory2;
  filterCategory3;
  filterCategory4;
  filterCategory5;
  filterCategory6;
  filterCategory7;
  filterCategory8;
  filterCategory9;
  filterCategory10;
  offlineMode = false;
  button$ = this.store.select(PosSelectors.selectBackButton);
  myImage;
  EXCLUDE_ACCOUNT_CODE_ITEM_TYPE_AT_ITEM_SEARCH:boolean = false;
  clientSidePermissions$ = this.permissionStore.select(
    ClientSidePermissionsSelectors.selectAll
  );
  tradeInBasicItem = false;
  posTheme ="LEFT_RIGHT";
  mode;
  mode$ = this.store.select(PosSelectors.selectMode);
  DISABLE_ITEM_LISTING = false;
  viewColFacade;
  appletSettings;
  constructor(
    private viewColFacadeMain: ViewColumnFacade,
    private readonly viewModelStore: Store<ColumnViewModelStates>,
    private apiService: ApiService,
    protected readonly permissionStore: Store<ClientSidePermissionStates>,
    protected posDexieService: POSDexieService,
    private subQueryService: SubQueryService,
    private readonly store: Store<PosStates>,
    private readonly appstore: Store<AppStates>,
    private viewColFacadeItem: ViewColumnFacadeItem,
    private pricingLinkService: PricingSchemeLinkService,
    private readonly componentStore: ComponentStore<LocalState>,
    private readonly sessionStore: Store<SessionStates>,
    private toastr: ToastrService,
    private readonly draftStore: Store<DraftStates>,
  ) {
    super();
    this.viewColFacade = this.viewColFacadeItem;
    this.toggleColumn$ = this.viewColFacade.toggleColumn$;

    this.subs.sink = this.sessionStore.select(SessionSelectors.selectMasterSettings).subscribe({next: (resolve: any) => {
      this.appletSettings  = resolve;
      this.posTheme = resolve?.POS_THEME?resolve.POS_THEME:"LEFT_RIGHT";
      if(this.posTheme==='TOP_BOTTOM'){
        this.viewColFacade = this.viewColFacadeMain;
        this.localState$ = this.viewColFacade.selectLocalState(30);
      }else{
        this.viewColFacade = this.viewColFacadeItem;
        this.localState$ = this.viewColFacade.selectLocalState(this.index);
      }
      this.columnsDefs = [
        {headerName: 'Code', field: 'financialItemContainer.bl_fi_mst_item_hdr.code', width: 150, cellStyle: () => ({'text-align': 'left'})},
        {headerName: 'Name', field: 'financialItemContainer.bl_fi_mst_item_hdr.name', width: 200, cellStyle: () => ({'text-align': 'left'}),
        cellRendererFramework: PosCustomTooltipComponent},
        // {headerName: 'UOM', field: 'item.bl_fi_mst_item_hdr.uom', width: 20, cellStyle: () => ({'text-align': 'left'})},
        {headerName: 'Price', width: 100,field: 'unitPrice', cellStyle: () => ({'text-align': 'right'}),
        type: 'numericColumn',
        cellRenderer: (param) => {
          let price = 0;
          let pricing;

          if(this.defaultPricingList.length){
            const newList = this.defaultPricingList
            .filter(obj1 => param.data.pricingSchemeLinkContainers
              .some(obj2 => obj1.bl_fi_mst_branch_pricing_scheme_hdr_link.guid_pricing_scheme_hdr.toString()
                === obj2.bl_fi_mst_pricing_scheme_link.guid_pricing_scheme_hdr.toString())
                );
            if(newList.length>0){
              const highestLevel = UtilitiesModulePos.findHighestLevel(newList);
              if(highestLevel && highestLevel.length){
                pricing =  param.data.pricingSchemeLinkContainers.find(item =>  item.bl_fi_mst_pricing_scheme_link.guid_pricing_scheme_hdr===highestLevel.bl_fi_mst_branch_pricing_scheme_hdr_link.guid_pricing_scheme_hdr && item.bl_fi_mst_pricing_scheme_link.sales_unit_price>0);
              }

            }

            if(!pricing){
              pricing = param.data.pricingSchemeLinkContainers.find(p => p.bl_fi_mst_pricing_scheme_link.guid_pricing_scheme_hdr===this.pricingHdrGuid);
            }
          }else{
            pricing = param.data.pricingSchemeLinkContainers.find(p => p.bl_fi_mst_pricing_scheme_link.guid_pricing_scheme_hdr===this.pricingHdrGuid);
          }

          if(pricing){
            if(pricing.bl_fi_mst_pricing_scheme_link.sales_promotion_price){
              if(pricing.bl_fi_mst_pricing_scheme_link.valid_date_start && pricing.bl_fi_mst_pricing_scheme_link.valid_date_end){
                const currentDate: Date = new Date();
                let start = new Date(pricing.bl_fi_mst_pricing_scheme_link.valid_date_start);
                let end = new Date(pricing.bl_fi_mst_pricing_scheme_link.valid_date_end);
                if(start && end){
                  start.setHours(0, 0, 0, 0);
                  end.setHours(23, 59, 59, 999);
                }
                const validDate = currentDate >= start && currentDate <= end
                if(validDate){
                  price =pricing.bl_fi_mst_pricing_scheme_link.sales_promotion_price?.toFixed(2);
                }
                else{
                  price = pricing.bl_fi_mst_pricing_scheme_link.sales_unit_price?.toFixed(2);
                }
              }
            }else{
              price = pricing.bl_fi_mst_pricing_scheme_link.sales_unit_price?.toFixed(2);
            }
            return price;
          }else{
            return "0.00";
          }
        }
        },
        {headerName: 'Stock Bal', width: 80,field: 'bal', cellStyle: () => ({'text-align': 'right'}),
        type: 'numericColumn',
        cellRenderer: (param) => {
          const stock = param.data.stockBalanceContainers.find(p => p.bl_inv_current_location_stock_balance.location_guid===this.locationGuid);
          const bundleAvailabilityContainers = param.data.bundleAvailabilityContainers;
          const financialItemContainer = param.data.financialItemContainer;

          let balance = 0;
          if (financialItemContainer.bl_fi_mst_item_hdr.txn_type==='BUNDLE' && bundleAvailabilityContainers.length) {
              const objectWithLowestStock = bundleAvailabilityContainers.reduce((min, current) =>
                  current.stockavailability < min.stockavailability ? current : min
              );
              if(objectWithLowestStock){
                balance = objectWithLowestStock.stockavailability
              }
          }else{
            if(stock){
              balance = stock.bl_inv_current_location_stock_balance.qty_ledger?.toFixed(0);
            }
          }

          return balance;
        }
        },
        {headerName: 'MA Cost', width: 80,field: 'bal', cellStyle: () => ({'text-align': 'right'}),hide: !this.appletSettings.SHOW_MA_COST ,
        type: 'numericColumn',
        cellRenderer: (param) => {
          const stock = param.data.currentCompanyStockBalanceContainers.find(p => p.bl_inv_current_company_stock_balance.comp_guid===this.compGuid);
          if(stock){
            return stock.bl_inv_current_company_stock_balance.cost_ma_price?.toFixed(2);
          }else{
            return "0.00";
          }
        }
        },
        {headerName: 'Type', field: 'financialItemContainer.bl_fi_mst_item_hdr.txn_type', width: 230, cellStyle: () => ({'text-align': 'left'}),
        cellRenderer: (param) => {
         let txnType = param.data.financialItemContainer.bl_fi_mst_item_hdr.txn_type;
         if (txnType === 'COUPON') {
          return 'VOUCHER';
         } else if (txnType === 'GL_CODE') {
          return 'ACCOUNT_CODE';
         }
         return txnType;
        }},
      ];
     }});
  }
  defaultPricingList =[];
  excludeWarranty = false;
  ngOnInit() {
    this.subs.sink = this.button$.subscribe({ next: (resolve: string) => this.myImage = resolve });
    this.subs.sink = this.mode$.subscribe({ next: (resolve: string) => this.mode = resolve });
    this.subs.sink = this.localState$.subscribe( a => {
     this.keyword = a.keyword;

    });
    this.subs.sink = this.main$.pipe(
      withLatestFrom(
        this.sessionStore.select(SessionSelectors.selectMasterSettings),
        this.draftStore.select(DefaultSelectors.selectDefault),
        this.store.select(PosSelectors.selectDefaultBranchPricingScheme),
        this.sessionStore.select(SessionSelectors.selectPersonalSettings),
        this.clientSidePermissions$,
        this.store.select(PosSelectors.selectReturn),
        this.store.select(PosSelectors.selectTradeIn)
        )
    )
    .subscribe({next: ([main,settings,def, defaultPricingList, personal, clientSidePerm, isReturn, isTradeIn]) => {
      this.isReturn = isReturn;
      this.isTradeIn = isTradeIn;
      if(settings?.POS_WARRANTY_ITEM_FOR_MEMBERS_ONLY){
        this.updateItemTypeOptions(main);
        if(!main?.membership){
          this.excludeWarranty = true;
        }else{
          this.excludeWarranty = false;
        }
      }
      this.DISABLE_ITEM_LISTING = settings?.DISABLE_ITEM_LISTING
      this.posTheme = settings?.POS_THEME?settings.POS_THEME:"LEFT_RIGHT";
      clientSidePerm.forEach((permission) => {
        if (permission.perm_code === "EXCLUDE_ACCOUNT_CODE_ITEM_TYPE_AT_ITEM_SEARCH") {
          this.EXCLUDE_ACCOUNT_CODE_ITEM_TYPE_AT_ITEM_SEARCH = true;
        }
      });
      this.tradeInBasicItem =  settings?.TRADE_IN_BASIC_ITEM;
      this.offlineMode = personal?.OFFLINE_MODE;
      this.defaultPricingList = defaultPricingList?defaultPricingList:[];
      this.compGuid = main.compGuid?main.compGuid:def.branch.bl_fi_mst_branch.comp_guid;
      this.branchGuid = main.branchGuid;
      this.locationGuid = main.locationGuid;
      this.pricingHdrGuid = main.pricingGuid??settings.PRICING_RETAIL_GUID;
      //console.log('this.pricingHdrGuid',this.pricingHdrGuid)
      this.taxApplicable = settings?.SHOW_TAX;
      this.roundingItemCode =  settings?.ROUNDING_ITEM_CODE;
      this.employeeDiscount = main.customer.bl_fi_mst_entity_hdr.is_employee && settings?.EMPLOYEE_DISCOUNT;
      this.isMember = main.membership?true:false;
    }});
    this.subs.sink = this.store.select(PosSelectors.selectItemCategoryFilterList).subscribe(settingItem => {
      const levelsToProcess = [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10];

      for (const level of levelsToProcess) {
        const matchingSetting = settingItem.find(s => {
          const settingLevelValue = (s.bl_applet_setting_filter_fi_item_category_link as any)?.level_value;
          const settingGuidBranch = (s.bl_applet_setting_filter_fi_item_category_link as any)?.guid_branch;
          return settingLevelValue == level && settingGuidBranch == this.branchGuid;
        });
          //console.log('matchingSetting',matchingSetting)
          if (matchingSetting) {
              const categoryGuids = (matchingSetting as any).bl_applet_setting_filter_fi_item_category_link.category_json?.guids;
              if(categoryGuids){
                this[`filterCategory${level}`] = categoryGuids.join(", ");
              }
          } else {
              this[`filterCategory${level}`] = "";
          }
      }
    })


      if(this.offlineMode){
        if(!this.DISABLE_ITEM_LISTING){
          this.createDataOffline(this.keyword);
        }
      }else{
        if(this.keyword){
          const query = new ItemSearchQueryModel();
          query.keyword = this.keyword;
          query.isBasic = true;
          this.onSearch(query)
        }else{
          if(!this.DISABLE_ITEM_LISTING){
            this.createData();
          }
        }
      }


  }

  updateItemTypeOptions(main) {
    if(main?.membership){
      this.filteredItemTypeOptions = ['BASIC_ITEM', 'MADE_TO_ORDER', 'BUNDLE','NSTI','VOUCHER','ACCOUNT_CODE','SERVICE', 'WARRANTY','MEMBERSHIP'];
    }else{
      this.filteredItemTypeOptions = this.itemTypeOptions.filter(itemType => itemType !== 'WARRANTY');
    }

    FIStockSearchModelV2.dataType.itemType[1] = this.filteredItemTypeOptions;

  }

  clear() {
    this.gridApi?.setRowData(null);
    this.totalRecords = 0;
    this.rowData = [];
    this.filterKeyword = null;
    this.txnTypeFilter = null;
  }

  onGridReady(params) {
    this.gridApi = params.api;
    this.gridColumnApi = params.columnApi;
    this.gridApi.closeToolPanel();
    this.gridApi.sizeColumnsToFit();
  }

  createData() {
    if(!this.locationGuid){
      return;
    }
    let criteria = [
        { columnName: 'calcTotalRecords', operator: '=', value: 'true' },
        { columnName: 'txn_class', operator: '=', value: 'PNS' },
        { columnName: 'stock_location_guid', operator: '=', value: this.locationGuid },
        { columnName: 'basic_qty_ledger_greater_than', operator: '=', value: '0' },
    ];

    for (let level = 0; level <= 10; level++) {
        const filterCategory = this[`filterCategory${level}`];
        if (filterCategory) {
            criteria.push({ columnName: `category_guids_${level}`, operator: '=', value: filterCategory });
        }
    }
    if (this.isTradeIn) {
      this.txnTypeFilter = !this.tradeInBasicItem ? "NSTI" : "BASIC_ITEM";
    }
    if (this.txnTypeFilter) {
        criteria.push({
            columnName: "txn_type",
            operator: "=",
            value: this.txnTypeFilter
        });
    }
    if (this.filterKeyword) {
        criteria.push({
            columnName: "keyword_search",
            operator: "=",
            value: this.filterKeyword
        });
    }



    const excludeTypes = [];
    if (this.EXCLUDE_ACCOUNT_CODE_ITEM_TYPE_AT_ITEM_SEARCH) {
        excludeTypes.push('GL_CODE');
    }
    if (this.excludeWarranty) {
        excludeTypes.push('WARRANTY');
    }
    if (this.tradeInBasicItem) {
      excludeTypes.push('NSTI');
    }

    if (excludeTypes.length > 0) {
        criteria.push({ columnName: "exclude_txn_types", operator: '=', value: excludeTypes.join(',') });
    }

    const sortCriteria = [
        { columnName: 'orderBy', value: 'code' },
    ];
    const offset = this.sqlGuids ? 0 : this.rowData.length;
    const paging = new Pagination(offset, this.limit, criteria, sortCriteria);

    this.subs.sink = this.apiService.getItemPricingListingByCriteria(paging, AppConfig.apiVisa).pipe().subscribe((resolved: any) => {
      let excludedBundleCount = 0;

      // Filter data to exclude rows with txn_type === 'BUNDLE' and bundleBalance === 0
      let filteredRowData = resolved.data.filter(row => {
          const financialItemContainer = row.financialItemContainer;
          const bundleAvailabilityContainers = row.bundleAvailabilityContainers;

          // Default bundleBalance is 0
          let bundleBalance = 0;

          // Calculate bundleBalance only for BUNDLE type
          if (
              financialItemContainer.bl_fi_mst_item_hdr.txn_type === 'BUNDLE' &&
              bundleAvailabilityContainers.length
          ) {
              const objectWithLowestStock = bundleAvailabilityContainers.reduce((min, current) =>
                  current.stockavailability < min.stockavailability ? current : min
              );
              if (objectWithLowestStock) {
                  bundleBalance = objectWithLowestStock.stockavailability;
              }
          }

          // If txn_type === 'BUNDLE' and bundleBalance === 0, exclude row and increment count
          if (
              financialItemContainer.bl_fi_mst_item_hdr.txn_type === 'BUNDLE' &&
              bundleBalance === 0
          ) {
              excludedBundleCount++;
              return false;
          }

          return true; // Include all other rows
      });

      this.totalRecords = resolved.totalRecords;

      if (excludedBundleCount>0 && filteredRowData.length < resolved.totalRecords - excludedBundleCount) {
          this.totalRecords = filteredRowData.length;
      }
      this.store.dispatch(PosActions.selectTotalRecordsItem({ totalRecordsItem: this.totalRecords }));

      this.rowData = [...this.rowData, ...filteredRowData];

      this.gridApi.setRowData(this.rowData);
      this.gridApi.paginationGoToLastPage();
      this.paginationComponent.totalRecords.next(this.totalRecords);
    }, err => {
        console.error(err);
    });

  }


  onToggle(e: boolean) {
    this.viewColFacade.toggleColumn(e);
  }

  onNext() {
    this.viewColFacade.updateInstance(this.index, {
      ...this.localState, deactivateAdd: true, deactivateList: true
    });
    this.viewColFacade.onNext(1);
  }

  createDataOffline(keyword?: string, txnType?: string) {
    console.log('txnType',txnType);
    const result: any[] = [];
    let  finalFilteredItems;

    this.posDexieService.db.transaction('r',
      this.posDexieService.db.bl_fi_mst_item_hdr,
      this.posDexieService.db.bl_fi_mst_pricing_scheme_link,
      this.posDexieService.db.bl_inv_current_location_stock_balance, async () => {
        // Fetch all items // Build the filter object based on the provided keyword and txnType
        const filter: {
          code?: { startsWithIgnoreCase: string };
          name?: { startsWithIgnoreCase: string };
          txn_type?: string;  // Add the txn_type property to the filter
        } = {};

        if (txnType) {
          filter.txn_type = txnType;
        }
        const offset = this.rowData.length;

        const totalRecords = await this.posDexieService.db.bl_fi_mst_item_hdr.count();
        this.totalRecords = totalRecords;
        this.store.dispatch(PosActions.selectTotalRecordsItem({ totalRecordsItem: this.totalRecords }));

        let query = this.posDexieService.db.bl_fi_mst_item_hdr
        .where('txn_class').equals('PNS');

        if (keyword) {
            const regex = new RegExp(`^${keyword}`, 'i');
            query = query.filter(item => regex.test(item.code));
        }

        if (txnType) {
            const regex = new RegExp(`^${txnType}`, 'i');
            query = query.filter(item => regex.test(item.txn_type));
        }

        const itemList = await query
            .offset(offset)
            .limit(this.limit)
            .toArray();

        const sortedItemList = itemList.sort((a, b) => b.date_updated - a.date_updated);
        await Promise.all(sortedItemList.map(async (item:any) => {
          const itemGuid = item.guid;

          const [pricingData, stockData] = await Promise.all([
            this.posDexieService.db.bl_fi_mst_pricing_scheme_link
              .where('item_hdr_guid')
              .equals(itemGuid)
              .toArray(),
            this.posDexieService.db.bl_inv_current_location_stock_balance
              .where('fi_item_guid')
              .equals(itemGuid)
              .toArray(),
          ])

          result.push({
            financialItemContainer: {
              bl_fi_mst_item_hdr: item
            },
            pricingSchemeLinkContainers: pricingData.map(pricing => ({
              "bl_fi_mst_pricing_scheme_link": pricing
            })),
            stockBalanceContainers: stockData.map(stock => ({
              "bl_inv_current_location_stock_balance": stock
            }))
          });
        }));


      })
      .then(async () => {
        this.rowData = [...this.rowData, ...result];
        //this.totalRecords = this.rowData.length;
        //this.store.dispatch(PosActions.selectTotalRecordsItem({ totalRecordsItem: this.totalRecords }));

        this.gridApi.setRowData(this.rowData);
        //this.gridApi.paginationGoToLastPage();
        this.paginationComponent.totalRecords.next(this.totalRecords);
      })
      .catch(error => {
        console.error('Error retrieve item listing from dexie:', error);
      });
  }


  onSearch(e: any) {
    this.clear();
    if(e.keyword){
      this.filterKeyword = e.keyword;
    }
    if(e.itemType){
      if(e.itemType==="ACCOUNT_CODE"){
        this.txnTypeFilter ="GL_CODE";
      }else if(e.itemType==="VOUCHER"){
        this.txnTypeFilter ="COUPON";
      }
      else{
        this.txnTypeFilter = e.itemType;
      }
    }


    if(this.offlineMode){
      this.createDataOffline( this.filterKeyword,this.txnTypeFilter);
    }else{
      this.store.dispatch(PosActions.selectTotalRecordsItem({ totalRecordsItem: 0 }));
      this.createData();
    }
  }

  onRowClicked(res: any) {
    console.log('onRowClicked', res);
    const pricing = res.pricingSchemeLinkContainers.find(p => p.bl_fi_mst_pricing_scheme_link.guid_pricing_scheme_hdr===this.pricingHdrGuid);
    let unitPrice:any =0;
    if(pricing){
      unitPrice = pricing.bl_fi_mst_pricing_scheme_link.sales_unit_price?.toFixed(2);
    }else{
      unitPrice = "0.00";
    }
    const item = res.financialItemContainer;

    if (item) {
      if(item.bl_fi_mst_item_hdr.txn_type==='GL_CODE' && (!res.glCodeLinkContainers.length && !item.bl_fi_mst_item_hdr.glcode_guid )){
        this.toastr.error("There is no GL Code link to this item", "Error", {
          tapToDismiss: true,
          progressBar: true,
          timeOut: 2000,
        });
      }else{
        item.bl_fi_mst_item_hdr = Object.assign(item.bl_fi_mst_item_hdr, {
          unitPrice: unitPrice
        });
        const salesGlCode = res.glCodeLinkContainers.find(item => item.txn_code === "SALES")?.guid_glcode || null;
        if (salesGlCode) {
          Object.assign(item.bl_fi_mst_item_hdr, { guid_glcode_company_link: salesGlCode });
        }

          let format = "YYYY-MM-DD";
          let end = moment(item.bl_fi_mst_item_hdr.valid_date_end).format(format);
          let start = moment(item.bl_fi_mst_item_hdr.valid_date_start).format(format);

          let d = moment().format(format);
          let canSelect = true;
          if(item.bl_fi_mst_item_hdr.txn_type==='MADE_TO_ORDER'){
            if(moment(d).isBetween(start, end,null, '[]')){
              canSelect = true;
            } else{
              canSelect = false;
              this.toastr.error("MTO not valid for this date", "Error", {
                tapToDismiss: true,
                progressBar: true,
                timeOut: 2000,
              });
            }
          }
          if(item.bl_fi_mst_item_hdr.txn_type==='COUPON'){
            if(item.bl_fi_mst_item_hdr.coupon_hdr_guid){
              this.store.dispatch(PosActions.selectCouponHdr({guid:item.bl_fi_mst_item_hdr.coupon_hdr_guid}));
            }else{
              this.toastr.error("This item code is not associated with any coupon", "Error", {
                tapToDismiss: true,
                progressBar: true,
                timeOut: 2000,
              });
              canSelect  = false;
            }

          }
          if (canSelect){
            this.viewModelStore.dispatch(Column4ViewModelActions.setFIItem({fiItem:item}));
            const stock = res.stockBalanceContainers.find(p => p.bl_inv_current_location_stock_balance.location_guid===this.locationGuid);
            const stockBalance = stock?stock.bl_inv_current_location_stock_balance.qty_ledger?.toFixed(0):0;
            const stockBalJson = {
              guid: item.bl_fi_mst_item_hdr.guid,
              location: this.locationGuid,
              balance: stockBalance
            }
            this.store.dispatch(PosActions.setCurrentBalanceList({bal: stockBalJson}));
            if(item.bl_fi_mst_item_hdr.sub_item_type){
              this.viewModelStore.dispatch(Column4ViewModelActions.setItemDetailsTab_itemType_Value({itemType:item.bl_fi_mst_item_hdr.sub_item_type.toString()}));
            }

            this.store.dispatch(PosActions.resetMTO());
            console.log('item',item)
            this.store.dispatch(PosActions.selectItem({entity:item}));
            this.store.dispatch(PosActions.selectInvItem({fiGuid:item.bl_fi_mst_item_hdr.guid.toString()}));
            if(item.bl_fi_mst_item_hdr.txn_type==='MADE_TO_ORDER'){
              this.store.dispatch(PosActions.selectMTOMasterInit({fiGuid:item.bl_fi_mst_item_hdr.guid.toString()}));
              this.store.dispatch(PosActions.selectMTOSection({fiGuid:item.bl_fi_mst_item_hdr.guid.toString()}));
            }
            //this.store.dispatch(PosActions.selectMTOComponent({fiGuid:item.bl_fi_mst_item_hdr.guid.toString()}));
            this.store.dispatch(PosActions.resetBatch());
            this.store.dispatch(PosActions.resetBin());
            this.store.dispatch(PosActions.resetSerialListing());

            this.store.dispatch(PosActions.selectSegmentLine({segment:null}));
            this.store.dispatch(PosActions.selectProjectLine({project:null}));
            this.store.dispatch(PosActions.selectProfitCenterLine({profitCenter:null}));
            this.store.dispatch(PosActions.selectDimensionLine({dimension:null}));

            let indexAdd = 4;
            if(this.posTheme==='TOP_BOTTOM'){
              indexAdd = 31;
            }else{
              indexAdd = 4;
            }
            this.viewColFacade.updateInstance(indexAdd,
                {
                  itemHdrGuid:  item.bl_fi_mst_item_hdr.guid,
                  txnType: item.bl_fi_mst_item_hdr.txn_type,
                  subItemType: item.bl_fi_mst_item_hdr.sub_item_type,
                  taxApplicable: this.taxApplicable,
                  pricingHdrGuid: this.pricingHdrGuid,
                  pricingLinks: res.pricingSchemeLinkContainers,
                  isReturn: this.isReturn,
                  isTradeIn: this.isTradeIn,
                  roundingItemCode: this.roundingItemCode,
                  eanCode: item.bl_fi_mst_item_hdr.scan_code,
                  employeeDiscount:this.employeeDiscount,
                  isMember: this.isMember,
                  selectedIndex: (item.bl_fi_mst_item_hdr.txn_type==='MADE_TO_ORDER'?1:0),
                  stockBalance: stockBalance,
                  defaultPricingList: this.defaultPricingList,
                  category1_code: res.category1_code,
                  category2_code: res.category2_code,
                  category3_code: res.category3_code,
                  category4_code: res.category4_code,
                  category5_code: res.category5_code,
                  category6_code: res.category6_code,
                  category7_code: res.category7_code,
                  category8_code: res.category8_code,
                  category9_code: res.category9_code,
                  category10_code: res.category10_code,
                });

            if(this.posTheme==='TOP_BOTTOM'){
              this.viewColFacadeMain.onNextAndReset(this.mode, 31);
            }else{
              this.viewColFacade.onNextAndReset(11,4);
            }
          }
      }
    }
  }

  onMorePage() {
    if (this.rowData.length < this.totalRecords) {
      if(this.offlineMode){
        this.createDataOffline();
      }else{
        this.createData();
      }

    }
  }


  onReturn() {
    if(this.posTheme==='TOP_BOTTOM'){
      this.viewColFacadeMain.onPrev(0);
    }else{
      this.viewColFacade.onPrev(0);
    }

  }

  ngOnDestroy() {
    this.subs.unsubscribe();
  }
}
